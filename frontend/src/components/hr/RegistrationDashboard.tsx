import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  TrendingUp,
  Calendar,
  Building2,
  Mail,
  Eye
} from 'lucide-react'
import { apiClient } from '@/services/api'

interface RegistrationOverview {
  period: {
    start_date: string
    end_date: string
    days: number
  }
  counts: {
    total_registrations: number
    pending_approvals: number
    approved_pending_activation: number
    completed_activations: number
    rejected_activations: number
  }
  rates: {
    completion_rate: number
    rejection_rate: number
    approval_rate: number
  }
  processing_times: {
    avg_approval_time_hours: number | null
    avg_activation_time_hours: number | null
  }
}

interface PendingApproval {
  activation_id: number
  employee_id: string
  employee_name: string
  employee_name_ar: string
  email: string
  position: string
  position_ar: string
  department: string | null
  department_ar: string | null
  created_by: string | null
  created_at: string
  waiting_time_hours: number
  waiting_time_days: number
  is_urgent: boolean
}

interface RegistrationAnalytics {
  overview: RegistrationOverview
  pending_approvals: PendingApproval[]
  trends: any
  department_breakdown: any[]
  generated_at: string
}

interface RegistrationDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    registrationDashboard: 'لوحة تحكم التسجيل',
    overview: 'نظرة عامة',
    pendingApprovals: 'الموافقات المعلقة',
    totalRegistrations: 'إجمالي التسجيلات',
    pendingApproval: 'في انتظار الموافقة',
    completedActivations: 'التفعيلات المكتملة',
    rejectedApplications: 'الطلبات المرفوضة',
    completionRate: 'معدل الإكمال',
    approvalRate: 'معدل الموافقة',
    avgApprovalTime: 'متوسط وقت الموافقة',
    avgActivationTime: 'متوسط وقت التفعيل',
    hours: 'ساعة',
    days: 'أيام',
    urgent: 'عاجل',
    approve: 'موافقة',
    viewDetails: 'عرض التفاصيل',
    waitingTime: 'وقت الانتظار',
    createdBy: 'أنشئ بواسطة',
    department: 'القسم',
    position: 'المنصب',
    email: 'البريد الإلكتروني',
    employeeId: 'رقم الموظف',
    loading: 'جاري التحميل...',
    error: 'حدث خطأ في تحميل البيانات',
    refresh: 'تحديث',
    noData: 'لا توجد بيانات',
    approveSuccess: 'تم الموافقة على الموظف بنجاح',
    approveError: 'فشل في الموافقة على الموظف'
  },
  en: {
    registrationDashboard: 'Registration Dashboard',
    overview: 'Overview',
    pendingApprovals: 'Pending Approvals',
    totalRegistrations: 'Total Registrations',
    pendingApproval: 'Pending Approval',
    completedActivations: 'Completed Activations',
    rejectedApplications: 'Rejected Applications',
    completionRate: 'Completion Rate',
    approvalRate: 'Approval Rate',
    avgApprovalTime: 'Avg Approval Time',
    avgActivationTime: 'Avg Activation Time',
    hours: 'hours',
    days: 'days',
    urgent: 'Urgent',
    approve: 'Approve',
    viewDetails: 'View Details',
    waitingTime: 'Waiting Time',
    createdBy: 'Created By',
    department: 'Department',
    position: 'Position',
    email: 'Email',
    employeeId: 'Employee ID',
    loading: 'Loading...',
    error: 'Error loading data',
    refresh: 'Refresh',
    noData: 'No data available',
    approveSuccess: 'Employee approved successfully',
    approveError: 'Failed to approve employee'
  }
}
export default function RegistrationDashboard({ language }: RegistrationDashboardProps): React.ReactElement {
  const [analytics, setAnalytics] = useState<RegistrationAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [approvingIds, setApprovingIds] = useState<Set<number>>(new Set())

  const t = translations[language]
  const isRTL = language === 'ar'
  useEffect(() => {
    loadAnalytics()
  }, [])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.get('/auth/registration-analytics/')
      setAnalytics(response.data)
    } catch (error) {
      console.error('Error loading registration analytics:', error)
      setError(t.error)
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (employeeId: number) => {
    try {
      setApprovingIds(prev => new Setprev.add(employeeId))
      
      await apiClient.post(`/employees/${employeeId}/approve/`)
      
      // Show success message
      alert(t.approveSuccess)
      
      // Reload analytics to update the dashboard
      await loadAnalytics()
    } catch (error) {
      console.error('Error approving employee:', error)
      alert(t.approveError)
    } finally {
      setApprovingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(employeeId)
        return newSet
      })
    }
  }

  if (loading) {
    return (<div className="flex items-center justify-center h-64">
        <div className="text-white">{t.loading}</div>
      </div>
    )
  }

  if (error || !analytics) {
    return (<div className="flex flex-col items-center justify-center h-64 space-y-4">
        <div className="text-red-400">{error || t.error}</div>
        <Button onClick={loadAnalytics} variant="outline">
          {t.refresh}
        </Button>
      </div>
    )
  }

  const { overview, pending_approvals } = analytics

  return (<div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-white">{t.registrationDashboard}</h1>
        <Button onClick={loadAnalytics} variant="outline" size="sm">
          {t.refresh}
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="glass-card border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">
              {t.totalRegistrations}
            </CardTitle>
            <Users className="h-4 w-4 text-blue-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {overview.counts.total_registrations}
            </div>
            <p className="text-xs text-white/70">
              {t.completionRate}: {overview.rates.completion_rate}%
            </p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">
              {t.pendingApproval}
            </CardTitle>
            <Clock className="h-4 w-4 text-yellow-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {overview.counts.pending_approvals}
            </div>
            <p className="text-xs text-white/70">
              {t.approvalRate}: {overview.rates.approval_rate}%
            </p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">
              {t.completedActivations}
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {overview.counts.completed_activations}
            </div>
            <p className="text-xs text-white/70">
              {overview.processing_times.avg_activation_time_hours 
                ? `${overview.processing_times.avg_activation_time_hours} ${t.hours}`
                : t.noData
              }
            </p>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-white">
              {t.rejectedApplications}
            </CardTitle>
            <XCircle className="h-4 w-4 text-red-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {overview.counts.rejected_activations}
            </div>
            <p className="text-xs text-white/70">
              {overview.rates.rejection_rate}%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Approvals */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-400" />
            {t.pendingApprovals}
          </CardTitle>
          <CardDescription className="text-white/70">
            {pending_approvals.length} employees waiting for approval
          </CardDescription>
        </CardHeader>
        <CardContent>
          {pending_approvals.length === 0 ? (<div className="text-center py-8 text-white/70">
              {t.noData}
            </div>
          ) : (<div className="space-y-4">
              {pending_approvals.map((approval) => (<div
                  key={approval.activation_id}
                  className="flex items-center justify-between p-4 bg-white/5 rounded-lg border border-white/10"
                >
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-white">
                        {language === 'ar' ? approval.employee_name_ar : approval.employee_name}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {approval.employee_id}
                      </Badge>
                      {approval.is_urgent && (<Badge variant="destructive" className="text-xs">
                          {t.urgent}
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-white/70 space-y-1">
                      <div className="flex items-center gap-2">
                        <Mail className="h-3 w-3" />
                        {approval.email}
                      </div>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-3 w-3" />
                        {language === 'ar' ? approval.position_ar : approval.position}
                        {approval.department && (<span>
                            - {language === 'ar' ? approval.department_ar : approval.department}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3" />
                        {t.waitingTime}: {approval.waiting_time_days} {t.days}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleApprove(approval.activation_id)}
                      disabled={approvingIds.has(approval.activation_id)}
                    >
                      {approvingIds.has(approval.activation_id) ? t.loading : t.approve}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
