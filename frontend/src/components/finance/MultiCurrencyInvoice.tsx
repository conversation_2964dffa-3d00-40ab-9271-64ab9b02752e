import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowRightLeft, 
  Calculator,
  Globe,
  Receipt,
  TrendingUp
} from 'lucide-react';
// Language prop will be passed from parent component

interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  decimal_places: number;
  symbol_position: 'before' | 'after';
  is_base_currency: boolean;
}

interface InvoiceData {
  currency: string;
  exchange_rate: number;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  // Base currency amounts
  subtotal_base: number;
  tax_amount_base: number;
  discount_amount_base: number;
  total_amount_base: number;
}

interface MultiCurrencyInvoiceProps {
  language: string;
  invoiceData?: InvoiceData;
  onCurrencyChange?: (currency: string, exchangeRate: number) => void;
  readOnly?: boolean;
}
const MultiCurrencyInvoice: React.FC<MultiCurrencyInvoiceProps> = ({
  language,
  invoiceData,
  onCurrencyChange,
  readOnly = false
}) => {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency | null>(null);
  const [baseCurrency, setBaseCurrency] = useState<Currency | null>(null);
  const [exchangeRate, setExchangeRate] = useState<number>(1);
  const [loading, setLoading] = useState(true);

  // Invoice amounts
  const [amounts, setAmounts] = useState({
    subtotal: invoiceData?.subtotal || 1000,
    tax_amount: invoiceData?.tax_amount || 150,
    discount_amount: invoiceData?.discount_amount || 50,
    total_amount: invoiceData?.total_amount || 1100
  });
  useEffect(() => {
    fetchCurrencies();
  }, []);
  useEffect(() => {
    if (invoiceData?.currency && currencies.length > 0) {
      const currency: any = currencies.find(c => c.code === invoiceData.currency);
      if (currency) {
        setSelectedCurrency(currency);
        setExchangeRate(invoiceData.exchange_rate);
      }
    }
  }, [invoiceData, currencies]);
  const fetchCurrencies: any = async () => {
    try {
      const response = await fetch('/api/currencies/active/');
      if (response.ok) {
        const data: any = await response.json();
        setCurrencies(data);
        const base: any = data.find((c: Currency) => c.is_base_currency);
        setBaseCurrency(base);
        
        if (!invoiceData?.currency && base) {
          setSelectedCurrency(base);
        }
      }
    } catch (error) {
      console.error('Error fetching currencies:', error);
    } finally {
      setLoading(false);
    }
  };
  const fetchExchangeRate: any = async (fromCurrency: string, toCurrency: string) => {
    if (fromCurrency === toCurrency) {
      setExchangeRate(1);
      return;
    }

    try {
      const response: any = await fetch(`/api/exchange-rates/get_rate/?from_currency=${fromCurrency}&to_currency=${toCurrency}`);
      
      if (response.ok) {
        const data: any = await response.json();
        setExchangeRate(data.rate);
        
        if (onCurrencyChange) {
          onCurrencyChange(fromCurrency, data.rate);
        }
      }
    } catch (error) {
      console.error('Error fetching exchange rate:', error);
      setExchangeRate(1);
    }
  };
  const handleCurrencyChange: any = (currencyCode: string): void => {
    const currency = currencies.find(c => c.code === currencyCode);
    if (currency && baseCurrency) {
      setSelectedCurrency(currency);
      fetchExchangeRate(currency.code, baseCurrency.code);
    }
  };
  const formatCurrency: any = (amount: number, currency: Currency): string => {
    const formatted = amount.toFixed(currency.decimal_places);
    
    if (currency.symbol_position === 'before') {
      return `${currency.symbol}${formatted}`;
    } else {
      return `${formatted} ${currency.symbol}`;
    }
  };
  const calculateBaseAmount: any = (amount: number): void => {
    return amount * exchangeRate;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-sm text-muted-foreground">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading currencies...'}
        </div>
      </div>
    );
  }

  return (<Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="h-5 w-5" />
          {language === 'ar' ? 'معلومات العملة' : 'Currency Information'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Currency Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label>{language === 'ar' ? 'عملة الفاتورة' : 'Invoice Currency'}</Label>
            <Select 
              value={selectedCurrency?.code || ''} 
              onValueChange={handleCurrencyChange}
              disabled={readOnly}
            >
              <SelectTrigger>
                <SelectValue placeholder={language === 'ar' ? 'اختر العملة' : 'Select currency'} />
              </SelectTrigger>
              <SelectContent>
                {currencies.map(currency => (
                  <SelectItem key={currency.id} value={currency.code}>
                    <div className="flex items-center gap-2">
                      <span>{currency.symbol}</span>
                      <span>{currency.code}</span>
                      <span className="text-muted-foreground">- {currency.name}</span>
                      {currency.is_base_currency && (
                        <Badge variant="outline" className="text-xs">
                          {language === 'ar' ? 'أساسية' : 'Base'}
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>{language === 'ar' ? 'سعر الصرف' : 'Exchange Rate'}</Label>
            <div className="flex items-center gap-2">
              <Input
                type="number"
                step="(0).000001"
                value={exchangeRate}
                onChange={(e: any) => setExchangeRate(parseFloat(e.target.value) || 1)}
                disabled={readOnly || selectedCurrency?.is_base_currency}
                className="flex-1"
              />
              {selectedCurrency && baseCurrency && !selectedCurrency.is_base_currency && (<div className="text-xs text-muted-foreground whitespace-nowrap">
                  1 {selectedCurrency.code} = {exchangeRate.toFixed(6)} {baseCurrency.code}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Exchange Rate Info */}
        {selectedCurrency && baseCurrency && !selectedCurrency.is_base_currency && (<div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ArrowRightLeft className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {language === 'ar' ? 'تحويل العملة' : 'Currency Conversion'}
                </span>
              </div>
              <Badge variant="outline">
                {selectedCurrency.code} → {baseCurrency.code}
              </Badge>
            </div>
            <div className="mt-2 text-sm text-muted-foreground">
              {language === 'ar' 
                ? `سعر الصرف: 1 ${selectedCurrency.code} = ${exchangeRate.toFixed(6)} ${baseCurrency.code}`
                : `Exchange Rate: 1 ${selectedCurrency.code} = ${exchangeRate.toFixed(6)} ${baseCurrency.code}`
              }
            </div>
          </div>
        )}

        {/* Invoice Amounts */}
        <div className="space-y-4">
          <h4 className="font-medium flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            {language === 'ar' ? 'مبالغ الفاتورة' : 'Invoice Amounts'}
          </h4>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Invoice Currency Column */}
            <div className="space-y-3">
              <div className="flex items-center gap-2 mb-3">
                <Badge variant="default">
                  {selectedCurrency?.code || 'N/A'}
                </Badge>
                <span className="text-sm font-medium">
                  {language === 'ar' ? 'عملة الفاتورة' : 'Invoice Currency'}
                </span>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">{language === 'ar' ? 'المجموع الفرعي:' : 'Subtotal:'}</span>
                  <span className="font-medium">
                    {selectedCurrency ? formatCurrency(amounts.subtotal, selectedCurrency) : amounts.subtotal}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">{language === 'ar' ? 'الضريبة:' : 'Tax:'}</span>
                  <span className="font-medium">
                    {selectedCurrency ? formatCurrency(amounts.tax_amount, selectedCurrency) : amounts.tax_amount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">{language === 'ar' ? 'الخصم:' : 'Discount:'}</span>
                  <span className="font-medium text-red-600">
                    -{selectedCurrency ? formatCurrency(amounts.discount_amount, selectedCurrency) : amounts.discount_amount}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold">
                  <span>{language === 'ar' ? 'الإجمالي:' : 'Total:'}</span>
                  <span>
                    {selectedCurrency ? formatCurrency(amounts.total_amount, selectedCurrency) : amounts.total_amount}
                  </span>
                </div>
              </div>
            </div>

            {/* Base Currency Column */}
            {selectedCurrency && baseCurrency && !selectedCurrency.is_base_currency && (<div className="space-y-3">
                <div className="flex items-center gap-2 mb-3">
                  <Badge variant="outline">
                    {baseCurrency.code}
                  </Badge>
                  <span className="text-sm font-medium">
                    {language === 'ar' ? 'العملة الأساسية' : 'Base Currency'}
                  </span>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">{language === 'ar' ? 'المجموع الفرعي:' : 'Subtotal:'}</span>
                    <span className="font-medium">
                      {formatCurrency(calculateBaseAmount(amounts.subtotal), baseCurrency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">{language === 'ar' ? 'الضريبة:' : 'Tax:'}</span>
                    <span className="font-medium">
                      {formatCurrency(calculateBaseAmount(amounts.tax_amount), baseCurrency)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">{language === 'ar' ? 'الخصم:' : 'Discount:'}</span>
                    <span className="font-medium text-red-600">
                      -{formatCurrency(calculateBaseAmount(amounts.discount_amount), baseCurrency)}
                    </span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-bold">
                    <span>{language === 'ar' ? 'الإجمالي:' : 'Total:'}</span>
                    <span>
                      {formatCurrency(calculateBaseAmount(amounts.total_amount), baseCurrency)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Currency Conversion Summary */}
        {selectedCurrency && baseCurrency && !selectedCurrency.is_base_currency && (<div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-2">
              <Calculator className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                {language === 'ar' ? 'ملخص التحويل' : 'Conversion Summary'}
              </span>
            </div>
            <div className="text-sm text-blue-800 dark:text-blue-200">
              {language === 'ar' 
                ? `إجمالي الفاتورة: ${formatCurrency(amounts.total_amount, selectedCurrency)} = ${formatCurrency(calculateBaseAmount(amounts.total_amount), baseCurrency)}`
                : `Invoice Total: ${formatCurrency(amounts.total_amount, selectedCurrency)} = ${formatCurrency(calculateBaseAmount(amounts.total_amount), baseCurrency)}`
              }
            </div>
            <div className="text-xs text-blue-600 dark:text-blue-300 mt-1">
              {language === 'ar' 
                ? `بسعر صرف: 1 ${selectedCurrency.code} = ${exchangeRate.toFixed(6)} ${baseCurrency.code}`
                : `At exchange rate: 1 ${selectedCurrency.code} = ${exchangeRate.toFixed(6)} ${baseCurrency.code}`
              }
            </div>
          </div>
        )}

        {/* Quick Actions */}
        {!readOnly && (
          <div className="flex gap-2 pt-4 border-t">
            <Button variant="outline" size="sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'تحديث السعر' : 'Update Rate'}
            </Button>
            <Button variant="outline" size="sm">
              <Calculator className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'حاسبة العملة' : 'Currency Calculator'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MultiCurrencyInvoice;
