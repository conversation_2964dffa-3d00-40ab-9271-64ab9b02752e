/**
 * Accessibility Enhancer Component
 * Fixes common accessibility issues and provides better UX patterns
 */

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { createPortal } from 'react-dom'

// Focus trap utility for modals
export const useFocusTrap: any = (isActive: boolean): void => {
  const containerRef = useRef<HTMLDivElement>(null)
  const previousActiveElement = useRef<HTMLElement | null>(null)

  useEffect(() => {
    if (!isActive) return

    const container = containerRef.current
    if (!container) return

    // Store the previously focused element
    previousActiveElement.current = document.activeElement as HTMLElement

    // Get all focusable elements
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement

    // Focus the first element
    firstElement?.focus()

    const handleTabKey = (e: KeyboardEvent): void => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          e.preventDefault()
          lastElement?.focus()
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          e.preventDefault()
          firstElement?.focus()
        }
      }
    }

    const handleEscapeKey = (e: KeyboardEvent): void => {
      if (e.key === 'Escape') {
        // Let parent handle escape
        e.stopPropagation()
      }
    }

    document.addEventListener('keydown', handleTabKey)
    document.addEventListener('keydown', handleEscapeKey)

    return () => {
      document.removeEventListener('keydown', handleTabKey)
      document.removeEventListener('keydown', handleEscapeKey)
      
      // Restore focus to previously active element
      if (previousActiveElement.current) {
        previousActiveElement.current.focus()
      }
    }
  }, [isActive])

  return containerRef
}

// Skip link component for keyboard navigation
export const SkipLink: React.FC<{ href: string; children: React.ReactNode }> = ({ 
  href, 
  children 
}) => {
  return (
    <a
      href={href as React.RefObject<HTMLDivElement>}
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded focus:shadow-lg"
      onFocus={(e) => {
        // Ensure skip link is visible when focused
        e.currentTarget.classList.remove('sr-only')
       // @ts-ignore}}
      onBlur={(e) => {
        // Hide skip link when not focused
        e.currentTarget.classList.add('sr-only')
       // @ts-ignore}}
    >
      {children}
    </a>
  )
}

// Accessible modal wrapper
export const AccessibleModal: React.FC<{
  isOpen: boolean
  onClose: () => void
  title: string
  description?: string
  children: React.ReactNode
  className?: string
}> = ({ isOpen, onClose, title, description, children, className = '' }) => {
  const focusTrapRef = useFocusTrap(isOpen)
  const [mounted, setMounted] = useState<boolean>(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll
      document.body.style.overflow = 'hidden'
      
      // Note: Use React components with aria-live instead of DOM manipulation
    } else {
      // Restore body scroll
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [isOpen, title])

  if (!mounted || !isOpen) return null

  return createPortal(
    <div
      className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      aria-describedby={description ? "modal-description" : undefined}
      onClick={(e: any) => {
        if (e.target === e.currentTarget) {
          onClose()
         // @ts-ignore}
      }}
    >
      <div className="flex items-center justify-center min-h-screen p-4">
        <div
          ref={focusTrapRef as React.RefObject<HTMLDivElement>}
          className={`bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto ${className}`}
          onClick={(e: any) => e.stopPropagation()}
        >
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 id="modal-title" className="text-xl font-semibold text-white">
                {title}
              </h2>
              <button
                onClick={onClose}
                className="text-white/60 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/20 rounded p-1"
                aria-label="Close modal"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {description && (
              <p id="modal-description" className="text-white/70 mb-4">
                {description}
              </p>
            )}
            
            {children}
          </div>
        </div>
      </div>
    </div>,
    document.body
  )
}

// Loading skeleton with proper dimensions to prevent layout shift
export const AccessibleSkeleton: React.FC<{
  rows?: number
  height?: string
  className?: string
  ariaLabel?: string
}> = ({ rows = 3, height = '1rem', className = '', ariaLabel = 'Loading content' }) => {
  return (
    <div 
      role="status" 
      aria-label={ariaLabel}
      className={`animate-pulse ${className}`}
    >
      {Array.from({ length: rows }).map((_, index) => (
        <div
          key={index}
          className="bg-gray-200 rounded mb-2 last:mb-0"
          style={{ height, width: index === rows - 1 ? '75%' : '100%'}}
        />
      ))}
      <span className="sr-only">{ariaLabel}</span>
    </div>
  )
}

// Accessible form field wrapper
export const AccessibleFormField: React.FC<{
  label: string
  error?: string
  required?: boolean
  children: React.ReactNode
  className?: string
}> = ({ label, error, required, children, className = '' }) => {
  const fieldId = useRef(`field-${Math.random().toString(36).substr(2, 9)}`)
  const errorId = useRef(`error-${Math.random().toString(36).substr(2, 9)}`)

  return (
    <div className={`space-y-2 ${className}`}>
      <label 
        htmlFor={fieldId.current}
        className="block text-sm font-medium text-white"
      >
        {label}
        {required && (
          <span className="text-red-400 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      <div>
        {React.cloneElement(children as React.ReactElement, {
          id: fieldId.current,
          'aria-describedby': error ? errorId.current : undefined,
          'aria-invalid': error ? 'true' : 'false',
          'aria-required': required ? 'true' : 'false'
        })}
      </div>
      
      {error && (
        <div
          id={errorId.current}
          role="alert"
          className="text-red-400 text-sm"
        >
          {error}
        </div>
      )}
    </div>
  )
}

// Accessible button with loading state
export const AccessibleButton: React.FC<{
  children: React.ReactNode
  onClick?: () => void
  loading?: boolean
  disabled?: boolean
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  ariaLabel?: string
  type?: 'button' | 'submit' | 'reset'
}> = ({ 
  children, 
  onClick, 
  loading = false, 
  disabled = false, 
  variant = 'primary',
  size = 'md',
  className = '',
  ariaLabel,
  type = 'button'
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors'
  
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',
    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      aria-label={ariaLabel}
      aria-busy={loading}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className} ${
        (disabled || loading) ? 'opacity-50 cursor-not-allowed' : ''
      }`}
    >
      {loading && (
        <svg 
          className="animate-spin -ml-1 mr-2 h-4 w-4" 
          fill="none" 
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {children}
    </button>
  )
}

export default {
  useFocusTrap,
  SkipLink,
  AccessibleModal,
  AccessibleSkeleton,
  AccessibleFormField,
  AccessibleButton
}
