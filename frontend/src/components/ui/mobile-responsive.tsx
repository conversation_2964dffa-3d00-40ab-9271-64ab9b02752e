import React from 'react'
import { <PERSON><PERSON>, ButtonProps } from './button'
import { <PERSON>, CardContent, CardHeader } from './card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@radix-ui/react-collapsible'
import { ChevronDown } from 'lucide-react'
import { cn } from '@/lib/utils'

// TouchButton - Enhanced button for mobile interactions
export interface TouchButtonProps extends ButtonProps {
  children: React.ReactNode
}

export const TouchButton = React.forwardRef<HTMLButtonElement, TouchButtonProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        className={cn(
          "touch-manipulation select-none",
          "active:scale-95 transition-transform duration-150",
          "min-h-[44px] min-w-[44px]", // Minimum touch target size
          className
        )}
        {...props}
      >
        {children}
      </Button>
    )
  }
)
TouchButton.displayName = "TouchButton"

// MobileTable - Responsive table component for mobile devices
export interface MobileTableProps {
  children: React.ReactNode
  className?: string
}

export const MobileTable = React.forwardRef<HTMLDivElement, MobileTableProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "w-full overflow-hidden",
          "md:overflow-x-auto", // Allow horizontal scroll on larger screens
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileTable.displayName = "MobileTable"

// CollapsibleSection - Collapsible content section for mobile layouts
export interface CollapsibleSectionProps {
  title: string
  children: React.ReactNode
  defaultOpen?: boolean
  className?: string
  headerClassName?: string
  contentClassName?: string
}

export const CollapsibleSection = React.forwardRef<HTMLDivElement, CollapsibleSectionProps>(
  ({ 
    title, 
    children, 
    defaultOpen = false, 
    className, 
    headerClassName, 
    contentClassName,
    ...props 
  }, ref) => {
    const [isOpen, setIsOpen] = React.useState(defaultOpen)

    return (
      <Card ref={ref} className={cn("w-full", className)} {...props}>
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader 
              className={cn(
                "cursor-pointer hover:bg-muted/50 transition-colors",
                "flex flex-row items-center justify-between space-y-0 pb-2",
                headerClassName
              )}
            >
              <h3 className="text-sm font-medium">{title}</h3>
              <ChevronDown 
                className={cn(
                  "h-4 w-4 transition-transform duration-200",
                  isOpen && "transform rotate-180"
                )}
              />
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className={cn("pt-0", contentClassName)}>
              {children}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    )
  }
)
CollapsibleSection.displayName = "CollapsibleSection"

// Hook for detecting mobile devices
export const useIsMobile = () => {
  const [isMobile, setIsMobile] = React.useState(false)

  React.useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)

    return () => window.removeEventListener('resize', checkIsMobile)
  }, [])

  return isMobile
}

// Mobile-optimized table row component
export interface MobileTableRowProps {
  children: React.ReactNode
  className?: string
  onClick?: () => void
}

export const MobileTableRow = React.forwardRef<HTMLDivElement, MobileTableRowProps>(
  ({ className, children, onClick, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "border-b border-border/40 p-4",
          "hover:bg-muted/50 transition-colors",
          onClick && "cursor-pointer",
          className
        )}
        onClick={onClick}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileTableRow.displayName = "MobileTableRow"

// Mobile-optimized table cell component
export interface MobileTableCellProps {
  label?: string
  children: React.ReactNode
  className?: string
}

export const MobileTableCell = React.forwardRef<HTMLDivElement, MobileTableCellProps>(
  ({ label, className, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col space-y-1",
          "sm:flex-row sm:items-center sm:justify-between sm:space-y-0",
          className
        )}
        {...props}
      >
        {label && (
          <span className="text-sm font-medium text-muted-foreground">
            {label}:
          </span>
        )}
        <div className="text-sm">{children}</div>
      </div>
    )
  }
)
MobileTableCell.displayName = "MobileTableCell"
