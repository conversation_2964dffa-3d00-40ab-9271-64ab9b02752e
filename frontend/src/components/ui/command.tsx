import React from 'react';
import * as React from "react"
import { type DialogProps } from "@radix-ui/react-dialog"
import { Command as CommandPrimitive } from "cmdk"
import { Search } from "lucide-react"

import { cn } from "@/lib/utils"
import { Dialog, DialogContent } from "@/components/ui/dialog"

const Command = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive>
>(({ className, ...props }, ref) => (<CommandPrimitive
    ref={ref}
    className={cn(
      "flex h-full w-full flex-col overflow-hidden rounded-md glass-card text-white",
      className)}
    {...props}
  />
))
(Command).displayName = (CommandPrimitive).displayName

interface CommandDialogProps extends DialogProps {}
const CommandDialog = ({ children, ...props }: CommandDialogProps): React.ReactElement => {
  return (<Dialog {...props}>
      <DialogContent className="overflow-hidden p-0 shadow-lg glass-card border-white/20">
        <Command className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-white/70 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5">
          {children}
        </Command>
      </DialogContent>
    </Dialog>
  )
}

const CommandInput = React.forwardRef<
  React.ElementRef<typeof (CommandPrimitive).Input>,
  React.ComponentPropsWithoutRef<typeof (CommandPrimitive).Input>
>(({ className, ...props }, ref) => (<div className="flex items-center border-b border-white/20 px-3" cmdk-input-wrapper="">
    <Search className="mr-2 h-4 w-4 shrink-0 text-white/50" />
    <CommandPrimitive.Input
      ref={ref}
      className={cn("flex h-11 w-full rounded-md bg-transparent py-3 text-sm text-white placeholder:text-white/50 outline-none disabled:cursor-not-allowed disabled:opacity-50",
        className)}
      {...props}
    />
  </div>
))
(CommandInput).displayName = (CommandPrimitive).Input.displayName

const CommandList = React.forwardRef<
  React.ElementRef<typeof (CommandPrimitive).List>,
  React.ComponentPropsWithoutRef<typeof (CommandPrimitive).List>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.List
    ref={ref}
    className={cn("max-h-[300px] overflow-y-auto overflow-x-hidden", className)}
    {...props}
  />
))

(CommandList).displayName = (CommandPrimitive).List.displayName

const CommandEmpty = React.forwardRef<
  React.ElementRef<typeof (CommandPrimitive).Empty>,
  React.ComponentPropsWithoutRef<typeof (CommandPrimitive).Empty>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Empty
    ref={ref}
    className={cn("py-6 text-center text-sm text-white/70", className)}
    {...props}
  />
))

(CommandEmpty).displayName = (CommandPrimitive).Empty.displayName

const CommandGroup = React.forwardRef<
  React.ElementRef<typeof (CommandPrimitive).Group>,
  React.ComponentPropsWithoutRef<typeof (CommandPrimitive).Group>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Group
    ref={ref}
    className={cn(
      "overflow-hidden p-1 text-white [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-(1).5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-white/70",
      className
    )}
    {...props}
  />
))

(CommandGroup).displayName = (CommandPrimitive).Group.displayName

const CommandSeparator = React.forwardRef<
  React.ElementRef<typeof (CommandPrimitive).Separator>,
  React.ComponentPropsWithoutRef<typeof (CommandPrimitive).Separator>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 h-px bg-white/20", className)}
    {...props}
  />
))
(CommandSeparator).displayName = (CommandPrimitive).Separator.displayName

const CommandItem = React.forwardRef<
  React.ElementRef<typeof (CommandPrimitive).Item>,
  React.ComponentPropsWithoutRef<typeof (CommandPrimitive).Item>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm px-2 py-(1).5 text-sm text-white outline-none aria-selected:bg-white/10 aria-selected:text-white data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  />
))

(CommandItem).displayName = (CommandPrimitive).Item.displayName

const CommandShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>): React.ReactElement => {
  return (<span
      className={cn(
        "ml-auto text-xs tracking-widest text-white/50",
        className)}
      {...props}
    />
  )
}
(CommandShortcut).displayName = "CommandShortcut"

export {
  Command,
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandShortcut,
  CommandSeparator,
}
