import { ReactNode } from 'react'
import { useSelector } from 'react-redux'
import { Navigate } from 'react-router-dom'
import type { RootState } from '../store'

interface RoleBasedRouteProps {
  children: ReactNode
  requiredPermissions?: string[]
  requiredRole?: string
  minRoleLevel?: number
  fallbackPath?: string
}

export default function RoleBasedRoute({
  children,
  requiredPermissions = [],
  requiredRole,
  minRoleLevel,
  fallbackPath = '/unauthorized'
}: RoleBasedRouteProps) {
  const { user, isAuthenticated } = useSelector((state: RootState => state.auth)

  // If not authenticated, redirect to login
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />
  }

  // Check role-based access - allow super_admin to access all routes
  if (requiredRole && user.role.id !== requiredRole && user.role.id !== 'super_admin') {
    return <Navigate to={fallbackPath} replace />
  }

  // Check minimum role level - lower numbers = higher privileges
  if (minRoleLevel && user.role.level > minRoleLevel) {
    return <Navigate to={fallbackPath} replace />
  }

  // Check specific permissions
  if (requiredPermissions.length > 0) {
    const hasPermission = requiredPermissions.every(permission => {
      // Check for wildcard permissions
      if (user.permissions.some(p => p.module === '*' && p.actions.includes('*'))) {
        return true
      }

      // Check specific permissions
      const [module, action] = permission.split('.')
      return user.permissions.some(p => {
        if (p.module === module || p.module === '*') {
          return p.actions.includes(action) || p.actions.includes('*')
        }
        return false
      })
    })

    if (!hasPermission) {
      return <Navigate to={fallbackPath} replace />
    }
  }

  return <>{children}</>
}

// Hook for checking permissions in components
export function usePermissions() {
  const { user } = useSelector((state: RootState => state.auth)

  const hasPermission = (permission: string): boolean => {
    if (!user) return false

    // Check for wildcard permissions
    if (user.permissions.some(p => p.module === '*' && p.actions.includes('*'))) {
      return true
    }

    // Check specific permissions
    const [module, action] = permission.split('.')
    return user.permissions.some(p => {
      if (p.module === module || p.module === '*') {
        return p.actions.includes(action) || p.actions.includes('*')
      }
      return false
    })
  }

  const hasRole = (roleId: string): boolean => {
    return user?.role.id === roleId
  }

  const hasMinRoleLevel = (level: number): boolean => {
    return user ? user.role.level <= level : false
  }

  const canAccess = (module: string, action: string): boolean => {
    return hasPermission(`${module}.${action}`)
  }

  return {
    hasPermission,
    hasRole,
    hasMinRoleLevel,
    canAccess,
    user,
    userRole: user?.role
  }
}

// Component for conditional rendering based on permissions
interface PermissionGateProps {
  children: ReactNode
  permission?: string
  role?: string
  minRoleLevel?: number
  fallback?: ReactNode
}

export function PermissionGate({
  children,
  permission,
  role,
  minRoleLevel,
  fallback = null
}: PermissionGateProps) {
  const { hasPermission, hasRole, hasMinRoleLevel } = usePermissions()

  let hasAccess = true

  if (permission && !hasPermission(permission)) {
    hasAccess = false
  }

  if (role && !hasRole(role)) {
    hasAccess = false
  }

  if (minRoleLevel && !hasMinRoleLevel(minRoleLevel)) {
    hasAccess = false
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>
}
