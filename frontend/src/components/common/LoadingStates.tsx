/**
 * Comprehensive Loading States Component
 * Provides various loading indicators with accessibility support
 */

import React from 'react'
import { Loader2, RefreshCw, Download, Upload, Search, Save } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white' | 'gray'
  className?: string
}

interface LoadingStateProps {
  type?: 'spinner' | 'dots' | 'pulse' | 'skeleton'
  message?: string
  language?: 'ar' | 'en'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fullScreen?: boolean
  overlay?: boolean
  className?: string
}

interface ActionLoadingProps {
  action: 'saving' | 'loading' | 'uploading' | 'downloading' | 'searching' | 'refreshing'
  message?: string
  language?: 'ar' | 'en'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const translations = {
  ar: {
    loading: 'جاري التحميل...',
    saving: 'جاري الحفظ...',
    uploading: 'جاري الرفع...',
    downloading: 'جاري التحميل...',
    searching: 'جاري البحث...',
    refreshing: 'جاري التحديث...',
    pleaseWait: 'يرجى الانتظار'
  },
  en: {
    loading: 'Loading...',
    saving: 'Saving...',
    uploading: 'Uploading...',
    downloading: 'Downloading...',
    searching: 'Searching...',
    refreshing: 'Refreshing...',
    pleaseWait: 'Please wait'
  }
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
}

const colorClasses = {
  primary: 'text-blue-500',
  secondary: 'text-purple-500',
  white: 'text-white',
  gray: 'text-gray-500'
}

// Basic loading spinner
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  return (
    <Loader2 
      className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]} ${className}`}
      aria-hidden="true"
    />
  )
}

// Dots loading animation
export const LoadingDots: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (<div className={`flex space-x-1 ${className}`} aria-hidden="true">
      <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '(0).1s' }}></div>
      <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '(0).2s' }}></div>
    </div>
  )
}

// Pulse loading animation
export const LoadingPulse: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`w-4 h-4 bg-current rounded-full animate-pulse ${className}`} aria-hidden="true"></div>
  )
}

// Skeleton loading for content
export const LoadingSkeleton: React.FC<{
  lines?: number
  className?: string
}> = ({ lines = 3, className = '' }) => {
  return (<div className={`space-y-3 ${className}`} aria-hidden="true">
      {Array.from({ length: lines }).map((_, index) => (
        <div key={index} className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-full"></div>
        </div>
      ))}
    </div>
  )
}

// Action-specific loading indicators
export const ActionLoading: React.FC<ActionLoadingProps> = ({
  action,
  message,
  language = 'en',
  size = 'md',
  className = ''
}) => {
  const t = translations[language]
  const getIcon = (): void => {
    switch (action) {
      case 'saving':
        return <Save className={`${sizeClasses[size]} animate-pulse`} />
      case 'uploading':
        return <Upload className={`${sizeClasses[size]} animate-bounce`} />
      case 'downloading':
        return <Download className={`${sizeClasses[size]} animate-bounce`} />
      case 'searching':
        return <Search className={`${sizeClasses[size]} animate-pulse`} />
      case 'refreshing':
        return <RefreshCw className={`${sizeClasses[size]} animate-spin`} />
      default:
        return <Loader2 className={`${sizeClasses[size]} animate-spin`} />
    }
  }
  const getMessage = (): void => {
    if (message) return message
    return t[action] || t.loading
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {getIcon()}
      <span className="text-sm">{getMessage()}</span>
    </div>
  )
}

// Main loading state component
export const LoadingState: React.FC<LoadingStateProps> = ({
  type = 'spinner',
  message,
  language = 'en',
  size = 'md',
  fullScreen = false,
  overlay = false,
  className = ''
}) => {
  const t = translations[language]
  const isRTL = language === 'ar'
  const renderLoadingIndicator = (): void => {
    switch (type) {
      case 'dots':
        return <LoadingDots className="text-current" />
      case 'pulse':
        return <LoadingPulse className="text-current" />
      case 'skeleton':
        return <LoadingSkeleton className="text-current" />
      default:
        return <LoadingSpinner size={size} color="white" />
    }
  }

  const content = (
    <div className={`flex flex-col items-center justify-center space-y-4 ${isRTL ? 'rtl' : 'ltr'} ${className}`}>
      {renderLoadingIndicator()}
      <div className="text-center">
        <p className="text-white text-lg font-medium">
          {message || t.loading}
        </p>
        <p className="text-white/70 text-sm mt-1">
          {t.pleaseWait}
        </p>
      </div>
    </div>
  )

  if (fullScreen) {
    return (<div 
        className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center z-50"
        role="status"
        aria-live="polite"
        aria-label={message || t.loading}
      >
        {content}
      </div>
    )
  }

  if (overlay) {
    return (<div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-40"
        role="status"
        aria-live="polite"
        aria-label={message || t.loading}
      >
        {content}
      </div>
    )
  }

  return (<div 
      className="flex items-center justify-center p-8"
      role="status"
      aria-live="polite"
      aria-label={message || t.loading}
    >
      {content}
    </div>
  )
}

// Button loading state
export const ButtonLoading: React.FC<{
  loading: boolean
  children: React.ReactNode
  loadingText?: string
  language?: 'ar' | 'en'
  className?: string
}> = ({ loading, children, loadingText, language = 'en', className = '' }) => {
  const t = translations[language]

  if (loading) {
    return (<div className={`flex items-center justify-center space-x-2 ${className}`}>
        <LoadingSpinner size="sm" color="white" />
        <span>{loadingText || t.loading}</span>
      </div>
    )
  }

  return <>{children}</>
}

// Table loading state
export const TableLoading: React.FC<{
  rows?: number
  columns?: number
  className?: string
}> = ({ rows = 5, columns = 4, className = '' }) => {
  return (<div className={`space-y-3 ${className}`} aria-hidden="true">
      {Array.from({ length: rows }).map((_, rowIndex) => (<div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="flex-1">
              <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      ))}
    </div>
  )
}

export default LoadingState
