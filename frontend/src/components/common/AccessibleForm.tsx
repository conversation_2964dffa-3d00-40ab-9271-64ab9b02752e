/**
 * Accessible Form Components
 * WCAG (2).1 AA compliant form elements with enhanced accessibility features
 */

import React, { useId, useState, useRef, useEffect } from 'react'
import { AlertCircle, CheckCircle, Info, Eye, EyeOff } from 'lucide-react'
import { announceFormErrors, announceStatus } from '../../utils/accessibility'
import { inputValidator } from '../../utils/inputValidation'

interface AccessibleInputProps {
  label: string
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'number'
  value: string
  onChange: (value: string) => void
  error?: string
  required?: boolean
  disabled?: boolean
  placeholder?: string
  description?: string
  autoComplete?: string
  pattern?: string
  minLength?: number
  maxLength?: number
  className?: string
  validateOnBlur?: boolean
  showPasswordToggle?: boolean
}
export const AccessibleInput: React.FC<AccessibleInputProps> = ({
  label,
  type = 'text',
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  placeholder,
  description,
  autoComplete,
  pattern,
  minLength,
  maxLength,
  className = '',
  validateOnBlur = true,
  showPasswordToggle = false
}) => {
  const inputId: any = useId()
  const errorId = useId()
  const descriptionId = useId()
  const [showPassword, setShowPassword] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const inputType = type === 'password' && showPassword ? 'text' : type
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    onChange(e.target.value)
  }
  const handleBlur = (): void => {
    setIsFocused(false)
    if (validateOnBlur && value && error) {
      announceFormErrors([error])
    }
  }
  const handleFocus = (): void => {
    setIsFocused(true)
  }
  const togglePasswordVisibility = (): void => {
    setShowPassword(!showPassword)
    announceStatus(showPassword ? 'كلمة المرور مخفية' : 'كلمة المرور مرئية')
    
    // Maintain focus on input
    setTimeout(() => {
      inputRef.current?.focus()
    }, 0)
  }

  // Announce validation errors
  useEffect(() => {
    if (error && !isFocused) {
      announceFormErrors([error])
    }
  }, [error, isFocused])

  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={inputId}
        className={`block text-sm font-medium ${
          error ? 'text-red-700' : 'text-gray-700'
        } ${disabled ? 'opacity-50' : ''}`}
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="مطلوب">
            *
          </span>
        )}
      </label>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-gray-600"
        >
          {description}
        </p>
      )}

      <div className="relative">
        <input
          ref={inputRef}
          id={inputId}
          type={inputType}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          required={required}
          placeholder={placeholder}
          autoComplete={autoComplete}
          pattern={pattern}
          minLength={minLength}
          maxLength={maxLength}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={`${description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
          className={`
            block w-full px-3 py-2 border rounded-md shadow-sm
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
            disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
            ${error 
              ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' 
              : 'border-gray-300 text-gray-900 placeholder-gray-400'
            }
            ${isFocused ? 'ring-2 ring-blue-500' : ''}
          `}
        />

        {type === 'password' && showPasswordToggle && (
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            aria-label={showPassword ? 'إخفاء كلمة المرور' : 'إظهار كلمة المرور'}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400" />
            )}
          </button>
        )}
      </div>

      {error && (
        <div
          id={errorId}
          role="alert"
          aria-live="polite"
          className="flex items-center space-x-2 text-sm text-red-600"
        >
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
}

interface AccessibleTextareaProps {
  label: string
  value: string
  onChange: (value: string) => void
  error?: string
  required?: boolean
  disabled?: boolean
  placeholder?: string
  description?: string
  rows?: number
  maxLength?: number
  className?: string
  showCharacterCount?: boolean
}
export const AccessibleTextarea: React.FC<AccessibleTextareaProps> = ({
  label,
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  placeholder,
  description,
  rows = 4,
  maxLength,
  className = '',
  showCharacterCount = false
}) => {
  const textareaId = useId()
  const errorId = useId()
  const descriptionId = useId()
  const countId = useId()
  const [isFocused, setIsFocused] = useState(false)

  const characterCount = value.length
  const isNearLimit = maxLength && characterCount > maxLength * 0.8
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>): void => {
    onChange(e.target.value)
  }
  const handleBlur = (): void => {
    setIsFocused(false)
  }
  const handleFocus = (): void => {
    setIsFocused(true)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={textareaId}
        className={`block text-sm font-medium ${
          error ? 'text-red-700' : 'text-gray-700'
        } ${disabled ? 'opacity-50' : ''}`}
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="مطلوب">
            *
          </span>
        )}
      </label>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-gray-600"
        >
          {description}
        </p>
      )}

      <textarea
        id={textareaId}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        disabled={disabled}
        required={required}
        placeholder={placeholder}
        rows={rows}
        maxLength={maxLength}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={`
          ${description ? descriptionId : ''} 
          ${error ? errorId : ''} 
          ${showCharacterCount ? countId : ''}
        `.trim()}
        className={`
          block w-full px-3 py-2 border rounded-md shadow-sm resize-vertical
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
          ${error 
            ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' 
            : 'border-gray-300 text-gray-900 placeholder-gray-400'
          }
          ${isFocused ? 'ring-2 ring-blue-500' : ''}
        `}
      />

      {showCharacterCount && maxLength && (
        <div
          id={countId}
          className={`text-sm text-right ${
            isNearLimit ? 'text-orange-600' : 'text-gray-500'
          }`}
          aria-live="polite"
        >
          {characterCount} / {maxLength}
        </div>
      )}

      {error && (
        <div
          id={errorId}
          role="alert"
          aria-live="polite"
          className="flex items-center space-x-2 text-sm text-red-600"
        >
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
}

interface AccessibleSelectProps {
  label: string
  value: string
  onChange: (value: string) => void
  options: Array<{ value: string; label: string; disabled?: boolean }>
  error?: string
  required?: boolean
  disabled?: boolean
  placeholder?: string
  description?: string
  className?: string
}
export const AccessibleSelect: React.FC<AccessibleSelectProps> = ({
  label,
  value,
  onChange,
  options,
  error,
  required = false,
  disabled = false,
  placeholder,
  description,
  className = ''
}) => {
  const selectId = useId()
  const errorId = useId()
  const descriptionId = useId()
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>): void => {
    onChange(e.target.value)
    announceStatus(`تم اختيار: ${e.target.selectedOptions[0]?.text}`)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={selectId}
        className={`block text-sm font-medium ${
          error ? 'text-red-700' : 'text-gray-700'
        } ${disabled ? 'opacity-50' : ''}`}
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="مطلوب">
            *
          </span>
        )}
      </label>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-gray-600"
        >
          {description}
        </p>
      )}

      <select
        id={selectId}
        value={value}
        onChange={handleChange}
        disabled={disabled}
        required={required}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={`${description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
        className={`
          block w-full px-3 py-2 border rounded-md shadow-sm
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
          ${error 
            ? 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500' 
            : 'border-gray-300 text-gray-900'
          }
        `}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (<option
            key={option.value}
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </option>
        ))}
      </select>

      {error && (
        <div
          id={errorId}
          role="alert"
          aria-live="polite"
          className="flex items-center space-x-2 text-sm text-red-600"
        >
          <AlertCircle className="h-4 w-4 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
}

interface AccessibleCheckboxProps {
  label: string
  checked: boolean
  onChange: (checked: boolean) => void
  disabled?: boolean
  description?: string
  className?: string
}
export const AccessibleCheckbox: React.FC<AccessibleCheckboxProps> = ({
  label,
  checked,
  onChange,
  disabled = false,
  description,
  className = ''
}) => {
  const checkboxId = useId()
  const descriptionId = useId()
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    onChange(e.target.checked)
    announceStatus(e.target.checked ? `تم تحديد: ${label}` : `تم إلغاء تحديد: ${label}`)
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-start">
        <input
          id={checkboxId}
          type="checkbox"
          checked={checked}
          onChange={handleChange}
          disabled={disabled}
          aria-describedby={description ? descriptionId : undefined}
          className={`
            h-4 w-4 text-blue-600 border-gray-300 rounded
            focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            disabled:opacity-50 disabled:cursor-not-allowed
          `}
        />
        <label
          htmlFor={checkboxId}
          className={`mr-3 text-sm font-medium text-gray-700 ${
            disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
          }`}
        >
          {label}
        </label>
      </div>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-gray-600 mr-7"
        >
          {description}
        </p>
      )}
    </div>
  )
}

export default {
  AccessibleInput,
  AccessibleTextarea,
  AccessibleSelect,
  AccessibleCheckbox
}
