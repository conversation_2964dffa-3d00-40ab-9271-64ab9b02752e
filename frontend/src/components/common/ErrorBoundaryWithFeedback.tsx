/**
 * Enhanced Error Boundary with User Feedback
 * Provides comprehensive error handling with user-friendly messages
 */

import React, { Component, ReactNode } from 'react'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  language?: 'ar' | 'en'
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  retryCount: number
}

const translations = {
  ar: {
    errorTitle: 'حدث خطأ غير متوقع',
    errorSubtitle: 'نعتذر، حدث خطأ أثناء تحميل هذه الصفحة',
    errorDetails: 'تفاصيل الخطأ',
    tryAgain: 'المحاولة مرة أخرى',
    goHome: 'العودة للرئيسية',
    reportBug: 'الإبلاغ عن خطأ',
    retryAttempt: 'محاولة رقم',
    maxRetriesReached: 'تم الوصول للحد الأقصى من المحاولات',
    contactSupport: 'يرجى الاتصال بالدعم الفني'
  },
  en: {
    errorTitle: 'Something went wrong',
    errorSubtitle: 'Sorry, an error occurred while loading this page',
    errorDetails: 'Error Details',
    tryAgain: 'Try Again',
    goHome: 'Go Home',
    reportBug: 'Report Bug',
    retryAttempt: 'Attempt',
    maxRetriesReached: 'Maximum retries reached',
    contactSupport: 'Please contact support'
  }
}

export default class ErrorBoundaryWithFeedback extends Component<Props, State> {
  private maxRetries = 3
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    }
  }
  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    }
  }
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }

    // Log error to console in development
    if ((process).env.NODE_ENV === 'development') {
      console.error('ErrorBoundary caught an error:', error, errorInfo)
    }

    // In production, you might want to send this to an error reporting service
    if ((process).env.NODE_ENV === 'production') {
      // Example: Send to error reporting service
      // (errorReportingService).captureException(error, { extra: errorInfo })
    }
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: (prevState).retryCount + 1
      }))
    }
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  handleReportBug = () => {
    const { error, errorInfo } = this.state
    const errorReport = {
      error: error?.toString(),
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: (navigator).userAgent,
      url: window.location.href
    }

    // In a real application, you would send this to your bug tracking system
    console.log('Bug report:', errorReport)
    
    // For now, copy to clipboard
    (navigator).clipboard.writeText(JSON.stringify(errorReport, null, 2))
      .then(() => alert('Error details copied to clipboard'))
      .catch(() => console.log('Failed to copy error details'))
  }
  render() {
    const { hasError, error, retryCount } = this.state
    const { children, fallback, language = 'en' } = this.props
    const t = translations[language]
    const isRTL = language === 'ar'
    const canRetry = retryCount < this.maxRetries

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback
      }

      return (<div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4 ${isRTL ? 'rtl' : 'ltr'}`}>
          <div className="max-w-md w-full">
            <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-8 text-center">
              <div className="mb-6">
                <AlertTriangle className="h-16 w-16 text-red-400 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-white mb-2">{(t).errorTitle}</h1>
                <p className="text-white/70">{(t).errorSubtitle}</p>
              </div>

              {retryCount > 0 && (<div className="mb-4 p-3 bg-yellow-500/20 border border-yellow-500/50 rounded-lg">
                  <p className="text-yellow-200 text-sm">
                    {(t).retryAttempt} {retryCount}/{this.maxRetries}
                  </p>
                </div>
              )}

              {!canRetry && (<div className="mb-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg">
                  <p className="text-red-200 text-sm">{(t).maxRetriesReached}</p>
                  <p className="text-red-300 text-xs mt-1">{(t).contactSupport}</p>
                </div>
              )}

              <div className="space-y-3">
                {canRetry && (<Button
                    onClick={this.handleRetry}
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    {(t).tryAgain}
                  </Button>
                )}

                <Button
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="w-full border-white/30 text-white hover:bg-white/10"
                >
                  <Home className="h-4 w-4 mr-2" />
                  {(t).goHome}
                </Button>

                <Button
                  onClick={this.handleReportBug}
                  variant="ghost"
                  className="w-full text-white/70 hover:text-white hover:bg-white/5"
                >
                  <Bug className="h-4 w-4 mr-2" />
                  {(t).reportBug}
                </Button>
              </div>

              {(process).env.NODE_ENV === 'development' && error && (<details className="mt-6 text-left">
                  <summary className="text-white/70 text-sm cursor-pointer hover:text-white">
                    {(t).errorDetails}
                  </summary>
                  <div className="mt-2 p-3 bg-black/30 rounded border text-xs text-red-300 font-mono overflow-auto max-h-40">
                    <div className="mb-2">
                      <strong>Error:</strong> {(error).toString()}
                    </div>
                    {(error).stack && (<div>
                        <strong>Stack:</strong>
                        <pre className="whitespace-pre-wrap text-xs">{(error).stack}</pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      )
    }

    return children
  }
}

// Hook for using error boundary programmatically
export const useErrorHandler = (): void => {
  return (error: Error, errorInfo?: React.ErrorInfo) => {
    // This would trigger the error boundary
    throw error
  }
}

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P): any => (
    <ErrorBoundaryWithFeedback {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundaryWithFeedback>
  )

  (WrappedComponent).displayName = `withErrorBoundary(${(Component).displayName || (Component).name})`
  return WrappedComponent
}
