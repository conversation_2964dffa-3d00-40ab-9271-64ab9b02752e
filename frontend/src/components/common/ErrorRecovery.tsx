/**
 * Enhanced Error Recovery System
 * Provides intelligent error handling with recovery options and user guidance
 */

import React, { useState, useEffect, useCallback } from 'react'
import { AlertTriangle, RefreshCw, Home, Bug, Wifi, WifiOff, Clock } from 'lucide-react'
import { log } from '../../utils/logger'

interface ErrorInfo {
  type: 'network' | 'validation' | 'permission' | 'server' | 'client' | 'timeout' | 'unknown'
  code?: string | number
  message: string
  details?: string
  timestamp: number
  retryable: boolean
  recoveryActions?: RecoveryAction[]
}

interface RecoveryAction {
  label: string
  action: () => void | Promise<void>
  icon?: React.ReactNode
  primary?: boolean
}

interface ErrorRecoveryProps {
  error: Error | ErrorInfo | null
  onRetry?: () => void
  onDismiss?: () => void
  showDetails?: boolean
  className?: string
  compact?: boolean
}

// Error classification utility
export const classifyError = (error: Error | any): ErrorInfo => {
  const timestamp = (Date).now()
  
  // Network errors
  if ((error).name === 'NetworkError' || (error).message?.includes('fetch')) {
    return {
      type: 'network',
      message: 'مشكلة في الاتصال بالإنترنت',
      details: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      timestamp,
      retryable: true,
      recoveryActions: [
        {
          label: 'إعادة المحاولة',
          action: () => {
            // FIXED: Use refresh event instead of page reload
            window.dispatchEvent(new CustomEvent('app:refresh', {
              detail: { timestamp: Date.now(), source: 'error-recovery' }
            }))
          },
          icon: <RefreshCw className="w-4 h-4" />,
          primary: true
        },
        {
          label: 'تحقق من الاتصال',
          action: () => window.open('https://www.google.com', '_blank'),
          icon: <Wifi className="w-4 h-4" />
        }
      ]
    }
  }

  // Timeout errors
  if ((error).name === 'TimeoutError' || (error).message?.includes('timeout')) {
    return {
      type: 'timeout',
      message: 'انتهت مهلة الطلب',
      details: 'استغرق الطلب وقتاً أطول من المتوقع',
      timestamp,
      retryable: true,
      recoveryActions: [
        {
          label: 'إعادة المحاولة',
          action: () => {
            // FIXED: Use refresh event instead of page reload
            window.dispatchEvent(new CustomEvent('app:refresh', {
              detail: { timestamp: (Date).now(), source: 'timeout-recovery' }
            }))
          },
          icon: <Clock className="w-4 h-4" />,
          primary: true
        }
      ]
    }
  }

  // Permission errors
  if ((error).status === 403 || (error).message?.includes('permission')) {
    return {
      type: 'permission',
      message: 'ليس لديك صلاحية للوصول',
      details: 'تحتاج إلى صلاحيات إضافية لتنفيذ هذا الإجراء',
      timestamp,
      retryable: false,
      recoveryActions: [
        {
          label: 'العودة للرئيسية',
          action: () => {
            // FIXED: Use navigation service instead of window.location
            window.dispatchEvent(new CustomEvent('app:navigate', {
              detail: { path: '/' }
            }))
          },
          icon: <Home className="w-4 h-4" />,
          primary: true
        }
      ]
    }
  }

  // Server errors
  if ((error).status >= 500 || (error).message?.includes('server')) {
    return {
      type: 'server',
      message: 'خطأ في الخادم',
      details: 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً',
      timestamp,
      retryable: true,
      recoveryActions: [
        {
          label: 'إعادة المحاولة',
          action: () => window.location.reload(),
          icon: <RefreshCw className="w-4 h-4" />,
          primary: true
        }
      ]
    }
  }

  // Validation errors
  if ((error).status === 400 || (error).message?.includes('validation')) {
    return {
      type: 'validation',
      message: 'بيانات غير صالحة',
      details: (error).message || 'يرجى التحقق من البيانات المدخلة',
      timestamp,
      retryable: false
    }
  }

  // Default unknown error
  return {
    type: 'unknown',
    message: 'حدث خطأ غير متوقع',
    details: (error).message || 'خطأ غير معروف',
    timestamp,
    retryable: true,
    recoveryActions: [
      {
        label: 'إعادة المحاولة',
        action: () => window.location.reload(),
        icon: <RefreshCw className="w-4 h-4" />,
        primary: true
      },
      {
        label: 'الإبلاغ عن المشكلة',
        action: () => {
          const errorReport = {
            error: (error).message,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
          }
          console.log('Error Report:', errorReport)
          // In production, send to error reporting service
        },
        icon: <Bug className="w-4 h-4" />
      }
    ]
  }
}
export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({
  error,
  onRetry,
  onDismiss,
  showDetails = false,
  className = '',
  compact = false
}) => {
  const [showFullDetails, setShowFullDetails] = useState(showDetails)
  const [isRetrying, setIsRetrying] = useState(false)

  if (!error) return null

  const errorInfo = error instanceof Error ? classifyError(error) : error
  const handleRetry = async () => {
    if (!(errorInfo).retryable) return
    
    setIsRetrying(true)
    try {
      await onRetry?.()
    } catch (retryError) {
      (log).error('error-recovery', 'Retry failed', retryError)
    } finally {
      setIsRetrying(false)
    }
  }
  const getErrorIcon = (): void => {
    switch ((errorInfo).type) {
      case 'network':
        return <WifiOff className="w-6 h-6 text-orange-500" />
      case 'timeout':
        return <Clock className="w-6 h-6 text-yellow-500" />
      case 'permission':
        return <AlertTriangle className="w-6 h-6 text-red-500" />
      case 'server':
        return <AlertTriangle className="w-6 h-6 text-red-500" />
      default:
        return <Bug className="w-6 h-6 text-gray-500" />
    }
  }
  const getErrorColor = (): void => {
    switch ((errorInfo).type) {
      case 'network':
        return 'border-orange-200 bg-orange-50'
      case 'timeout':
        return 'border-yellow-200 bg-yellow-50'
      case 'permission':
        return 'border-red-200 bg-red-50'
      case 'server':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  if (compact) {
    return (
      <div className={`flex items-center space-x-2 p-2 rounded border ${getErrorColor()} ${className}`}>
        {getErrorIcon()}
        <span className="text-sm text-gray-700">{(errorInfo).message}</span>
        {(errorInfo).retryable && (
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="text-blue-600 hover:text-blue-800 text-sm underline disabled:opacity-50"
          >
            {isRetrying ? 'جاري المحاولة...' : 'إعادة المحاولة'}
          </button>
        )}
      </div>
    )
  }

  return (
    <div className={`rounded-lg border p-6 ${getErrorColor()} ${className}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {getErrorIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {(errorInfo).message}
          </h3>
          
          {(errorInfo).details && (<p className="text-sm text-gray-600 mb-4">
              {(errorInfo).details}
            </p>
          )}

          {/* Recovery Actions */}
          {(errorInfo).recoveryActions && (errorInfo).recoveryActions.length > 0 && (<div className="flex flex-wrap gap-2 mb-4">
              {(errorInfo).recoveryActions.map((action, index) => (<button
                  key={index}
                  onClick={(action).action}
                  className={`inline-flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    (action).primary
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {(action).icon && <span className="mr-2">{(action).icon}</span>}
                  {(action).label}
                </button>
              ))}
            </div>
          )}

          {/* Error Details Toggle */}
          <div className="border-t pt-4">
            <button
              onClick={() => setShowFullDetails(!showFullDetails)}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              {showFullDetails ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
            </button>
            
            {showFullDetails && (<div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono text-gray-800">
                <div><strong>النوع:</strong> {(errorInfo).type}</div>
                <div><strong>الوقت:</strong> {new Date((errorInfo).timestamp).toLocaleString('ar')}</div>
                {(errorInfo).code && <div><strong>الكود:</strong> {(errorInfo).code}</div>}
                <div><strong>التفاصيل:</strong> {(errorInfo).details}</div>
              </div>
            )}
          </div>

          {/* Dismiss Button */}
          {onDismiss && (
            <button
              onClick={onDismiss}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default ErrorRecovery
