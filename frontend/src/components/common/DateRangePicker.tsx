/**
 * Date Range Picker Component
 * A comprehensive date range picker with presets and custom range selection
 */

import React, { useState, useEffect } from 'react'
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react'
// MEMORY OPTIMIZATION: Import only specific date-fns functions to reduce bundle size
import { format } from 'date-fns/format'
import { addDays } from 'date-fns/addDays'
import { addMonths } from 'date-fns/addMonths'
import { addYears } from 'date-fns/addYears'
import { startOfDay } from 'date-fns/startOfDay'
import { endOfDay } from 'date-fns/endOfDay'
import { startOfWeek } from 'date-fns/startOfWeek'
import { endOfWeek } from 'date-fns/endOfWeek'
import { startOfMonth } from 'date-fns/startOfMonth'
import { endOfMonth } from 'date-fns/endOfMonth'
import { startOfQuarter } from 'date-fns/startOfQuarter'
import { endOfQuarter } from 'date-fns/endOfQuarter'
import { startOfYear } from 'date-fns/startOfYear'
import { endOfYear } from 'date-fns/endOfYear'
import { isSameMonth } from 'date-fns/isSameMonth'
import { isSameDay } from 'date-fns/isSameDay'
import { isToday } from 'date-fns/isToday'
import { isWithinInterval } from 'date-fns/isWithinInterval'
import { isBefore } from 'date-fns/isBefore'
import { isAfter } from 'date-fns/isAfter'
// MEMORY OPTIMIZATION: Import only needed locales
import { ar } from 'date-fns/locale/ar'
import { enUS } from 'date-fns/locale/en-US'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'

export type DateRange = {
  from: Date
  to: Date
}

export type DateRangePreset = {
  name: string
  label: string
  value: () => DateRange
}

export interface DateRangePickerProps {
  onChange: (range: DateRange) => void
  value?: DateRange
  presets?: DateRangePreset[]
  align?: 'start' | 'center' | 'end'
  locale?: 'ar' | 'en'
  showCompare?: boolean
  onCompareChange?: (range: DateRange | null) => void
  compareValue?: DateRange | null
  className?: string
  disabled?: boolean
}

const defaultPresets: (locale: 'ar' | 'en') => DateRangePreset[] = (locale) => [
  {
    name: 'today',
    label: locale === 'ar' ? 'اليوم' : 'Today',
    value: () => ({
      from: startOfDay(new Date()),
      to: endOfDay(new Date())
    })
  },
  {
    name: 'yesterday',
    label: locale === 'ar' ? 'أمس' : 'Yesterday',
    value: () => ({
      from: startOfDay(addDays(new Date(), -1)),
      to: endOfDay(addDays(new Date(), -1))
    })
  },
  {
    name: 'last7Days',
    label: locale === 'ar' ? 'آخر 7 أيام' : 'Last 7 Days',
    value: () => ({
      from: startOfDay(addDays(new Date(), -6)),
      to: endOfDay(new Date())
    })
  },
  {
    name: 'last30Days',
    label: locale === 'ar' ? 'آخر 30 يوم' : 'Last 30 Days',
    value: () => ({
      from: startOfDay(addDays(new Date(), -29)),
      to: endOfDay(new Date())
    })
  },
  {
    name: 'thisWeek',
    label: locale === 'ar' ? 'هذا الأسبوع' : 'This Week',
    value: () => ({
      from: startOfWeek(new Date(), { weekStartsOn: 1 }),
      to: endOfWeek(new Date(), { weekStartsOn: 1 })
    })
  },
  {
    name: 'lastWeek',
    label: locale === 'ar' ? 'الأسبوع الماضي' : 'Last Week',
    value: () => {
      const lastWeekStart = addDays(startOfWeek(new Date(), { weekStartsOn: 1 }), -7)
      return {
        from: lastWeekStart,
        to: addDays(lastWeekStart, 6)
      }
    }
  },
  {
    name: 'thisMonth',
    label: locale === 'ar' ? 'هذا الشهر' : 'This Month',
    value: () => ({
      from: startOfMonth(new Date()),
      to: endOfMonth(new Date())
    })
  },
  {
    name: 'lastMonth',
    label: locale === 'ar' ? 'الشهر الماضي' : 'Last Month',
    value: () => {
      const lastMonth = addMonths(new Date(), -1)
      return {
        from: startOfMonth(lastMonth),
        to: endOfMonth(lastMonth)
      }
    }
  },
  {
    name: 'thisQuarter',
    label: locale === 'ar' ? 'هذا الربع' : 'This Quarter',
    value: () => ({
      from: startOfQuarter(new Date()),
      to: endOfQuarter(new Date())
    })
  },
  {
    name: 'lastQuarter',
    label: locale === 'ar' ? 'الربع الماضي' : 'Last Quarter',
    value: () => {
      const lastQuarter = addMonths(new Date(), -3)
      return {
        from: startOfQuarter(lastQuarter),
        to: endOfQuarter(lastQuarter)
      }
    }
  },
  {
    name: 'thisYear',
    label: locale === 'ar' ? 'هذا العام' : 'This Year',
    value: () => ({
      from: startOfYear(new Date()),
      to: endOfYear(new Date())
    })
  },
  {
    name: 'lastYear',
    label: locale === 'ar' ? 'العام الماضي' : 'Last Year',
    value: () => {
      const lastYear = addYears(new Date(), -1)
      return {
        from: startOfYear(lastYear),
        to: endOfYear(lastYear)
      }
    }
  }
]

export function DateRangePicker({
  onChange,
  value,
  presets = [],
  align = 'start',
  locale = 'en',
  showCompare = false,
  onCompareChange,
  compareValue = null,
  className,
  disabled = false
}: DateRangePickerProps): React.ReactElement {
  const [date, setDate] = useState<DateRange | undefined>(value)
  const [compareDate, setCompareDate] = useState<DateRange | null>(compareValue)
  const [isComparing, setIsComparing] = useState<boolean>(!!compareValue)
  const [calendarOpen, setCalendarOpen] = useState<boolean>(false)
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null)

  const allPresets = [...defaultPresets(locale), ...presets]

  // Update internal state when value changes
  useEffect(() => {
    if (value) {
      setDate(value)
    }
  }, [value])

  // Update internal state when compareValue changes
  useEffect(() => {
    if (compareValue) {
      setCompareDate(compareValue)
      setIsComparing(true)
    } else {
      setIsComparing(false)
    }
  }, [compareValue])

  // Handle preset selection
  const handlePresetChange = (presetName: string): void => {
    const preset = allPresets.find(p => p.name === presetName)
    if (preset) {
      const newRange = preset.value()
      setDate(newRange)
      setSelectedPreset(presetName)
      onChange(newRange)
      
      // If comparing, update compare range based on the same preset but for previous period
      if (isComparing && onCompareChange) {
        const daysDiff = Math.round((newRange.to.getTime() - newRange.from.getTime()) / (1000 * 60 * 60 * 24))
        const compareFrom = addDays(newRange.from, -(daysDiff + 1))
        const compareTo = addDays(newRange.to, -(daysDiff + 1))
        const newCompareRange = { from: compareFrom, to: compareTo }
        setCompareDate(newCompareRange)
        onCompareChange(newCompareRange)
      }
    }
  }

  // Handle date selection
  const handleDateChange = (range: DateRange | undefined): void => {
    if (range?.from && range?.to) {
      setDate(range)
      setSelectedPreset(null)
      onChange(range)
      
      // If comparing, update compare range based on the same length but for previous period
      if (isComparing && onCompareChange && range.from && range.to) {
        const daysDiff = Math.round((range.to.getTime() - range.from.getTime()) / (1000 * 60 * 60 * 24))
        const compareFrom = addDays(range.from, -(daysDiff + 1))
        const compareTo = addDays(range.to, -(daysDiff + 1))
        const newCompareRange = { from: compareFrom, to: compareTo }
        setCompareDate(newCompareRange)
        onCompareChange(newCompareRange)
      }
    }
  }

  // Toggle compare mode
  const toggleCompare = (): void => {
    if (!isComparing && date) {
      // Enable compare mode
      const daysDiff = Math.round((date.to.getTime() - date.from.getTime()) / (1000 * 60 * 60 * 24))
      const compareFrom = addDays(date.from, -(daysDiff + 1))
      const compareTo = addDays(date.to, -(daysDiff + 1))
      const newCompareRange = { from: compareFrom, to: compareTo }
      setCompareDate(newCompareRange)
      setIsComparing(true)
      if (onCompareChange) onCompareChange(newCompareRange)
    } else {
      // Disable compare mode
      setCompareDate(null)
      setIsComparing(false)
      if (onCompareChange) onCompareChange(null)
    }
  }

  // Format date range for display
  const formatDateRange = (range?: DateRange): string => {
    if (!range?.from || !range?.to) {
      return locale === 'ar' ? 'اختر التاريخ' : 'Select date range'
    }

    if (isSameDay(range.from, range.to)) {
      return format(range.from, 'PPP', { locale: locale === 'ar' ? ar : enUS })
    }

    return `${format(range.from, 'PP', { locale: locale === 'ar' ? ar : enUS })} - ${format(range.to, 'PP', { locale: locale === 'ar' ? ar : enUS })}`
  }

  return (<div className={cn("grid gap-2", className)}>
      <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn("glass-input w-full justify-start text-left font-normal",
              !date && "text-muted-foreground",
              locale === 'ar' && "flex-row-reverse")}
            disabled={disabled}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            <div className="flex-1 text-white/90">
              {formatDateRange(date)}
              {isComparing && compareDate && (<Badge variant="outline" className="ml-2 bg-blue-500/20 text-blue-300 border-blue-500/30">
                  {locale === 'ar' ? 'مقارنة' : 'Compare'}: {formatDateRange(compareDate)}
                </Badge>
              )}
            </div>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto glass-card border-white/20" align={align}>
          <div className="flex flex-col sm:flex-row gap-4 p-2">
            {/* Presets */}
            <div className="flex flex-col gap-2">
              <div className="text-sm font-medium text-white mb-2">
                {locale === 'ar' ? 'الفترات المحددة مسبقًا' : 'Preset Ranges'}
              </div>
              <div className="flex flex-wrap gap-2 sm:flex-col">
                {allPresets.map((preset) => (<Button
                    key={preset.name}
                    onClick={() => handlePresetChange(preset.name)}
                    variant="ghost"
                    size="sm"
                    className={cn("justify-start text-white/70 hover:text-white hover:bg-white/10",
                      selectedPreset === preset.name && "bg-white/20 text-white"
                    )}
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>
            </div>
            
            {/* Calendar */}
            <div className="flex flex-col gap-2">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={handleDateChange}
                numberOfMonths={2}
                locale={locale === 'ar' ? ar : enUS}
                dir={locale === 'ar' ? 'rtl' : 'ltr'}
                className="rounded-md border border-white/20 bg-white/5"
                classNames={{
                  day_selected: "bg-blue-500 text-white hover:bg-blue-600 hover:text-white",
                  day_today: "bg-white/10 text-white",
                  day_range_middle: "bg-blue-500/20 text-white",
                  day_range_end: "bg-blue-500 text-white hover:bg-blue-600 hover:text-white",
                  day_range_start: "bg-blue-500 text-white hover:bg-blue-600 hover:text-white",
                }}
              />
              
              {/* Compare Toggle */}
              {showCompare && (<div className="flex items-center gap-2 pt-2 border-t border-white/10">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleCompare}
                    className={cn(
                      "text-white/70 hover:text-white hover:bg-white/10",
                      isComparing && "bg-blue-500/20 text-blue-300 border-blue-500/30")}
                  >
                    {isComparing 
                      ? (locale === 'ar' ? 'إلغاء المقارنة' : 'Remove Comparison') 
                      : (locale === 'ar' ? 'إضافة مقارنة' : 'Add Comparison')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}

export default DateRangePicker
