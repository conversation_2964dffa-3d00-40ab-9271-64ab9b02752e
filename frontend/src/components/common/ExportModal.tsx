/**
 * Export Modal
 * Enhanced modal for exporting KPI data in different formats
 */

import React, { useState } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Download, FileSpreadsheet, FileText, File, Loader2, Calendar, BarChart3, Info } from 'lucide-react'
import { DateRangePicker, DateRange } from '@/components/common/DateRangePicker'
import { KPIFilterValue } from '@/components/kpi/KPIFilterPanel'
import { unifiedExport as exportService, ExportOptions } from '@/services/unifiedExport'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ExportModalProps {
  isOpen: boolean
  onClose: () => void
  data: ExportData
  title?: string
  formats?: Array<'csv' | 'excel' | 'pdf'>
  language: 'ar' | 'en'
  defaultFilename?: string
  showAdvancedOptions?: boolean
}

const translations = {
  ar: {
    exportData: 'تصدير البيانات',
    selectFormat: 'اختر تنسيق التصدير',
    csv: 'ملف CSV (قيم مفصولة بفواصل)',
    excel: 'ملف Excel (XLSX)',
    pdf: 'ملف PDF',
    cancel: 'إلغاء',
    export: 'تصدير',
    exporting: 'جاري التصدير...',
    csvDescription: 'تنسيق نصي بسيط يمكن فتحه في Excel أو Google Sheets',
    excelDescription: 'تنسيق جدول بيانات يحتفظ بالتنسيق والصيغ',
    pdfDescription: 'تنسيق مستند ثابت مناسب للطباعة والمشاركة',
    filename: 'اسم الملف',
    advancedOptions: 'خيارات متقدمة',
    includeCharts: 'تضمين الرسوم البيانية',
    includeMetadata: 'تضمين البيانات الوصفية',
    dateRange: 'النطاق الزمني',
    customDateRange: 'نطاق زمني مخصص',
    exportSuccess: 'تم تصدير البيانات بنجاح',
    exportError: 'خطأ في تصدير البيانات'
  },
  en: {
    exportData: 'Export Data',
    selectFormat: 'Select export format',
    csv: 'CSV File (Comma Separated Values)',
    excel: 'Excel File (XLSX)',
    pdf: 'PDF Document',
    cancel: 'Cancel',
    export: 'Export',
    exporting: 'Exporting...',
    csvDescription: 'Simple text format that can be opened in Excel or Google Sheets',
    excelDescription: 'Spreadsheet format that preserves formatting and formulas',
    pdfDescription: 'Fixed document format suitable for printing and sharing',
    filename: 'Filename',
    advancedOptions: 'Advanced Options',
    includeCharts: 'Include Charts',
    includeMetadata: 'Include Metadata',
    dateRange: 'Date Range',
    customDateRange: 'Custom Date Range',
    exportSuccess: 'Data exported successfully',
    exportError: 'Error exporting data'
  }
}

export default function ExportModal({
  isOpen,
  onClose,
  data,
  title,
  formats = ['csv', 'excel', 'pdf'],
  language,
  defaultFilename,
  showAdvancedOptions = false
}: ExportModalProps): React.ReactElement {
  const [selectedFormat, setSelectedFormat] = useState<'csv' | 'excel' | 'pdf'>('csv')
  const [isExporting, setIsExporting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [filename, setFilename] = useState(defaultFilename || `export_${new Date().toISOString().split('T')[0]}`)
  const [includeCharts, setIncludeCharts] = useState(true)
  const [includeMetadata, setIncludeMetadata] = useState(true)
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined)
  const [showAdvanced, setShowAdvanced] = useState(false)

  const t = translations[language]
  const isRTL = language === 'ar'

  const handleExport = async () => {
    try {
      setIsExporting(true)
      setError(null)
      setSuccess(false)

      const exportOptions: ExportOptions = {
        format: selectedFormat,
        language,
        includeCharts,
        includeMetadata,
        dateRange,
        filename: filename || `export_${new Date().toISOString().split('T')[0]}`
      }

      // Validate options
      const validation = exportService.validateExportOptions(exportOptions)
      if (!validation.valid) {
        throw new Error(validation.errors.join(', '))
      }

      // Export data
      await exportService.exportDashboard(data, exportOptions)

      setSuccess(true)
      setTimeout(() => {
        onClose()
        setSuccess(false)
      }, 2000)
    } catch (error: any) {
      console.error('Export error:', error)
      setError(error.message || t.exportError)
    } finally {
      setIsExporting(false)
    }
  }

  const getFormatIcon = (format: string): void => {
    switch (format) {
      case 'csv':
        return <FileText className="h-5 w-5 text-green-400" />
      case 'excel':
        return <FileSpreadsheet className="h-5 w-5 text-blue-400" />
      case 'pdf':
        return <File className="h-5 w-5 text-red-400" />
      default:
        return <FileText className="h-5 w-5" />
    }
  }

  const getFormatDescription = (format: string): void => {
    switch (format) {
      case 'csv':
        return t.csvDescription
      case 'excel':
        return t.excelDescription
      case 'pdf':
        return t.pdfDescription
      default:
        return ''
    }
  }

  return (<Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`glass-card border-white/20 max-w-md ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <DialogTitle className="text-white text-lg">
            {title || t.exportData}
          </DialogTitle>
          <DialogDescription className="text-white/70">
            {t.selectFormat}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="space-y-3">
            {formats.includes('csv') && (
              <div
                className={`flex items-center space-x-3 space-y-0 glass-input p-3 rounded-md cursor-pointer transition-colors ${
                  selectedFormat === 'csv' ? 'bg-white/20 border-white/40' : 'hover:bg-white/10'
                }`}
                onClick={() => setSelectedFormat('csv')}
              >
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  selectedFormat === 'csv' ? 'border-white bg-white' : 'border-white/50'
                }`}>
                  {selectedFormat === 'csv' && <div className="w-2 h-2 rounded-full bg-black" />}
                </div>
                <div className="flex-1 flex items-center gap-3">
                  {getFormatIcon('csv')}
                  <div>
                    <div className="text-white">{t.csv}</div>
                    <div className="text-white/60 text-xs">{getFormatDescription('csv')}</div>
                  </div>
                </div>
              </div>
            )}

            {formats.includes('excel') && (
              <div
                className={`flex items-center space-x-3 space-y-0 glass-input p-3 rounded-md cursor-pointer transition-colors ${
                  selectedFormat === 'excel' ? 'bg-white/20 border-white/40' : 'hover:bg-white/10'
                }`}
                onClick={() => setSelectedFormat('excel')}
              >
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  selectedFormat === 'excel' ? 'border-white bg-white' : 'border-white/50'
                }`}>
                  {selectedFormat === 'excel' && <div className="w-2 h-2 rounded-full bg-black" />}
                </div>
                <div className="flex-1 flex items-center gap-3">
                  {getFormatIcon('excel')}
                  <div>
                    <div className="text-white">{t.excel}</div>
                    <div className="text-white/60 text-xs">{getFormatDescription('excel')}</div>
                  </div>
                </div>
              </div>
            )}

            {formats.includes('pdf') && (
              <div
                className={`flex items-center space-x-3 space-y-0 glass-input p-3 rounded-md cursor-pointer transition-colors ${
                  selectedFormat === 'pdf' ? 'bg-white/20 border-white/40' : 'hover:bg-white/10'
                }`}
                onClick={() => setSelectedFormat('pdf')}
              >
                <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                  selectedFormat === 'pdf' ? 'border-white bg-white' : 'border-white/50'
                }`}>
                  {selectedFormat === 'pdf' && <div className="w-2 h-2 rounded-full bg-black" />}
                </div>
                <div className="flex-1 flex items-center gap-3">
                  {getFormatIcon('pdf')}
                  <div>
                    <div className="text-white">{t.pdf}</div>
                    <div className="text-white/60 text-xs">{getFormatDescription('pdf')}</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="glass-button"
            disabled={isExporting}
          >
            {t.cancel}
          </Button>
          <Button
            type="button"
            onClick={handleExport}
            className="glass-button"
            disabled={isExporting}
          >
            {isExporting ? (<>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {t.exporting}
              </>
            ) : (<>
                <Download className="h-4 w-4 mr-2" />
                {t.export}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
