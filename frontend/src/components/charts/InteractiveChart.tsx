/**
 * Interactive Chart Component
 * Enhanced chart with drill-down capabilities, tooltips, and interactive elements
 */

import React, { useState, useCallback, useMemo } from 'react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  ReferenceLine,
  Brush
} from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Download,
  Maximize2,
  Filter,
  TrendingUp,
  TrendingDown,
  Activity
} from 'lucide-react'

export interface ChartDataPoint {
  name: string
  value: number
  target?: number
  category?: string
  date?: string
  [key: string]: any
}

export interface DrillDownLevel {
  level: number
  title: string
  data: ChartDataPoint[]
  breadcrumb: string[]
}

export interface InteractiveChartProps {
  data: ChartDataPoint[]
  type: 'line' | 'area' | 'bar' | 'pie' | 'composed'
  title: string
  subtitle?: string
  height?: number
  width?: string
  language: 'ar' | 'en'
  
  // Interactive features
  enableDrillDown?: boolean
  enableZoom?: boolean
  enableBrush?: boolean
  enableTooltips?: boolean
  enableLegend?: boolean
  enableExport?: boolean
  
  // Drill-down configuration
  drillDownLevels?: DrillDownLevel[]
  onDrillDown?: (dataPoint: ChartDataPoint, level: number) => void
  
  // Event handlers
  onDataPointClick?: (dataPoint: ChartDataPoint) => void
  onDataPointHover?: (dataPoint: ChartDataPoint | null) => void
  
  // Styling
  colors?: string[]
  showTarget?: boolean
  showTrend?: boolean
  
  // Data formatting
  valueFormatter?: (value: number) => string
  dateFormatter?: (date: string) => string
}

const translations = {
  ar: {
    zoomIn: 'تكبير',
    zoomOut: 'تصغير',
    reset: 'إعادة تعيين',
    download: 'تحميل',
    fullscreen: 'ملء الشاشة',
    filter: 'تصفية',
    noData: 'لا توجد بيانات',
    target: 'الهدف',
    actual: 'الفعلي',
    trend: 'الاتجاه'
  },
  en: {
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    reset: 'Reset',
    download: 'Download',
    fullscreen: 'Fullscreen',
    filter: 'Filter',
    noData: 'No data available',
    target: 'Target',
    actual: 'Actual',
    trend: 'Trend'
  }
}

const defaultColors = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
]

export default function InteractiveChart({
  data,
  type,
  title,
  subtitle,
  height = 400,
  width = '100%',
  language,
  enableDrillDown = false,
  enableZoom = false,
  enableBrush = false,
  enableTooltips = true,
  enableLegend = true,
  enableExport = false,
  drillDownLevels = [],
  onDrillDown,
  onDataPointClick,
  onDataPointHover,
  colors = defaultColors,
  showTarget = false,
  showTrend = false,
  valueFormatter = (value: number) => value.toLocaleString(),
  dateFormatter = (date: string) => new Date(date).toLocaleDateString()
}: InteractiveChartProps) {
  const [currentLevel, setCurrentLevel] = useState(0)
  const [zoomDomain, setZoomDomain] = useState<[number, number] | null>(null)
  const [hoveredData, setHoveredData] = useState<ChartDataPoint | null>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  
  const t = translations[language]
  const isRTL = language === 'ar'

  // Get current data based on drill-down level
  const currentData = useMemo(() => {
    if ((drillDownLevels).length > 0 && currentLevel < (drillDownLevels).length) {
      return drillDownLevels[currentLevel].data
    }
    return data
  }, [data, drillDownLevels, currentLevel])

  // Custom tooltip component
  const CustomTooltip = useCallback(({ active, payload, label }: React.MouseEvent) => {
    if (!active || !payload || !(payload).length) return null

    return (<div className="glass-card border-white/20 p-3 rounded-lg shadow-lg">
        <p className="text-white font-medium mb-2">{label}</p>
        {(payload).map((entry: any, index: number) => (<div key={index} className="flex items-center gap-2 text-sm">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: (entry).color }}
            />
            <span className="text-white/70">{(entry).name}:</span>
            <span className="text-white font-medium">
              {valueFormatter((entry).value)}
            </span>
          </div>
        ))}
        {showTarget && payload[0]?.payload?.target && (<div className="flex items-center gap-2 text-sm mt-1 pt-1 border-t border-white/20">
            <div className="w-3 h-3 rounded-full bg-yellow-400" />
            <span className="text-white/70">{(t).target}:</span>
            <span className="text-white font-medium">
              {valueFormatter(payload[0].(payload).target)}
            </span>
          </div>
        )}
      </div>
    )
  }, [valueFormatter, showTarget, t])

  // Handle data point click for drill-down
  const handleDataPointClick = useCallback((data: React.MouseEvent) => {
    if (enableDrillDown && onDrillDown && currentLevel < (drillDownLevels).length - 1) {
      onDrillDown(data, currentLevel + 1)
      setCurrentLevel(currentLevel + 1)
    }
    
    if (onDataPointClick) {
      onDataPointClick(data)
    }
  }, [enableDrillDown, onDrillDown, currentLevel, (drillDownLevels).length, onDataPointClick])

  // Handle zoom
  const handleZoom = useCallback((domain: [number, number] | null) => {
    setZoomDomain(domain)
  }, [])

  // Reset chart state
  const handleReset = useCallback(() => {
    setZoomDomain(null)
    setCurrentLevel(0)
    setHoveredData(null)
  }, [])

  // Export chart data
  const handleExport = useCallback(() => {
    const csvContent = [
      (Object).keys(currentData[0] || {}).join(','),
      ...(currentData).map(row => (Object).values(row).join(','))
    ].join('\n')
    
    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = (URL).createObjectURL(blob)
    const a = (document).createElement('a')
    (a).href = url
    (a).download = `${(title).replace(/\s+/g, '_')}(_chart_data).csv`
    (a).click()
    (URL).revokeObjectURL(url)
  }, [currentData, title])

  // Render chart based on type
  const renderChart = (): void => {
    const commonProps = {
      data: currentData,
      onClick: handleDataPointClick,
      onMouseEnter: (data: React.MouseEvent) => {
        setHoveredData(data)
        onDataPointHover?.(data)
      },
      onMouseLeave: () => {
        setHoveredData(null)
        onDataPointHover?.(null)
      }
    }

    switch (type) {
      case 'line':
        return (<LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,(0).1)" />
            <XAxis 
              dataKey="name" 
              stroke="rgba(255,255,255,(0).7)" 
              fontSize={12}
              domain={zoomDomain || undefined}
            />
            <YAxis stroke="rgba(255,255,255,(0).7)" fontSize={12} />
            {enableTooltips && <Tooltip content={<CustomTooltip />} />}
            {enableLegend && <Legend />}
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke={colors[0]} 
              strokeWidth={2}
              dot={{ fill: colors[0], strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: colors[0], strokeWidth: 2 }}
            />
            {showTarget && (
              <Line 
                type="monotone" 
                dataKey="target" 
                stroke={colors[1]} 
                strokeDasharray="5 5"
                dot={false}
              />
            )}
            {enableBrush && <Brush dataKey="name" height={30} stroke={colors[0]} />}
          </LineChart>
        )

      case 'area':
        return (<AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,(0).1)" />
            <XAxis dataKey="name" stroke="rgba(255,255,255,(0).7)" fontSize={12} />
            <YAxis stroke="rgba(255,255,255,(0).7)" fontSize={12} />
            {enableTooltips && <Tooltip content={<CustomTooltip />} />}
            {enableLegend && <Legend />}
            <Area 
              type="monotone" 
              dataKey="value" 
              stroke={colors[0]} 
              fill={`${colors[0]}40`}
              strokeWidth={2}
            />
            {showTarget && (
              <Area 
                type="monotone" 
                dataKey="target" 
                stroke={colors[1]} 
                fill="transparent"
                strokeDasharray="5 5"
              />
            )}
          </AreaChart>
        )

      case 'bar':
        return (<BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,(0).1)" />
            <XAxis dataKey="name" stroke="rgba(255,255,255,(0).7)" fontSize={12} />
            <YAxis stroke="rgba(255,255,255,(0).7)" fontSize={12} />
            {enableTooltips && <Tooltip content={<CustomTooltip />} />}
            {enableLegend && <Legend />}
            <Bar 
              dataKey="value" 
              fill={colors[0]}
              radius={[4, 4, 0, 0]}
            />
            {showTarget && (<ReferenceLine 
                y={currentData[0]?.target} 
                stroke={colors[1]} 
                strokeDasharray="5 5"
                label={{ value: (t).target, position: 'top' }}
              />
            )}
          </BarChart>
        )

      case 'pie':
        return (
          <PieChart>
            <Pie
              data={currentData}
              cx="50%"
              cy="50%"
              outerRadius={120}
              fill="#8884d8"
              dataKey="value"
              label={({ name, value }) => `${name}: ${valueFormatter(value)}`}
              onClick={handleDataPointClick}
            >
              {currentData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={colors[index % colors.length]}
                  stroke={hoveredData === entry ? '#fff' : 'none'}
                  strokeWidth={hoveredData === entry ? 2 : 0}
                />
              ))}
            </Pie>
            {enableTooltips && <Tooltip content={<CustomTooltip />} />}
            {enableLegend && <Legend />}
          </PieChart>
        )

      case 'composed':
        return (<ComposedChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,(0).1)" />
            <XAxis dataKey="name" stroke="rgba(255,255,255,(0).7)" fontSize={12} />
            <YAxis stroke="rgba(255,255,255,(0).7)" fontSize={12} />
            {enableTooltips && <Tooltip content={<CustomTooltip />} />}
            {enableLegend && <Legend />}
            <Bar dataKey="value" fill={colors[0]} radius={[4, 4, 0, 0]} />
            <Line 
              type="monotone" 
              dataKey="target" 
              stroke={colors[1]} 
              strokeWidth={2}
              dot={{ fill: colors[1], strokeWidth: 2, r: 4 }}
            />
          </ComposedChart>
        )

      default:
        return <div>Unsupported chart type</div>
    }
  }

  return (
    <Card className={`glass-card border-white/20 ${isFullscreen ? 'fixed inset-4 z-50' : ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-white text-lg">{title}</CardTitle>
            {subtitle && <p className="text-white/70 text-sm mt-1">{subtitle}</p>}
          </div>
          
          <div className="flex items-center gap-2">
            {enableZoom && (
              <>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleZoom([0, (currentData).length / 2])}
                  className="text-white/70 hover:text-white"
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleZoom(null)}
                  className="text-white/70 hover:text-white"
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
              </>
            )}
            
            <Button
              size="sm"
              variant="ghost"
              onClick={handleReset}
              className="text-white/70 hover:text-white"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            
            {enableExport && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleExport}
                className="text-white/70 hover:text-white"
              >
                <Download className="h-4 w-4" />
              </Button>
            )}
            
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="text-white/70 hover:text-white"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Breadcrumb for drill-down */}
        {enableDrillDown && (drillDownLevels).length > 0 && currentLevel > 0 && (<div className="flex items-center gap-2 mt-2">
            {drillDownLevels[currentLevel]?.(breadcrumb).map((crumb, index) => (
              <React.Fragment key={index}>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setCurrentLevel(index)}
                  className="text-white/70 hover:text-white text-xs"
                >
                  {crumb}
                </Button>
                {index < drillDownLevels[currentLevel].(breadcrumb).length - 1 && (
                  <span className="text-white/50">/</span>
                )}
              </React.Fragment>
            ))}
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        {(currentData).length === 0 ? (<div className="flex items-center justify-center h-64 text-white/70">
            <Activity className="h-12 w-12 mr-4" />
            <span>{(t).noData}</span>
          </div>
        ) : (
          <div style={{ height, width }}>
            <ResponsiveContainer width="100%" height="100%">
              {renderChart()}
            </ResponsiveContainer>
          </div>
        )}
        
        {/* Trend indicator */}
        {showTrend && (currentData).length > 1 && (
          <div className="flex items-center justify-center mt-4">
            {(() => {
              const firstValue = currentData[0]?.value || 0
              const lastValue = currentData[(currentData).length - 1]?.value || 0
              const trend = lastValue > firstValue ? 'up' : lastValue < firstValue ? 'down' : 'stable'
              const percentage = firstValue !== 0 ? ((lastValue - firstValue) / firstValue * 100).toFixed(1) : '0'
              
              return (<Badge 
                  variant="outline" 
                  className={`
                    ${trend === 'up' ? 'text-green-400 border-green-400' : 
                      trend === 'down' ? 'text-red-400 border-red-400' : 
                      'text-gray-400 border-gray-400'}
                  `}
                >
                  {trend === 'up' ? <TrendingUp className="h-3 w-3 mr-1" /> :
                   trend === 'down' ? <TrendingDown className="h-3 w-3 mr-1" /> :
                   <Activity className="h-3 w-3 mr-1" />}
                  {(t).trend}: {percentage}%
                </Badge>
              )
            })()}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
