/**
 * Sidebar Navigation Component
 * Handles the main navigation sidebar with role-based menu items
 */

import React, { memo, useMemo } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { ChevronDown, ChevronRight, Activity, AlertTriangle } from 'lucide-react'
import { getNavigationForRole, NavigationItem } from './navigationConfig'
import { getEnhancedNavigationForRole, EnhancedNavigationItem } from './enhancedNavigationConfig'
import { navigationTranslations } from './translations'
import { useHierarchicalAccessRedux } from '../../hooks/useHierarchicalAccessRedux'

interface SidebarProps {
  isOpen: boolean
  userRole: string
  language: 'ar' | 'en'
  expandedMenus: string[]
  onToggleMenu: (menuId: string) => void
  onClose?: () => void
  useEnhancedNavigation?: boolean
}
const Sidebar: React.FC<SidebarProps> = memo(({
  isOpen,
  userRole,
  language,
  expandedMenus,
  onToggleMenu,
  onClose,
  useEnhancedNavigation = true
}) => {
  const location = useLocation()
  const t = useMemo(() => navigationTranslations[language], [language])

  // Use Redux-based hierarchical access hook for enhanced navigation
  const {
    accessInfo,
    isManager,
    hasHierarchicalAccess,
    canAccessAllData,
    loading: accessLoading,
    error: accessError
  } = useHierarchicalAccessRedux()

  // Get navigation based on enhanced or legacy configuration
  const navigation = useMemo(() => {
    if (useEnhancedNavigation) {
      return getEnhancedNavigationForRole(userRole, t)
    } else {
      return getNavigationForRole(userRole, t)
    }
  }, [userRole, t, useEnhancedNavigation])

  // Only log in development mode when props actually change
  const prevPropsRef = React.useRef({ isOpen, language, userRole })
  React.useEffect(() => {
    const prevProps = prevPropsRef.current
    const hasChanged = prevProps.isOpen !== isOpen ||
                      prevProps.language !== language ||
                      prevProps.userRole !== userRole

    if (process.env.NODE_ENV === 'development' && hasChanged) {
      console.log('Sidebar render - isOpen:', isOpen, 'language:', language, 'userRole:', userRole)
      console.log('Navigation items count:', navigation.length)
      if (navigation.length === 0) {
        console.warn('No navigation items found for role:', userRole)
      }
    }

    prevPropsRef.current = { isOpen, language, userRole }
  }, [isOpen, language, userRole, navigation.length])
  const isActiveRoute = useMemo(() => (href: string) => {
    if (href === '/' || href === '/admin/dashboard') {
      return location.pathname === '/' || location.pathname === '/admin/dashboard'
    }
    return location.pathname.startsWith(href)
  }, [location.pathname])

  // Enhanced navigation item renderer with hierarchical access features
  const renderEnhancedNavigationItem = (item: EnhancedNavigationItem, level = 0): void => {
    const Icon = item.icon
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = item.id ? expandedMenus.includes(item.id) : false
    const isActive = item.href ? isActiveRoute(item.href) : false

    // Show real-time status indicator for KPI sections
    const showRealTimeIndicator = item.realTimeUpdates && item.kpiCategory
    const showHierarchicalBadge = isManager && item.kpiCategory && hasHierarchicalAccess

    if (hasChildren) {
      return (<div key={item.id || item.name} className="space-y-1">
          <button
            onClick={(e: any) => {
              e.preventDefault()
              e.stopPropagation()
              if (item.id) {
                onToggleMenu(item.id)
              }
            }}
            aria-label={`${isExpanded ? 'إغلاق' : 'فتح'} قائمة ${item.name}`}
            aria-expanded={isExpanded}
            type="button"
            className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer ${
              isActive
                ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
                : 'text-white/80 hover:text-white hover:bg-white/10'
            }`}
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <div className="relative">
                <Icon className="h-5 w-5 flex-shrink-0" />
                {showRealTimeIndicator && (
                  <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                )}
              </div>
              <span className="truncate">
                {language === 'ar' && item.nameAr ? item.nameAr : item.name}
              </span>
              {showHierarchicalBadge && (
                <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full">
                  Manager
                </span>
              )}
              {item.badge && (<span className={`px-2 py-1 text-xs rounded-full ${
                  item.badge.color === 'green' ? 'bg-green-500/20 text-green-300' :
                  item.badge.color === 'blue' ? 'bg-blue-500/20 text-blue-300' :
                  item.badge.color === 'yellow' ? 'bg-yellow-500/20 text-yellow-300' :
                  'bg-red-500/20 text-red-300'
                }`}>
                  {item.badge.text || item.badge.count}
                </span>
              )}
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 flex-shrink-0" />
            ) : (
              <ChevronRight className="h-4 w-4 flex-shrink-0" />
            )}
          </button>

          {isExpanded && (<div className="ml-6 rtl:mr-6 rtl:ml-0 space-y-1">
              {item.children?.map(child => renderEnhancedNavigationItem(child, level + 1))}
            </div>
          )}
        </div>
      )
    }

    return (<Link
        key={item.name}
        to={item.href || '#'}
        onClick={onClose}
        className={`flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
          isActive
            ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
            : 'text-white/80 hover:text-white hover:bg-white/10'
        }`}
      >
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          <div className="relative">
            <Icon className="h-5 w-5 flex-shrink-0" />
            {showRealTimeIndicator && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            )}
          </div>
          <span className="truncate">
            {language === 'ar' && item.nameAr ? item.nameAr : item.name}
          </span>
        </div>
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          {showHierarchicalBadge && (
            <span className="px-2 py-1 text-xs bg-blue-500/20 text-blue-300 rounded-full">
              Manager
            </span>
          )}
          {item.badge && (<span className={`px-2 py-1 text-xs rounded-full ${
              item.badge.color === 'green' ? 'bg-green-500/20 text-green-300' :
              item.badge.color === 'blue' ? 'bg-blue-500/20 text-blue-300' :
              item.badge.color === 'yellow' ? 'bg-yellow-500/20 text-yellow-300' :
              'bg-red-500/20 text-red-300'
            }`}>
              {item.badge.text || item.badge.count}
            </span>
          )}
        </div>
      </Link>
    )
  }
  const renderNavigationItem = (item: NavigationItem, level = 0): void => {
    const Icon = item.icon
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = item.id ? expandedMenus.includes(item.id) : false
    const isActive = item.href ? isActiveRoute(item.href) : false

    if (hasChildren) {
      return (<div key={item.id || item.name} className="space-y-1">
          <button
            onClick={(e: any) => {
              // FIXED: Prevent event bubbling and ensure proper handling
              e.preventDefault()
              e.stopPropagation()
              if (item.id) {
                onToggleMenu(item.id)
              }
            }}
            aria-label={`${isExpanded ? 'إغلاق' : 'فتح'} قائمة ${item.name}`}
            aria-expanded={isExpanded}
            type="button"
            style={{ pointerEvents: 'auto' }} // FIXED: Ensure button is clickable
            className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer ${
              isActive
                ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
                : 'text-white/80 hover:text-white hover:bg-white/10'
            }`}
          >
            <div className="flex items-center space-x-3 rtl:space-x-reverse">
              <Icon className="h-5 w-5 flex-shrink-0" />
              <span className="truncate">{item.name}</span>
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 flex-shrink-0" />
            ) : (
              <ChevronRight className="h-4 w-4 flex-shrink-0" />
            )}
          </button>

          {isExpanded && (<div className="ml-6 rtl:mr-6 rtl:ml-0 space-y-1">
              {item.children?.map(child => renderNavigationItem(child, level + 1))}
            </div>
          )}
        </div>
      )
    }

    return (<Link
        key={item.href || item.name}
        to={item.href || '#'}
        onClick={(e: any) => {
          // FIXED: Prevent event bubbling and ensure proper navigation
          e.stopPropagation()
          if (onClose) {
            onClose()
          }
        }}
        style={{ pointerEvents: 'auto' }} // FIXED: Ensure link is clickable
        className={`flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer ${
          isActive
            ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border border-blue-500/30'
            : 'text-white/80 hover:text-white hover:bg-white/10'
        }`}
      >
        <Icon className="h-5 w-5 flex-shrink-0" />
        <span className="truncate">{item.name}</span>
      </Link>
    )
  }

  return (<>
      {/* FIXED: Sidebar with proper z-index hierarchy and pointer events */}
      <div
        className={`
          sidebar-container
          bg-gradient-to-b from-slate-900/95 to-purple-900/95 backdrop-blur-xl
          border-white/10
          ${language === 'ar' ? 'right-0 border-l' : 'left-0 border-r'}
          ${isOpen
            ? 'sidebar-open'
            : language === 'ar'
              ? 'sidebar-closed-rtl'
              : 'sidebar-closed-ltr'
          }
        `}
        style={{
          // FIXED: Proper pointer events management
          pointerEvents: isOpen ? 'auto' : 'none',
          visibility: isOpen ? 'visible' : 'hidden',
          zIndex: 50, // FIXED: Higher z-index to prevent interception
          position: 'fixed',
          top: 0,
          bottom: 0,
          width: '320px',
          transform: isOpen
            ? 'translateX(0)'
            : language === 'ar'
              ? 'translateX(100%)'
              : 'translateX(-100%)',
          transition: 'transform (0).3s ease-in-out, visibility (0).3s ease-in-out'
        }}
        // FIXED: Prevent event bubbling that causes interception
        onClick={(e: any) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-center h-16 px-6 border-b border-white/10">
          <div className={`flex items-center gap-3 ${language === 'ar' ? 'flex-row-reverse' : 'flex-row'}`}>
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">ن</span>
            </div>
            <span className="text-xl font-bold text-white">نمو</span>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto max-h-full">
          {/* Hierarchical Access Status */}
          {useEnhancedNavigation && accessInfo && (<div className="mb-4 p-3 bg-white/5 rounded-lg border border-white/10">
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-white/70">
                <Activity className="h-3 w-3" />
                <span>
                  {isManager ? 'Manager Access' : accessInfo.user_role}
                </span>
                {hasHierarchicalAccess && (<span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full">
                    {accessInfo.accessible_data.employees_count} employees
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Access Error Display */}
          {accessError && (
            <div className="mb-4 p-3 bg-red-500/10 rounded-lg border border-red-500/20">
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-xs text-red-300">
                <AlertTriangle className="h-3 w-3" />
                <span>{language === 'ar' ? 'معلومات الوصول غير متاحة' : 'Access info unavailable'}</span>
              </div>
            </div>
          )}

          {/* Navigation Items */}
          {useEnhancedNavigation
            ? navigation.map(item => renderEnhancedNavigationItem(item as EnhancedNavigationItem))
            : navigation.map(item => renderNavigationItem(item as NavigationItem))
          }
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-white/10">
          <div className="text-center text-xs text-white/50">
            نمو EMS v1.0.0
          </div>
        </div>
      </div>
    </>
  )
})

Sidebar.displayName = 'Sidebar'

export default Sidebar
