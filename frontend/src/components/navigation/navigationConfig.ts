/**
 * Navigation Configuration
 * Centralized navigation structure for different user roles
 */

import {
  Home,
  Users,
  Building,
  BarChart3,
  Settings,
  Calendar,
  Clock,
  TrendingUp,
  DollarSign,
  FolderOpen,
  Briefcase,
  Package,
  Truck,
  ShoppingCart,
  Monitor,
  Shield,
  Database,
  Key,
  Flag,
  Layers,
  Heart,
  Cpu,
  AlertTriangle,
  ToggleLeft,
  Plug,
  Server,
  Zap,
  Target,
  FileText,
  CreditCard,
  Receipt,
  Bell,
  Code,
  Plug,
  MessageSquare,
  Megaphone,
  FileText,
  Video,
  User,
  Boxes,
  ClipboardList,
  Workflow,
  Zap,
  Activity,
  Target,
  Server,
  Brain,
  Award,
  Headphones,
  HelpCircle,
  Star,
  Bot,
  type LucideIcon
} from 'lucide-react'

export interface NavigationItem {
  name: string
  icon: LucideIcon
  href?: string
  id?: string
  children?: NavigationItem[]
}

export interface NavigationConfig {
  [role: string]: NavigationItem[]
}

// Navigation translations interface
export interface NavigationTranslations {
  [key: string]: string
}

export const getRolePrefix = (role: string): string => {
  switch (role) {
    case 'super_admin': return '/superadmin'  // FIXED: Separate path for superadmin
    case 'admin': return '/admin'             // FIXED: Keep admin on /admin
    case 'hr_manager': return '/hr'
    case 'finance_manager': return '/finance'
    case 'sales_manager': return '/sales'
    case 'department_manager': return '/department'
    case 'employee': return '/employee'
    default: return '/employee'
  }
}

export const createNavigationConfig = (t: NavigationTranslations): NavigationConfig => ({
  super_admin: [
    { name: t.dashboard, icon: Home, href: '/superadmin/dashboard' },
    { name: 'System Overview', icon: Monitor, href: '/superadmin/system' },
    { name: 'User Management', icon: Users, href: '/superadmin/users' },
    { name: 'Organization Management', icon: Building, href: '/superadmin/organizations' },
    {
      name: 'System Administration',
      icon: Settings,
      id: 'system-admin',
      children: [
        { name: 'Security Center', icon: Shield, href: '/superadmin/security' },
        { name: 'System Settings', icon: Settings, href: '/superadmin/settings' },
        { name: 'Audit Logs', icon: FileText, href: '/superadmin/audit' },
        { name: 'Backup & Recovery', icon: Database, href: '/superadmin/backup' },
      ]
    },
    {
      name: 'Global Analytics',
      icon: BarChart3,
      id: 'global-analytics',
      children: [
        { name: 'System Performance', icon: Activity, href: '/superadmin/performance' },
        { name: 'Usage Analytics', icon: TrendingUp, href: '/superadmin/analytics' },
        { name: 'Business Intelligence', icon: BarChart3, href: '/admin/business-intelligence' },
      ]
    },
    {
      name: 'Enterprise Management',
      icon: Building,
      id: 'enterprise',
      children: [
        { name: 'Multi-Tenant Management', icon: Building, href: '/superadmin/tenants' },
        { name: 'License Management', icon: Key, href: '/superadmin/licenses' },
        { name: 'Feature Flags', icon: Flag, href: '/superadmin/features' },
      ]
    },
    {
      name: 'Advanced System Control',
      icon: Server,
      id: 'advanced-system',
      children: [
        { name: 'System Monitoring', icon: Monitor, href: '/superadmin/monitoring' },
        { name: 'Database Management', icon: Database, href: '/superadmin/database' },
        { name: 'API Management', icon: Code, href: '/superadmin/api' },
        { name: 'Integration Hub', icon: Plug, href: '/superadmin/integrations' },
      ]
    },
    {
      name: 'Platform Management',
      icon: Layers,
      id: 'platform',
      children: [
        { name: 'Service Health', icon: Heart, href: '/superadmin/health' },
        { name: 'Resource Usage', icon: Cpu, href: '/superadmin/resources' },
        { name: 'Error Tracking', icon: AlertTriangle, href: '/superadmin/errors' },
      ]
    },
    {
      name: 'Global Configuration',
      icon: Settings,
      id: 'global-config',
      children: [
        { name: 'Global Settings', icon: Settings, href: '/superadmin/global-settings' },
        { name: 'Environment Config', icon: Server, href: '/superadmin/environment' },
        { name: 'Feature Management', icon: ToggleLeft, href: '/superadmin/features' },
      ]
    },
    {
      name: 'Super Admin Profile',
      icon: User,
      id: 'superadmin-profile',
      children: [
        { name: 'My Profile', icon: User, href: '/superadmin/profile' },
        { name: 'Admin Logs', icon: FileText, href: '/superadmin/logs' },
        { name: 'System Notifications', icon: Bell, href: '/superadmin/notifications' },
      ]
    },
  ],

  admin: [
    { name: t.dashboard, icon: Home, href: '/admin/dashboard' },
    { name: t.employees, icon: Users, href: '/admin/employees' },
    { name: t.departments, icon: Building, href: '/admin/departments' },
    { name: t.assets, icon: Package, href: '/admin/assets' },
    {
      name: t.hrManagement,
      icon: Users,
      id: 'hr',
      children: [
        { name: t.leaveManagement, icon: Calendar, href: '/admin/hr/leave' },
        { name: t.attendance, icon: Clock, href: '/admin/hr/attendance' },
        { name: t.performance, icon: TrendingUp, href: '/admin/hr/performance' },
        { name: t.payroll, icon: DollarSign, href: '/admin/hr/payroll' },
      ]
    },
    {
      name: t.sales,
      icon: ShoppingCart,
      id: 'sales',
      children: [
        { name: t.salesOrders, icon: ShoppingCart, href: '/admin/sales/orders' },
        { name: t.quotations, icon: FileText, href: '/admin/sales/quotations' },
        { name: t.salesPipeline, icon: TrendingUp, href: '/admin/sales/pipeline' },
      ]
    },
    {
      name: t.inventory,
      icon: Package,
      id: 'inventory',
      children: [
        { name: t.products, icon: Package, href: '/admin/products' },
        { name: t.inventory, icon: Boxes, href: '/admin/inventory' },
      ]
    },
    {
      name: t.analytics,
      icon: BarChart3,
      id: 'analytics',
      children: [
        { name: t.businessIntelligence, icon: TrendingUp, href: '/admin/business-intelligence' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.messages, icon: MessageSquare, href: '/admin/communication/messages' },
        { name: t.announcements, icon: Megaphone, href: '/admin/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/admin/communication/documents' },
        { name: t.meetings, icon: Video, href: '/admin/communication/meetings' },
      ]
    },
    { name: t.reports, icon: BarChart3, href: '/admin/reports' },
    { name: t.settings, icon: Settings, href: '/admin/settings' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/admin/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/admin/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/admin/calendar' },
      ]
    },
  ],

  hr_manager: [
    { name: t.dashboard, icon: Home, href: '/hr/dashboard' },
    { name: t.employees, icon: Users, href: '/hr/employees' },
    { name: t.departments, icon: Building, href: '/hr/departments' },
    {
      name: t.hrManagement,
      icon: Users,
      id: 'hr',
      children: [
        { name: t.leaveManagement, icon: Calendar, href: '/hr/leave' },
        { name: t.attendance, icon: Clock, href: '/hr/attendance' },
        { name: t.performance, icon: TrendingUp, href: '/hr/performance' },
        { name: t.payroll, icon: DollarSign, href: '/hr/payroll' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.messages, icon: MessageSquare, href: '/hr/communication/messages' },
        { name: t.announcements, icon: Megaphone, href: '/hr/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/hr/communication/documents' },
        { name: t.meetings, icon: Video, href: '/hr/communication/meetings' },
      ]
    },
    { name: t.reports, icon: BarChart3, href: '/hr/reports' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/hr/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/hr/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/hr/calendar' },
      ]
    },
  ],

  sales_manager: [
    { name: t.dashboard, icon: Home, href: '/sales/dashboard' },
    { name: t.customers, icon: Users, href: '/sales/customers' },
    {
      name: t.sales,
      icon: ShoppingCart,
      id: 'sales',
      children: [
        { name: t.salesOrders, icon: ShoppingCart, href: '/sales/orders' },
        { name: t.quotations, icon: FileText, href: '/sales/quotations' },
        { name: t.salesPipeline, icon: TrendingUp, href: '/sales/pipeline' },
      ]
    },
    { name: t.products, icon: Package, href: '/sales/products' },
    { name: t.analytics, icon: BarChart3, href: '/sales/analytics' },
    { name: t.reports, icon: BarChart3, href: '/sales/reports' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/sales/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/sales/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/sales/calendar' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/sales/communication/announcements' },
      ]
    },
  ],

  finance_manager: [
    { name: t.dashboard, icon: Home, href: '/finance/dashboard' },
    {
      name: t.financialManagement,
      icon: DollarSign,
      id: 'finance',
      children: [
        { name: 'General Ledger', icon: FileText, href: '/finance/general-ledger' },
        { name: 'Chart of Accounts', icon: BarChart3, href: '/finance/chart-of-accounts' },
        { name: t.budgets, icon: DollarSign, href: '/finance/budgets' },
        { name: t.expenses, icon: TrendingUp, href: '/finance/expenses' },
        { name: t.financialReports, icon: BarChart3, href: '/finance/reports' },
      ]
    },
    {
      name: 'Accounts Payable',
      icon: CreditCard,
      id: 'accounts-payable',
      children: [
        { name: 'Vendors', icon: Users, href: '/finance/vendors' },
        { name: 'Vendor Invoices', icon: FileText, href: '/finance/vendor-invoices' },
        { name: 'Payments', icon: DollarSign, href: '/finance/payments' },
      ]
    },
    {
      name: 'Accounts Receivable',
      icon: Receipt,
      id: 'accounts-receivable',
      children: [
        { name: 'Customer Invoices', icon: FileText, href: '/finance/customer-invoices' },
        { name: 'Collections', icon: DollarSign, href: '/finance/collections' },
        { name: 'Aging Reports', icon: BarChart3, href: '/finance/aging-reports' },
      ]
    },
    {
      name: t.assetManagement,
      icon: Package,
      id: 'assets',
      children: [
        { name: t.assets, icon: Package, href: '/finance/assets' },
        { name: t.suppliers, icon: Truck, href: '/finance/suppliers' },
        { name: t.purchaseOrders, icon: ShoppingCart, href: '/finance/purchase-orders' },
      ]
    },
    { name: t.inventory, icon: Boxes, href: '/finance/inventory' },
    {
      name: t.analyticsReporting,
      icon: BarChart3,
      id: 'analytics',
      children: [
        { name: t.executiveDashboard, icon: TrendingUp, href: '/finance/analytics/executive' },
        { name: t.kpiManagement, icon: Target, href: '/finance/analytics/kpi' },
        { name: t.reportsManagement, icon: FileText, href: '/finance/analytics/reports' },
      ]
    },
    {
      name: t.integrationApi,
      icon: Plug,
      id: 'integrations',
      children: [
        { name: t.apiManagement, icon: Key, href: '/finance/integrations/api' },
        { name: t.externalServices, icon: Server, href: '/finance/integrations/services' },
        { name: t.webhookManagement, icon: Zap, href: '/finance/integrations/webhooks' },
      ]
    },
    {
      name: t.securityCompliance,
      icon: Shield,
      id: 'security',
      children: [
        { name: t.securityDashboard, icon: Shield, href: '/finance/security/dashboard' },
        { name: t.securityIncidents, icon: AlertTriangle, href: '/finance/security/incidents' },
        { name: t.complianceManagement, icon: FileText, href: '/finance/security/compliance' },
      ]
    },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/finance/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/finance/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/finance/calendar' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/finance/communication/announcements' },
      ]
    },
  ],

  department_manager: [
    { name: t.dashboard, icon: Home, href: '/department/dashboard' },
    {
      name: t.projectManagement,
      icon: Briefcase,
      id: 'projects',
      children: [
        { name: t.projects, icon: FolderOpen, href: '/department/projects' },
        { name: t.tasks, icon: BarChart3, href: '/department/tasks' },
        { name: t.projectReports, icon: BarChart3, href: '/department/reports' },
      ]
    },
    { name: t.departments, icon: Building, href: '/department/departments' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/department/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/department/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/department/calendar' },
      ]
    },
    {
      name: t.communication,
      icon: MessageSquare,
      id: 'communication',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/department/communication/announcements' },
      ]
    },
  ],

  employee: [
    { name: t.dashboard, icon: Home, href: '/employee/dashboard' },
    {
      name: t.personalSpace,
      icon: User,
      id: 'personal',
      children: [
        { name: t.myProfile, icon: User, href: '/employee/profile' },
        { name: t.myMessages, icon: MessageSquare, href: '/employee/messages' },
        { name: t.myCalendar, icon: Calendar, href: '/employee/calendar' },
      ]
    },
    {
      name: t.myWork,
      icon: Briefcase,
      id: 'work',
      children: [
        { name: t.employeeLeave, icon: Calendar, href: '/employee/leave' },
        { name: t.myTasks, icon: ClipboardList, href: '/employee/tasks' },
        { name: t.assignedProjects, icon: FolderOpen, href: '/employee/projects' },
      ]
    },
    {
      name: t.companyInfo,
      icon: Building,
      id: 'company',
      children: [
        { name: t.announcements, icon: Megaphone, href: '/employee/communication/announcements' },
        { name: t.documents, icon: FileText, href: '/employee/communication/documents' },
      ]
    },
  ],
})

export const getNavigationForRole = (userRole: string, t: NavigationTranslations): NavigationItem[] => {
  const config = createNavigationConfig(t)

  // Check if the role exists in the configuration
  if (config[userRole]) {
    return config[userRole]
  }

  // Log warning for unknown roles in development
  if (process.env.NODE_ENV === 'development') {
    console.warn(`Navigation: Unknown user role '${userRole}', falling back to employee navigation`)
  }

  // Fallback to employee navigation
  return config.employee || []
}
