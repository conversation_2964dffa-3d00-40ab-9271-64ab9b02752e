import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Users, 
  UserCheck, 
  UserX, 
  Mail, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  RefreshCw
} from 'lucide-react'
import { employeeAPI, Employee } from '@/services/employeeAPI'

interface OnboardingStats {
  totalEmployees: number
  activatedEmployees: number
  pendingActivation: number
  emailsSent: number
}
const EmployeeOnboardingStatus: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [stats, setStats] = useState<OnboardingStats>({
    totalEmployees: 0,
    activatedEmployees: 0,
    pendingActivation: 0,
    emailsSent: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  useEffect(() => {
    loadEmployeeData()
  }, [])

  const loadEmployeeData = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await employeeAPI.getAll()
      const employeeData = response.data
      
      setEmployees(employeeData)
      
      // Calculate stats
      const totalEmployees = employeeData.length
      const activatedEmployees = employeeData.filter(emp => emp.isAccountActivated).length
      const pendingActivation = totalEmployees - activatedEmployees
      const emailsSent = employeeData.filter(emp => emp.activationEmailSent).length
      
      setStats({
        totalEmployees,
        activatedEmployees,
        pendingActivation,
        emailsSent
      })
      
    } catch (error) {
      console.error('Error loading employee data:', error)
      setError('حدث خطأ أثناء تحميل بيانات الموظفين')
    } finally {
      setLoading(false)
    }
  }

  const getPendingEmployees = (): void => {
    return employees.filter(emp => !emp.isAccountActivated)
  }

  const getRecentlyActivated = (): void => {
    return employees
      .filter(emp => emp.isAccountActivated)
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 5)
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          <span>جاري تحميل بيانات التفعيل...</span>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  return (<div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                <p className="text-2xl font-bold">{stats.totalEmployees}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الحسابات المفعلة</p>
                <p className="text-2xl font-bold text-green-600">{stats.activatedEmployees}</p>
              </div>
              <UserCheck className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">في انتظار التفعيل</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pendingActivation}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الإيميلات المرسلة</p>
                <p className="text-2xl font-bold text-blue-600">{stats.emailsSent}</p>
              </div>
              <Mail className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Onboarding Workflow */}
      <Card>
        <CardHeader>
          <CardTitle>سير عمل تفعيل الموظفين</CardTitle>
          <CardDescription>
            العملية المبسطة لتفعيل حسابات الموظفين الجدد
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                <span className="mr-2">المدير ينشئ موظف جديد</span>
              </div>
              <div className="text-gray-400">←</div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                <span className="mr-2">إرسال إيميل التفعيل تلقائياً</span>
              </div>
              <div className="text-gray-400">←</div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                <span className="mr-2">الموظف يفعل حسابه</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pending Activations */}
      {stats.pendingActivation > 0 && (<Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserX className="h-5 w-5 text-yellow-600" />
              الموظفون في انتظار التفعيل ({stats.pendingActivation})
            </CardTitle>
            <CardDescription>
              الموظفون الذين لم يفعلوا حساباتهم بعد
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {getPendingEmployees().map((employee) => (<div key={employee.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium">
                        {employee.firstName.charAt(0)}{employee.lastName.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium">{employee.name}</p>
                      <p className="text-sm text-gray-600">{employee.position} - {employee.department}</p>
                      <p className="text-xs text-gray-500">{employee.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                      في انتظار التفعيل
                    </Badge>
                    {employee.activationEmailSent && (
                      <Badge variant="outline" className="text-blue-600 border-blue-200">
                        تم إرسال الإيميل
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recently Activated */}
      {stats.activatedEmployees > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              آخر الموظفين المفعلين
            </CardTitle>
            <CardDescription>
              الموظفون الذين فعلوا حساباتهم مؤخراً
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {getRecentlyActivated().map((employee) => (<div key={employee.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">{employee.name}</p>
                      <p className="text-sm text-gray-600">{employee.position} - {employee.department}</p>
                      <p className="text-xs text-gray-500">{employee.email}</p>
                    </div>
                  </div>
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    مفعل
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Refresh Button */}
      <div className="flex justify-center">
        <Button onClick={loadEmployeeData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          تحديث البيانات
        </Button>
      </div>
    </div>
  )
}

export default EmployeeOnboardingStatus
