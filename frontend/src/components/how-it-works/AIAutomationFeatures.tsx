import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import {
  Brain,
  Zap,
  Target,
  TrendingUp,
  AlertTriangle,
  MessageSquare,
  BarChart3,
  Users,
  Calendar,
  FileText,
  Settings,
  Eye,
  Lightbulb,
  Workflow,
  Bot,
  Search,
  Shield,
  Clock,
  CheckCircle
} from 'lucide-react'

interface AIAutomationFeaturesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'الذكاء الاصطناعي والأتمتة',
    subtitle: 'تقنيات ذكية لتحسين الكفاءة والإنتاجية',
    description: 'استفد من قوة الذكاء الاصطناعي والأتمتة لتحسين عمليات مؤسستك وزيادة الإنتاجية',

    // AI Categories
    predictiveAnalytics: 'التحليلات التنبؤية',
    intelligentAutomation: 'الأتمتة الذكية',
    naturalLanguage: 'معالجة اللغة الطبيعية',
    anomalyDetection: 'كشف الشذوذ',

    // Features
    features: 'الميزات',
    benefits: 'الفوائد',
    useCases: 'حالات الاستخدام',

    // AI Features
    employeeTurnover: 'توقع دوران الموظفين',
    salesForecasting: 'توقع المبيعات',
    budgetOptimization: 'تحسين الميزانية',
    riskAssessment: 'تقييم المخاطر',

    workflowAutomation: 'أتمتة سير العمل',
    documentProcessing: 'معالجة المستندات',
    emailAutomation: 'أتمتة البريد الإلكتروني',
    reportGeneration: 'إنتاج التقارير التلقائي',

    chatbot: 'روبوت المحادثة',
    sentimentAnalysis: 'تحليل المشاعر',
    documentSearch: 'البحث في المستندات',
    voiceCommands: 'الأوامر الصوتية',

    fraudDetection: 'كشف الاحتيال',
    performanceAnomalies: 'شذوذ الأداء',
    securityThreats: 'التهديدات الأمنية',
    dataQuality: 'جودة البيانات',

    // Benefits
    increasedEfficiency: 'زيادة الكفاءة',
    reducedErrors: 'تقليل الأخطاء',
    costSavings: 'توفير التكاليف',
    betterDecisions: 'قرارات أفضل',
    improvedCustomerService: 'تحسين خدمة العملاء',
    fasterProcessing: 'معالجة أسرع'
  },
  en: {
    title: 'AI & Automation',
    subtitle: 'Smart technologies to improve efficiency and productivity',
    description: 'Leverage the power of AI and automation to enhance your organization processes and increase productivity',

    // AI Categories
    predictiveAnalytics: 'Predictive Analytics',
    intelligentAutomation: 'Intelligent Automation',
    naturalLanguage: 'Natural Language Processing',
    anomalyDetection: 'Anomaly Detection',

    // Features
    features: 'Features',
    benefits: 'Benefits',
    useCases: 'Use Cases',

    // AI Features
    employeeTurnover: 'Employee Turnover Prediction',
    salesForecasting: 'Sales Forecasting',
    budgetOptimization: 'Budget Optimization',
    riskAssessment: 'Risk Assessment',

    workflowAutomation: 'Workflow Automation',
    documentProcessing: 'Document Processing',
    emailAutomation: 'Email Automation',
    reportGeneration: 'Automatic Report Generation',

    chatbot: 'AI Chatbot',
    sentimentAnalysis: 'Sentiment Analysis',
    documentSearch: 'Document Search',
    voiceCommands: 'Voice Commands',

    fraudDetection: 'Fraud Detection',
    performanceAnomalies: 'Performance Anomalies',
    securityThreats: 'Security Threats',
    dataQuality: 'Data Quality Issues',

    // Benefits
    increasedEfficiency: 'Increased Efficiency',
    reducedErrors: 'Reduced Errors',
    costSavings: 'Cost Savings',
    betterDecisions: 'Better Decisions',
    improvedCustomerService: 'Improved Customer Service',
    fasterProcessing: 'Faster Processing'
  }
}

export default function AIAutomationFeatures({ language }: AIAutomationFeaturesProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  const aiCategories = {
    predictiveAnalytics: {
      title: t.predictiveAnalytics,
      icon: TrendingUp,
      color: 'from-blue-500 to-cyan-500',
      features: [
        {
          name: t.employeeTurnover,
          icon: Users,
          description: language === 'ar'
            ? 'توقع احتمالية ترك الموظفين للعمل بناءً على البيانات التاريخية'
            : 'Predict employee turnover probability based on historical data',
          accuracy: '92%',
          benefits: [t.reducedErrors, t.betterDecisions]
        },
        {
          name: t.salesForecasting,
          icon: BarChart3,
          description: language === 'ar'
            ? 'توقع المبيعات المستقبلية وتحديد الاتجاهات'
            : 'Forecast future sales and identify trends',
          accuracy: '87%',
          benefits: [t.betterDecisions, t.increasedEfficiency]
        },
        {
          name: t.budgetOptimization,
          icon: Target,
          description: language === 'ar'
            ? 'تحسين توزيع الميزانية بناءً على الأداء التاريخي'
            : 'Optimize budget allocation based on historical performance',
          accuracy: '89%',
          benefits: [t.costSavings, t.betterDecisions]
        }
      ]
    },
    intelligentAutomation: {
      title: t.intelligentAutomation,
      icon: Workflow,
      color: 'from-green-500 to-emerald-500',
      features: [
        {
          name: t.workflowAutomation,
          icon: Workflow,
          description: language === 'ar'
            ? 'أتمتة العمليات المعقدة وسير العمل'
            : 'Automate complex processes and workflows',
          accuracy: '95%',
          benefits: [t.increasedEfficiency, t.reducedErrors]
        },
        {
          name: t.documentProcessing,
          icon: FileText,
          description: language === 'ar'
            ? 'معالجة وتصنيف المستندات تلقائياً'
            : 'Automatically process and classify documents',
          accuracy: '91%',
          benefits: [t.fasterProcessing, t.reducedErrors]
        },
        {
          name: t.reportGeneration,
          icon: BarChart3,
          description: language === 'ar'
            ? 'إنتاج التقارير تلقائياً بناءً على البيانات'
            : 'Automatically generate reports based on data',
          accuracy: '98%',
          benefits: [t.fasterProcessing, t.increasedEfficiency]
        }
      ]
    },
    naturalLanguage: {
      title: t.naturalLanguage,
      icon: MessageSquare,
      color: 'from-purple-500 to-pink-500',
      features: [
        {
          name: t.chatbot,
          icon: Bot,
          description: language === 'ar'
            ? 'مساعد ذكي للإجابة على استفسارات الموظفين والعملاء'
            : 'Smart assistant to answer employee and customer queries',
          accuracy: '94%',
          benefits: [t.improvedCustomerService, t.fasterProcessing]
        },
        {
          name: t.sentimentAnalysis,
          icon: Eye,
          description: language === 'ar'
            ? 'تحليل مشاعر العملاء من التعليقات والرسائل'
            : 'Analyze customer sentiment from feedback and messages',
          accuracy: '88%',
          benefits: [t.improvedCustomerService, t.betterDecisions]
        },
        {
          name: t.documentSearch,
          icon: Search,
          description: language === 'ar'
            ? 'البحث الذكي في المستندات باللغة الطبيعية'
            : 'Smart document search using natural language',
          accuracy: '93%',
          benefits: [t.fasterProcessing, t.increasedEfficiency]
        }
      ]
    },
    anomalyDetection: {
      title: t.anomalyDetection,
      icon: AlertTriangle,
      color: 'from-red-500 to-orange-500',
      features: [
        {
          name: t.fraudDetection,
          icon: Shield,
          description: language === 'ar'
            ? 'كشف المعاملات المشبوهة والاحتيال المالي'
            : 'Detect suspicious transactions and financial fraud',
          accuracy: '96%',
          benefits: [t.reducedErrors, t.costSavings]
        },
        {
          name: t.performanceAnomalies,
          icon: TrendingUp,
          description: language === 'ar'
            ? 'كشف الانحرافات في أداء الموظفين والعمليات'
            : 'Detect deviations in employee and process performance',
          accuracy: '90%',
          benefits: [t.betterDecisions, t.increasedEfficiency]
        },
        {
          name: t.securityThreats,
          icon: Shield,
          description: language === 'ar'
            ? 'كشف التهديدات الأمنية والوصول غير المصرح به'
            : 'Detect security threats and unauthorized access',
          accuracy: '97%',
          benefits: [t.reducedErrors, t.betterDecisions]
        }
      ]
    }
  }

  const overallBenefits = [
    {
      title: t.increasedEfficiency,
      description: language === 'ar' ? 'زيادة الكفاءة بنسبة تصل إلى 40%' : 'Increase efficiency by up to 40%',
      icon: Zap,
      percentage: '40%'
    },
    {
      title: t.reducedErrors,
      description: language === 'ar' ? 'تقليل الأخطاء البشرية بنسبة 85%' : 'Reduce human errors by 85%',
      icon: CheckCircle,
      percentage: '85%'
    },
    {
      title: t.costSavings,
      description: language === 'ar' ? 'توفير في التكاليف يصل إلى 30%' : 'Cost savings up to 30%',
      icon: Target,
      percentage: '30%'
    },
    {
      title: t.fasterProcessing,
      description: language === 'ar' ? 'معالجة أسرع بـ 5 مرات' : '5x faster processing',
      icon: Clock,
      percentage: '5x'
    }
  ]

  return (
    <div className={`space-y-8 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-4xl font-bold text-white mb-4">{t.title}</h2>
        <p className="text-xl text-white/80 mb-2">{t.subtitle}</p>
        <p className="text-white/70 max-w-3xl mx-auto">{t.description}</p>
      </div>

      {/* Overall Benefits */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {overallBenefits.map((benefit, index) => {
          const Icon = benefit.icon
          return (
            <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 text-center">
              <CardContent className="p-6">
                <Icon className="h-8 w-8 text-white/80 mx-auto mb-3" />
                <div className="text-3xl font-bold text-white mb-2">{benefit.percentage}</div>
                <h4 className="text-white font-semibold mb-1">{benefit.title}</h4>
                <p className="text-white/70 text-sm">{benefit.description}</p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* AI Categories */}
      <div className="space-y-8">
        {Object.entriesaiCategories.map(([categoryKey, category]) => {
          const CategoryIcon = category.icon
          return (
            <div key={categoryKey}>
              <div className="flex items-center gap-3 mb-6">
                <div className={`w-10 h-10 bg-gradient-to-r ${category.color} rounded-lg flex items-center justify-center`}>
                  <CategoryIcon className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">{category.title}</h3>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {category.features.map((feature, index) => {
                  const Icon = feature.icon
                  return (
                    <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group">
                      <CardHeader>
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 bg-gradient-to-r ${category.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                            <Icon className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <CardTitle className="text-white text-lg">{feature.name}</CardTitle>
                            <Badge className="bg-green-500/20 text-green-400 border-green-500/30 mt-1">
                              {feature.accuracy} {language === 'ar' ? 'دقة' : 'Accuracy'}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-white/70 text-sm mb-4">{feature.description}</p>
                        <div className="space-y-2">
                          <h5 className="text-white font-semibold text-sm">{t.benefits}:</h5>
                          {feature.benefits.map((benefit, benefitIndex) => (
                            <div key={benefitIndex} className="flex items-center gap-2">
                              <CheckCircle className="h-3 w-3 text-green-400" />
                              <span className="text-white/80 text-xs">{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>

      {/* AI Implementation Process */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-center">
            {language === 'ar' ? 'عملية تطبيق الذكاء الاصطناعي' : 'AI Implementation Process'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[
              {
                step: 1,
                title: language === 'ar' ? 'تحليل البيانات' : 'Data Analysis',
                description: language === 'ar' ? 'تحليل بيانات مؤسستك' : 'Analyze your organization data'
              },
              {
                step: 2,
                title: language === 'ar' ? 'تدريب النماذج' : 'Model Training',
                description: language === 'ar' ? 'تدريب نماذج الذكاء الاصطناعي' : 'Train AI models'
              },
              {
                step: 3,
                title: language === 'ar' ? 'التطبيق' : 'Implementation',
                description: language === 'ar' ? 'تطبيق الحلول الذكية' : 'Implement smart solutions'
              },
              {
                step: 4,
                title: language === 'ar' ? 'التحسين المستمر' : 'Continuous Improvement',
                description: language === 'ar' ? 'تحسين الأداء باستمرار' : 'Continuously improve performance'
              }
            ].map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-white font-bold">{step.step}</span>
                </div>
                <h4 className="text-white font-semibold mb-2">{step.title}</h4>
                <p className="text-white/70 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
