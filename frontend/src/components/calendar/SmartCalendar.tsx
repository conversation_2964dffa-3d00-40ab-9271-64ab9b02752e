import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  ChevronLeft,
  ChevronRight,
  Plus,
  Clock,
  Users,
  MapPin,
  Video,
  Bell,
  Filter,
  MoreVertical
} from 'lucide-react'

interface SmartCalendarProps {
  language: 'ar' | 'en'
  userRole: string
}

interface CalendarEvent {
  id: string
  title: string
  description?: string
  startTime: Date
  endTime: Date
  type: 'meeting' | 'deadline' | 'leave' | 'training' | 'personal'
  attendees?: string[]
  location?: string
  isOnline?: boolean
  priority: 'low' | 'medium' | 'high'
  color: string
  isAllDay?: boolean
}

const translations: any = {
  ar: {
    calendar: 'التقويم',
    today: 'اليوم',
    month: 'شهر',
    week: 'أسبوع',
    day: 'يوم',
    addEvent: 'إضافة حدث',
    filter: 'تصفية',
    meeting: 'اجتماع',
    deadline: 'موعد نهائي',
    leave: 'إجازة',
    training: 'تدريب',
    personal: 'شخصي',
    allDay: 'طوال اليوم',
    attendees: 'الحضور',
    location: 'الموقع',
    online: 'عبر الإنترنت',
    noEvents: 'لا توجد أحداث',
    viewDetails: 'عرض التفاصيل',
    editEvent: 'تعديل الحدث',
    deleteEvent: 'حذف الحدث',
    upcoming: 'القادمة',
    past: 'السابقة'
  },
  en: {
    calendar: 'Calendar',
    today: 'Today',
    month: 'Month',
    week: 'Week',
    day: 'Day',
    addEvent: 'Add Event',
    filter: 'Filter',
    meeting: 'Meeting',
    deadline: 'Deadline',
    leave: 'Leave',
    training: 'Training',
    personal: 'Personal',
    allDay: 'All Day',
    attendees: 'Attendees',
    location: 'Location',
    online: 'Online',
    noEvents: 'No events',
    viewDetails: 'View Details',
    editEvent: 'Edit Event',
    deleteEvent: 'Delete Event',
    upcoming: 'Upcoming',
    past: 'Past'
  }
}
export default function SmartCalendar({ language, userRole }: SmartCalendarProps): React.ReactElement {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [view, setView] = useState<'month' | 'week' | 'day'>('month')
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [filterType, setFilterType] = useState<string>('all')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Load real events from API
  useEffect(() => {
    const loadEvents = async () => {
      try {
        // TODO: Replace with real calendar/events API when available
        // For now, return empty events array instead of mock data
        setEvents([])
      } catch (error) {
        console.error('Error loading calendar events:', error)
        setEvents([])
      }
    }
    loadEvents()
  }, [language])

  const getEventTypeIcon = (type: string): void => {
    switch (type) {
      case 'meeting': return <Users className="h-3 w-3" />
      case 'deadline': return <Clock className="h-3 w-3" />
      case 'leave': return <Calendar className="h-3 w-3" />
      case 'training': return <Bell className="h-3 w-3" />
      case 'personal': return <Users className="h-3 w-3" />
      default: return <Calendar className="h-3 w-3" />
    }
  }

  const formatTime = (date: Date): string => {
    return (date).toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDate = (date: Date): string => {
    return (date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getDaysInMonth = (date: Date): void => {
    const year = (date).getFullYear()
    const month = (date).getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = (lastDay).getDate()
    const startingDayOfWeek = (firstDay).getDay()

    const days = []
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      (days).push(null)
    }
    
    // Add days of the month
    for (let day: any = 1; day <= daysInMonth; day++) {
      (days).push(new Date(year, month, day))
    }
    
    return days
  }

  const getEventsForDate = (date: Date): void => {
    return (events).filter(event => {
      const eventDate = new Date((event).startTime)
      return (eventDate).toDateString() === (date).toDateString()
    })
  }

  const filteredEvents = (events).filter(event => {
    if (filterType === 'all') return true
    return (event).type === filterType
  })

  const navigateMonth = (direction: 'prev' | 'next'): void => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        (newDate).setMonth((prev).getMonth() - 1)
      } else {
        (newDate).setMonth((prev).getMonth() + 1)
      }
      return newDate
    })
  }

  const goToToday = (): void => {
    setCurrentDate(new Date())
  }

  const days = getDaysInMonth(currentDate)
  const monthName = (currentDate).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US', {
    month: 'long',
    year: 'numeric'
  })

  return (<div className="space-y-6">
      {/* Calendar Header */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {(t).calendar}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={goToToday}
                className="glass-button"
              >
                {(t).today}
              </Button>
              <div className="flex items-center gap-1">
                {['month', 'week', 'day'].map((viewType) => (
                  <Button
                    key={viewType}
                    variant={view === viewType ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setView(viewType)}
                    className={`glass-button ${view === viewType ? 'bg-blue-500/30' : ''}`}
                  >
                    {t[viewType as keyof typeof t]}
                  </Button>
                ))}
              </div>
              <Button className="glass-button">
                <Plus className="h-4 w-4 mr-2" />
                {(t).addEvent}
              </Button>
            </div>
          </div>

          {/* Month Navigation */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateMonth('prev')}
                className="glass-button"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-white text-lg font-semibold">{monthName}</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateMonth('next')}
                className="glass-button"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Event Type Filter */}
            <div className="flex items-center gap-2">
              <span className="text-white/70 text-sm">{(t).filter}:</span>
              {['all', 'meeting', 'deadline', 'leave', 'training', 'personal'].map((type) => (
                <Button
                  key={type}
                  variant={filterType === type ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setFilterType(type)}
                  className={`glass-button text-xs ${
                    filterType === type ? 'bg-purple-500/30' : ''
                  }`}
                >
                  {getEventTypeIcon(type)}
                  <span className="ml-1">{t[type as keyof typeof t]}</span>
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Calendar Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Calendar View */}
        <div className="lg:col-span-3">
          <Card className="glass-card border-white/20">
            <CardContent className="p-6">
              {view === 'month' && (<div className="space-y-4">
                  {/* Day Headers */}
                  <div className="grid grid-cols-7 gap-2">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                      <div key={day} className="text-center text-white/70 text-sm font-medium p-2">
                        {day}
                      </div>
                    ))}
                  </div>

                  {/* Calendar Days */}
                  <div className="grid grid-cols-7 gap-2">
                    {(days).map((day, index) => {
                      if (!day) {
                        return <div key={index} className="h-24"></div>
                      }

                      const dayEvents = getEventsForDate(day)
                      const isToday = (day).toDateString() === new Date().toDateString()
                      const isSelected = selectedDate?.toDateString() === (day).toDateString()

                      return (
                        <div
                          key={index}
                          onClick={() => setSelectedDate(day)}
                          className={`h-24 p-2 border border-white/10 rounded-lg cursor-pointer transition-all duration-300 hover:border-white/30 ${
                            isToday ? 'bg-blue-500/20 border-blue-500/50' : ''
                          } ${
                            isSelected ? 'bg-purple-500/20 border-purple-500/50' : ''
                          }`}
                        >
                          <div className="text-white text-sm font-medium mb-1">
                            {(day).getDate()}
                          </div>
                          <div className="space-y-1">
                            {(dayEvents).slice(0, 2).map((event) => (<div
                                key={(event).id}
                                className={`text-xs text-white px-1 py-(0).5 rounded ${(event).color} truncate`}
                              >
                                {(event).title}
                              </div>
                            ))}
                            {(dayEvents).length > 2 && (<div className="text-xs text-white/60">
                                +{(dayEvents).length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Event List Sidebar */}
        <div className="space-y-4">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-lg">
                {selectedDate ? formatDate(selectedDate) : (t).upcoming}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {(selectedDate ? getEventsForDate(selectedDate) : (filteredEvents).slice(0, 5)).length === 0 ? (<div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-white/30 mx-auto mb-3" />
                    <p className="text-white/60">{(t).noEvents}</p>
                  </div>
                ) : (
                  (selectedDate ? getEventsForDate(selectedDate) : (filteredEvents).slice(0, 5)).map((event) => (<div
                      key={(event).id}
                      className="p-3 glass-card border-white/10 hover:border-white/30 transition-all duration-300"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${(event).color}`}></div>
                          {getEventTypeIcon((event).type)}
                        </div>
                        <Button variant="ghost" size="sm" className="p-1">
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </div>
                      
                      <h4 className="text-white font-medium text-sm mb-1">
                        {(event).title}
                      </h4>
                      
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-white/70 text-xs">
                          <Clock className="h-3 w-3" />
                          {(event).isAllDay ? (t).allDay : `${formatTime((event).startTime)} - ${formatTime((event).endTime)}`}
                        </div>
                        
                        {(event).location && (<div className="flex items-center gap-2 text-white/70 text-xs">
                            {(event).isOnline ? <Video className="h-3 w-3" /> : <MapPin className="h-3 w-3" />}
                            {(event).location}
                          </div>
                        )}
                        
                        {(event).attendees && (event).attendees.length > 0 && (<div className="flex items-center gap-2 text-white/70 text-xs">
                            <Users className="h-3 w-3" />
                            {(event).attendees.length} {(t).attendees}
                          </div>
                        )}
                      </div>
                      
                      <Badge variant="outline" className="mt-2 text-xs">
                        {t[(event).type as keyof typeof t]}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
