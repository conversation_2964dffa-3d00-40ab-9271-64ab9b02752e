import React, { useState } from 'react'
import { useSelector } from 'react-redux'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import type { RootState } from '../store'
import { syncReduxAuthState, forceTokenSync, manualAuthenticate, checkAuthStatus } from '../utils/authSync'

interface AuthSyncTestProps {
  language?: 'ar' | 'en'
}

export default function AuthSyncTest({ language = 'en' }: AuthSyncTestProps): React.ReactElement {
  const authState = useSelector((state: RootState => state.auth)
  const [testToken, setTestToken] = useState<string>('')
  const [testResult, setTestResult] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const t = {
    title: language === 'ar' ? 'اختبار مزامنة المصادقة' : 'Authentication Sync Test',
    description: language === 'ar' ? 'اختبار مزامنة حالة Redux مع الرموز المميزة' : 'Test Redux state synchronization with tokens',
    currentState: language === 'ar' ? 'الحالة الحالية' : 'Current State',
    authenticated: language === 'ar' ? 'مصادق' : 'Authenticated',
    notAuthenticated: language === 'ar' ? 'غير مصادق' : 'Not Authenticated',
    username: language === 'ar' ? 'اسم المستخدم' : 'Username',
    testToken: language === 'ar' ? 'رمز الاختبار' : 'Test Token',
    syncAuth: language === 'ar' ? 'مزامنة المصادقة' : 'Sync Auth',
    forceSync: language === 'ar' ? 'مزامنة قسرية' : 'Force Sync',
    manualAuth: language === 'ar' ? 'مصادقة يدوية' : 'Manual Auth',
    checkStatus: language === 'ar' ? 'فحص الحالة' : 'Check Status',
    clearTokens: language === 'ar' ? 'مسح الرموز' : 'Clear Tokens',
    result: language === 'ar' ? 'النتيجة' : 'Result'
  }

  const handleSyncAuth = async () => {
    setIsLoading(true)
    setTestResult(null)
    try {
      const result = await syncReduxAuthState()
      setTestResult(result ? 'Sync successful!' : 'Sync failed - no valid tokens')
    } catch (error) {
      setTestResult(`Sync error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleForceSync = async () => {
    setIsLoading(true)
    setTestResult(null)
    try {
      const result = await forceTokenSync(testToken || undefined)
      setTestResult(result ? 'Force sync successful!' : 'Force sync failed')
    } catch (error) {
      setTestResult(`Force sync error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleManualAuth = async () => {
    if (!testToken.trim()) {
      setTestResult('Please enter a test token')
      return
    }

    setIsLoading(true)
    setTestResult(null)
    try {
      const result = await manualAuthenticate(testToken)
      setTestResult(result ? 'Manual authentication successful!' : 'Manual authentication failed')
    } catch (error) {
      setTestResult(`Manual auth error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCheckStatus = async () => {
    setIsLoading(true)
    setTestResult(null)
    try {
      const result = await checkAuthStatus()
      setTestResult(`Auth status: ${result ? 'Authenticated' : 'Not authenticated'}`)
    } catch (error) {
      setTestResult(`Status check error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClearTokens = (): void => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    setTestResult('Tokens cleared from localStorage')
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>{t.title}</CardTitle>
        <CardDescription>{t.description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current State Display */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">{t.currentState}</Label>
          <div className="flex items-center gap-2">
            <Badge variant={authState.isAuthenticated ? "default" : "secondary"}>
              {authState.isAuthenticated ? t.authenticated : t.notAuthenticated}
            </Badge>
            {authState.user && (
              <Badge variant="outline">
                {t.username}: {authState.user.username}
              </Badge>
            )}
          </div>
        </div>

        {/* Test Token Input */}
        <div className="space-y-2">
          <Label htmlFor="testToken">{t.testToken}</Label>
          <Input
            id="testToken"
            type="text"
            placeholder="Enter test token..."
            value={testToken}
            onChange={(e: any) => setTestToken(e.target.value)}
          />
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-2">
          <Button 
            onClick={handleSyncAuth} 
            disabled={isLoading}
            variant="default"
          >
            {t.syncAuth}
          </Button>
          <Button 
            onClick={handleForceSync} 
            disabled={isLoading}
            variant="secondary"
          >
            {t.forceSync}
          </Button>
          <Button 
            onClick={handleManualAuth} 
            disabled={isLoading || !testToken.trim()}
            variant="outline"
          >
            {t.manualAuth}
          </Button>
          <Button 
            onClick={handleCheckStatus} 
            disabled={isLoading}
            variant="outline"
          >
            {t.checkStatus}
          </Button>
        </div>

        <Button 
          onClick={handleClearTokens} 
          disabled={isLoading}
          variant="destructive"
          className="w-full"
        >
          {t.clearTokens}
        </Button>

        {/* Test Result */}
        {testResult && (
          <Alert>
            <AlertDescription>
              <strong>{t.result}:</strong> {testResult}
            </AlertDescription>
          </Alert>
        )}

        {/* Debug Info */}
        <div className="text-xs text-gray-500 space-y-1">
          <div>localStorage token: {localStorage.getItem('access_token') ? 'Present' : 'None'}</div>
          <div>Cookies: {document.cookie.includes('access_token') ? 'Present' : 'None'}</div>
          <div>Redux loading: {authState.isLoading ? 'Yes' : 'No'}</div>
          <div>Redux verifying: {authState.isVerifying ? 'Yes' : 'No'}</div>
        </div>
      </CardContent>
    </Card>
  )
}
