/**
 * Real-time Notification Center
 * Advanced notification management with real-time updates, filtering, and actions
 */

import React, { useState, useEffect, memo, useCallback, useMemo } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  Bell,
  X,
  Check,
  Trash2,
  Filter,
  Search,
  MoreVertical,
  AlertCircle,
  Info,
  CheckCircle,
  AlertTriangle,
  Clock,
  User,
  Settings
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Badge } from '../ui/badge'
import type { RootState, AppDispatch } from '../../store'
import {
  markAsRead,
  markAllAsRead,
  removeNotification,
  clearError,
  setFilter
} from '../../store/slices/notificationSlice'
import webSocketService, { NotificationData } from '../../services/websocket'
import type { Notification } from '../../store/slices/notificationSlice'

interface NotificationCenterProps {
  isOpen: boolean
  onClose: () => void
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    notifications: 'الإشعارات',
    markAllRead: 'تحديد الكل كمقروء',
    clearAll: 'مسح الكل',
    filter: 'تصفية',
    search: 'بحث في الإشعارات',
    noNotifications: 'لا توجد إشعارات',
    all: 'الكل',
    unread: 'غير مقروء',
    read: 'مقروء',
    info: 'معلومات',
    success: 'نجح',
    warning: 'تحذير',
    error: 'خطأ',
    markAsRead: 'تحديد كمقروء',
    delete: 'حذف',
    settings: 'إعدادات الإشعارات',
    enableSound: 'تفعيل الصوت',
    enableDesktop: 'إشعارات سطح المكتب',
    enableEmail: 'إشعارات البريد الإلكتروني',
    autoMarkRead: 'تحديد كمقروء تلقائياً',
    groupSimilar: 'تجميع الإشعارات المتشابهة',
    showPreview: 'عرض المعاينة',
    just_now: 'الآن',
    minutes_ago: 'منذ دقائق',
    hours_ago: 'منذ ساعات',
    days_ago: 'منذ أيام',
    weeks_ago: 'منذ أسابيع'
  },
  en: {
    notifications: 'Notifications',
    markAllRead: 'Mark All Read',
    clearAll: 'Clear All',
    filter: 'Filter',
    search: 'Search notifications',
    noNotifications: 'No notifications',
    all: 'All',
    unread: 'Unread',
    read: 'Read',
    info: 'Info',
    success: 'Success',
    warning: 'Warning',
    error: 'Error',
    markAsRead: 'Mark as Read',
    delete: 'Delete',
    settings: 'Notification Settings',
    enableSound: 'Enable Sound',
    enableDesktop: 'Desktop Notifications',
    enableEmail: 'Email Notifications',
    autoMarkRead: 'Auto Mark as Read',
    groupSimilar: 'Group Similar',
    showPreview: 'Show Preview',
    just_now: 'Just now',
    minutes_ago: 'minutes ago',
    hours_ago: 'hours ago',
    days_ago: 'days ago',
    weeks_ago: 'weeks ago'
  }
}

const notificationIcons = {
  info: Info,
  success: CheckCircle,
  warning: AlertTriangle,
  error: AlertCircle,
  urgent: AlertTriangle
}

const notificationColors = {
  info: 'text-blue-400 bg-blue-500/20',
  success: 'text-green-400 bg-green-500/20',
  warning: 'text-yellow-400 bg-yellow-500/20',
  error: 'text-red-400 bg-red-500/20',
  urgent: 'text-red-500 bg-red-600/30'
}
const NotificationCenter: React.FC<NotificationCenterProps> = memo(({
  isOpen,
  onClose,
  language
}) => {
  const dispatch = useDispatch<AppDispatch>()
  const { notifications } = useSelector((state: RootState => state.notifications)

  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'unread' | 'read'>('all')
  const [filterCategory, setFilterCategory] = useState<'all' | 'info' | 'success' | 'warning' | 'error'>('all')
  const [showSettings, setShowSettings] = useState(false)
  const [selectedNotifications, setSelectedNotifications] = useState<Set<string>>(new Set())
  const t = useMemo(() => translations[language], [language])
  const isRTL = useMemo(() => language === 'ar', [language])

  // Filter and search notifications
  const filteredNotifications = useMemo(() => {
    // Create a copy of the notifications array to avoid mutating the original
    let filtered = [...notifications]

    // Filter by read status
    if (filterType === 'unread') {
      filtered = filtered.filter(n => !n.isRead)
    } else if (filterType === 'read') {
      filtered = filtered.filter(n => n.isRead)
    }

    // Filter by category
    if (filterCategory !== 'all') {
      filtered = filtered.filter(n => n.type === filterCategory)
    }

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(n =>
        n.title.toLowerCase().includes(query) ||
        n.message.toLowerCase().includes(query)
      )
    }

    // Sort by timestamp (newest first) - already working with a copy
    return filtered.sort((a, b) => {
      const aTime = typeof a.timestamp === 'string' ? new Date(a.timestamp).getTime() : a.timestamp
      const bTime = typeof b.timestamp === 'string' ? new Date(b.timestamp).getTime() : b.timestamp
      return bTime - aTime
    })
  }, [notifications, filterType, filterCategory, searchQuery])

  // Get unread count
  const unreadCount = useMemo(() =>
    notifications.filter(n => !n.isRead).length,
    [notifications]
  )

  // Format relative time
  const formatRelativeTime = useCallback((timestamp: string | number) => {
    const now = Date.now()
    const timestampMs = typeof timestamp === 'string' ? new Datetimestamp.getTime() : timestamp
    const diff = now - timestampMs
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const weeks = Math.floor(diff / (1000 * 60 * 60 * 24 * 7))
    if (minutes < 1) return t.just_now
    if (minutes < 60) return `${minutes} ${t.minutes_ago}`
    if (hours < 24) return `${hours} ${t.hours_ago}`
    if (days < 7) return `${days} ${t.days_ago}`
    return `${weeks} ${t.weeks_ago}`
  }, [t])

  // Handle notification click
  const handleNotificationClick = useCallback((notification: Notification => {
    if (!notification.isRead) {
      dispatch(markAsRead(notification.id))
    }

    // Handle notification actions
    if (notification.actions && notification.actions.length > 0) {
      // Execute first action by default
      const action = notification.actions[0]
      console.log('Executing action:', action)
    }
  }, [dispatch])

  // Handle mark as read
  const handleMarkAsRead = useCallback((id: string, event: React.MouseEvent) => {
    event.stopPropagation()
    dispatch(markAsRead(id))
  }, [dispatch])

  // Handle delete notification
  const handleDelete = useCallback((id: string, event: React.MouseEvent) => {
    event.stopPropagation()
    dispatch(removeNotification(id))
  }, [dispatch])

  // Handle mark all as read
  const handleMarkAllAsRead = useCallback(() => {
    dispatch(markAllAsRead())
  }, [dispatch])

  // Handle clear all notifications
  const handleClearAll = useCallback(() => {
    dispatch(clearError())
  }, [dispatch])

  // Handle notification selection
  const handleNotificationSelect = useCallback((id: string, event?: React.MouseEvent | React.ChangeEvent) => {
    event?.stopPropagation()
    setSelectedNotifications(prev => {
      const newSet = new Set(prev)
      if (newSet.has(id)) {
        newSet.delete(id)
      } else {
        newSet.add(id)
      }
      return newSet
    })
  }, [])

  // Handle bulk actions
  const handleBulkMarkAsRead = useCallback(() => {
    selectedNotifications.forEach(id => {
      dispatch(markAsRead(id))
    })
    setSelectedNotifications(new Set())
  }, [selectedNotifications, dispatch])
  const handleBulkDelete = useCallback(() => {
    selectedNotifications.forEach(id => {
      dispatch(removeNotification(id))
    })
    setSelectedNotifications(new Set())
  }, [selectedNotifications, dispatch])

  // Listen for real-time notifications
  useEffect(() => {
    const handleNewNotification = (notification: NotificationData): void => {
      // Notification is already added to store by WebSocket service
      // We can add additional UI feedback here
      // Play notification sound (simplified for demo)
      const audio = new Audio('/notification-sound.mp3')
      audio.play().catch(() => {
        // Ignore audio play errors
      })
    }

    webSocketService.on('notification', handleNewNotification)
    return () => {
      webSocketService.off('notification', handleNewNotification)
    }
  }, [])

  if (!isOpen) return null

  return (<div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
      <div className={`fixed top-0 ${isRTL ? 'left-0' : 'right-0'} h-full w-full max-w-md bg-white/10 backdrop-blur-xl border-l border-white/20 shadow-2xl`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/10">
          <div className="flex items-center gap-3">
            <Bell className="h-5 w-5 text-white" />
            <h2 className="text-lg font-semibold text-white">{t.notifications}</h2>
            {unreadCount > 0 && (
              <Badge variant="destructive" className="bg-red-500">
                {unreadCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="text-white hover:bg-white/10"
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Settings Panel */}
        {showSettings && (<div className="p-4 border-b border-white/10 bg-white/5">
            <h3 className="text-white font-medium mb-3">{t.settings}</h3>
            <div className="space-y-2">
              {/* Settings toggles would go here */}
              <div className="flex items-center justify-between">
                <span className="text-white/80 text-sm">{t.enableSound}</span>
                <input type="checkbox" className="rounded" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/80 text-sm">{t.enableDesktop}</span>
                <input type="checkbox" className="rounded" />
              </div>
            </div>
          </div>
        )}

        {/* Search and Filters */}
        <div className="p-4 border-b border-white/10">
          <div className="relative mb-3">
            <Search className={`absolute top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60 ${
              isRTL ? 'right-3' : 'left-3'
            }`} />
            <Input
              value={searchQuery}
              onChange={(e: any) => setSearchQuery(e.target.value)}
              placeholder={t.search}
              className={`bg-white/5 border-white/20 text-white placeholder-white/60 ${
                isRTL ? 'pr-10 text-right' : 'pl-10'
              }`}
            />
          </div>

          <div className="flex gap-2 mb-3">
            {(['all', 'unread', 'read'] as const).map((type) => (
              <Button
                key={type}
                variant={filterType === type ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType(type)}
                className={`text-xs ${
                  filterType === type
                    ? 'bg-blue-500 text-white'
                    : 'bg-white/5 text-white border-white/20 hover:bg-white/10'
                }`}
              >
                {t[type]}
              </Button>
            ))}
          </div>

          <div className="flex gap-2">
            {(['all', 'info', 'success', 'warning', 'error'] as const).map((category) => (
              <Button
                key={category}
                variant={filterCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterCategory(category)}
                className={`text-xs ${
                  filterCategory === category
                    ? 'bg-blue-500 text-white'
                    : 'bg-white/5 text-white border-white/20 hover:bg-white/10'
                }`}
              >
                {t[category]}
              </Button>
            ))}
          </div>
        </div>

        {/* Actions */}
        {(unreadCount > 0 || selectedNotifications.size > 0) && (<div className="p-4 border-b border-white/10 bg-white/5">
            <div className="flex gap-2">
              {selectedNotifications.size > 0 ? (<>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkMarkAsRead}
                    className="bg-white/5 border-white/20 text-white hover:bg-white/10"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    Mark Read ({selectedNotifications.size})
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleBulkDelete}
                    className="bg-white/5 border-white/20 text-red-400 hover:bg-red-500/10"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete ({selectedNotifications.size})
                  </Button>
                </>
              ) : (<>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="bg-white/5 border-white/20 text-white hover:bg-white/10"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    {t.markAllRead}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearAll}
                    className="bg-white/5 border-white/20 text-red-400 hover:bg-red-500/10"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    {t.clearAll}
                  </Button>
                </>
              )}
            </div>
          </div>
        )}

        {/* Notifications List */}
        <div className="flex-1 overflow-y-auto">
          {filteredNotifications.length === 0 ? (<div className="flex flex-col items-center justify-center h-64 text-white/60">
              <Bell className="h-12 w-12 mb-3 opacity-50" />
              <p>{t.noNotifications}</p>
            </div>
          ) : (<div className="space-y-1 p-2">
              {filteredNotifications.map((notification) => {
                const IconComponent = notificationIcons[notification.type]
                const colorClass = notificationColors[notification.type]
                const isSelected = selectedNotifications.has(notification.id)

                return (<div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`p-3 rounded-lg cursor-pointer transition-all duration-200 border ${
                      notification.isRead
                        ? 'bg-white/5 border-white/10 opacity-75'
                        : 'bg-white/10 border-white/20'
                    } ${isSelected ? 'ring-2 ring-blue-500' : ''} hover:bg-white/15`}
                  >
                    <div className="flex items-start gap-3">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e: any) => handleNotificationSelect(notification.id, e)}
                        className="mt-1 rounded"
                        onClick={(e: any) => e.stopPropagation()}
                      />

                      <div className={`p-2 rounded-lg ${colorClass} flex-shrink-0`}>
                        <IconComponent className="h-4 w-4" />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <h4 className={`font-medium truncate ${
                            notification.isRead ? 'text-white/70' : 'text-white'
                          }`}>
                            {notification.title}
                          </h4>
                          <div className="flex items-center gap-1 ml-2">
                            {!notification.isRead && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e: any) => handleMarkAsRead(notification.id, e)}
                                className="p-1 h-auto text-white/60 hover:text-white hover:bg-white/10"
                              >
                                <Check className="h-3 w-3" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e: any) => handleDelete(notification.id, e)}
                              className="p-1 h-auto text-white/60 hover:text-red-400 hover:bg-red-500/10"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        <p className={`text-sm mt-1 line-clamp-2 ${
                          notification.isRead ? 'text-white/50' : 'text-white/70'
                        }`}>
                          {notification.message}
                        </p>

                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs text-white/40 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatRelativeTime(notification.timestamp)}
                          </span>

                          {notification.actions && notification.actions.length > 0 && (<div className="flex gap-1">
                              {notification.actions.slice(0, 2).map((action, index) => (<Button
                                  key={index}
                                  variant="outline"
                                  size="sm"
                                  className="text-xs h-6 px-2 bg-white/5 border-white/20 text-white hover:bg-white/10"
                                >
                                  {action.label}
                                </Button>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
})

NotificationCenter.displayName = 'NotificationCenter'

export default NotificationCenter
