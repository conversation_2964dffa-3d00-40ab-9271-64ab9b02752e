import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Bell,
  X,
  Check,
  AlertTriangle,
  Info,
  CheckCircle,
  Clock,
  User,
  Calendar,
  DollarSign,
  Briefcase,
  Settings,
  Filter,
  MoreVertical
} from 'lucide-react'

interface SmartNotificationCenterProps {
  language: 'ar' | 'en'
  userRole: string
}

interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'warning' | 'success' | 'error' | 'urgent'
  category: 'system' | 'hr' | 'finance' | 'project' | 'personal'
  timestamp: Date
  isRead: boolean
  isImportant: boolean
  actionRequired: boolean
  relatedEntity?: {
    type: string
    id: string
    name: string
  }
}

const translations = {
  ar: {
    notifications: 'الإشعارات',
    markAllRead: 'تحديد الكل كمقروء',
    filter: 'تصفية',
    all: 'الكل',
    unread: 'غير مقروء',
    important: 'مهم',
    actionRequired: 'يتطلب إجراء',
    system: 'النظام',
    hr: 'الموارد البشرية',
    finance: 'المالية',
    project: 'المشاريع',
    personal: 'شخصي',
    justNow: 'الآن',
    minutesAgo: 'منذ دقائق',
    hoursAgo: 'منذ ساعات',
    daysAgo: 'منذ أيام',
    markAsRead: 'تحديد كمقروء',
    dismiss: 'إغلاق',
    viewDetails: 'عرض التفاصيل',
    noNotifications: 'لا توجد إشعارات',
    settings: 'إعدادات الإشعارات'
  },
  en: {
    notifications: 'Notifications',
    markAllRead: 'Mark All Read',
    filter: 'Filter',
    all: 'All',
    unread: 'Unread',
    important: 'Important',
    actionRequired: 'Action Required',
    system: 'System',
    hr: 'HR',
    finance: 'Finance',
    project: 'Projects',
    personal: 'Personal',
    justNow: 'Just now',
    minutesAgo: 'minutes ago',
    hoursAgo: 'hours ago',
    daysAgo: 'days ago',
    markAsRead: 'Mark as Read',
    dismiss: 'Dismiss',
    viewDetails: 'View Details',
    noNotifications: 'No notifications',
    settings: 'Notification Settings'
  }
}
export default function SmartNotificationCenter({ language, userRole }: SmartNotificationCenterProps): React.ReactElement {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filter, setFilter] = useState<'all' | 'unread' | 'important' | 'actionRequired'>('all')
  const [categoryFilter, setCategoryFilter] = useState<string>('all')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Fetch real notifications from API with deduplication
  useEffect(() => {
    const fetchNotifications = async () => {
      try {
        // PERFORMANCE FIX: Use request deduplication to prevent excessive API calls
        const { deduplicateRequest } = await import('../../utils/apiCache')
        const { apiClient } = await import('../../services/api')

        // Fetch notifications from backend with deduplication
        const response = await deduplicateRequest(
          'notifications-smart-center-fetch',
          async () => {
            return await apiClient.get<{
              count: number
              results: Array<{
                id: string
                title: string
                title_ar: string
                message: string
                message_ar: string
                notification_type: string
                priority: string
                category: string
                is_read: boolean
                action_required: boolean
                created_at: string
                action_url?: string
                related_object_type?: string
                related_object_id?: string
              }>
            }>('/notifications/notifications/')
          },
          3000 // 3 second deduplication window
        )

        // Transform API data to component format
        const apiNotifications: Notification[] = response.data.results.map(apiNotif => ({
          id: apiNotif.id,
          title: language === 'ar' ? (apiNotif.title_ar || apiNotif.title) : apiNotif.title,
          message: language === 'ar' ? (apiNotif.message_ar || apiNotif.message) : apiNotif.message,
          type: apiNotif.notification_type,
          category: apiNotif.category,
          timestamp: new Date(apiNotif.created_at),
          isRead: apiNotif.is_read,
          isImportant: apiNotif.priority === 'high' || apiNotif.priority === 'urgent',
          actionRequired: apiNotif.action_required,
          relatedEntity: apiNotif.related_object_type ? {
            type: apiNotif.related_object_type,
            id: apiNotif.related_object_id || '',
            name: ''
          } : undefined
        }))

        setNotifications(apiNotifications)
      } catch (error) {
        console.error('Error fetching notifications:', error)

        // Fallback to empty array if API fails
        setNotifications([])
      }
    }
    fetchNotifications()
  }, [language])

  const getTypeIcon = (type: string): void => {
    switch (type) {
      case 'info': return <Info className="h-4 w-4 text-blue-400" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case 'success': return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-400" />
      case 'urgent': return <Bell className="h-4 w-4 text-red-500" />
      default: return <Info className="h-4 w-4 text-gray-400" />
    }
  }

  const getCategoryIcon = (category: string): void => {
    switch (category) {
      case 'hr': return <User className="h-4 w-4" />
      case 'finance': return <DollarSign className="h-4 w-4" />
      case 'project': return <Briefcase className="h-4 w-4" />
      case 'personal': return <User className="h-4 w-4" />
      case 'system': return <Settings className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getTimeAgo = (timestamp: Date): void => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return t.justNow
    if (minutes < 60) return `${minutes} ${t.minutesAgo}`
    if (hours < 24) return `${hours} ${t.hoursAgo}`
    return `${days} ${t.daysAgo}`
  }

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread' && notification.isRead) return false
    if (filter === 'important' && !notification.isImportant) return false
    if (filter === 'actionRequired' && !notification.actionRequired) return false
    if (categoryFilter !== 'all' && notification.category !== categoryFilter) return false
    return true
  })

  const markAsRead = (id: string): void => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true }
          : notification
      )
    )
  }

  const markAllAsRead = (): void => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, isRead: true }))
    )
  }

  const dismissNotification = (id: string): void => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }

  const unreadCount = notifications.filter(n => !n.isRead).length

  return (<Card className="glass-card border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <Bell className="h-5 w-5" />
            {t.notifications}
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="glass-button text-xs"
            >
              <Check className="h-3 w-3 mr-1" />
              {t.markAllRead}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="glass-button text-xs"
            >
              <Settings className="h-3 w-3 mr-1" />
              {t.settings}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-2 mt-4">
          {['all', 'unread', 'important', 'actionRequired'].map((filterType) => (
            <Button
              key={filterType}
              variant={filter === filterType ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setFilter(filterType)}
              className={`glass-button text-xs ${
                filter === filterType ? 'bg-blue-500/30' : ''
              }`}
            >
              {t[filterType as keyof typeof t]}
            </Button>
          ))}
        </div>

        <div className="flex flex-wrap gap-2 mt-2">
          {['all', 'system', 'hr', 'finance', 'project', 'personal'].map((category) => (
            <Button
              key={category}
              variant={categoryFilter === category ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setCategoryFilter(category)}
              className={`glass-button text-xs ${
                categoryFilter === category ? 'bg-purple-500/30' : ''
              }`}
            >
              {getCategoryIcon(category)}
              <span className="ml-1">{t[category as keyof typeof t]}</span>
            </Button>
          ))}
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredNotifications.length === 0 ? (<div className="text-center py-8">
              <Bell className="h-12 w-12 text-white/30 mx-auto mb-3" />
              <p className="text-white/60">{t.noNotifications}</p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (<div
                key={notification.id}
                className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all duration-300 ${
                  !notification.isRead ? 'border-l-4 border-l-blue-500' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    <div className="flex-shrink-0 mt-1">
                      {getTypeIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className={`font-medium ${notification.isRead ? 'text-white/80' : 'text-white'}`}>
                          {notification.title}
                        </h4>
                        {notification.isImportant && (
                          <Badge variant="destructive" className="text-xs">!</Badge>
                        )}
                        {notification.actionRequired && (
                          <Badge variant="outline" className="text-xs">
                            {language === 'ar' ? 'إجراء' : 'Action'}
                          </Badge>
                        )}
                      </div>
                      <p className={`text-sm ${notification.isRead ? 'text-white/60' : 'text-white/80'}`}>
                        {notification.message}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        {getCategoryIcon(notification.category)}
                        <span className="text-xs text-white/50">
                          {t[notification.category as keyof typeof t]}
                        </span>
                        <span className="text-xs text-white/50">•</span>
                        <span className="text-xs text-white/50">
                          {getTimeAgo(notification.timestamp)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 ml-3">
                    {!notification.isRead && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                        className="glass-button p-1 h-auto"
                      >
                        <Check className="h-3 w-3" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => dismissNotification(notification.id)}
                      className="glass-button p-1 h-auto"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
