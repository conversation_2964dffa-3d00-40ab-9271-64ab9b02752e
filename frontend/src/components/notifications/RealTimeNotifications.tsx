import React from 'react';
import { useState, useEffect, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Bell,
  X,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  User,
  MessageSquare,
  Calendar,
  DollarSign,
  Briefcase,
  Settings
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import type { RootState, AppDispatch } from '../../store'
import { addNotification, markAsRead, type Notification } from '../../store/slices/notificationSlice'
import { useNotificationSync } from '../../hooks/useNotificationSync'

interface RealTimeNotificationsProps {
  language: 'ar' | 'en'
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  maxVisible?: number
}

const translations = {
  ar: {
    notifications: 'الإشعارات',
    markAllRead: 'تحديد الكل كمقروء',
    clearAll: 'مسح الكل',
    noNotifications: 'لا توجد إشعارات',
    newNotification: 'إشعار جديد',
    viewAll: 'عرض الكل',
    dismiss: 'إغلاق',
    markAsRead: 'تحديد كمقروء',
    timeAgo: {
      now: 'الآن',
      minute: 'منذ دقيقة',
      minutes: 'منذ {count} دقائق',
      hour: 'منذ ساعة',
      hours: 'منذ {count} ساعات',
      day: 'منذ يوم',
      days: 'منذ {count} أيام'
    }
  },
  en: {
    notifications: 'Notifications',
    markAllRead: 'Mark all as read',
    clearAll: 'Clear all',
    noNotifications: 'No notifications',
    newNotification: 'New notification',
    viewAll: 'View all',
    dismiss: 'Dismiss',
    markAsRead: 'Mark as read',
    timeAgo: {
      now: 'now',
      minute: '1 minute ago',
      minutes: '{count} minutes ago',
      hour: '1 hour ago',
      hours: '{count} hours ago',
      day: '1 day ago',
      days: '{count} days ago'
    }
  }
}

export default function RealTimeNotifications({
  language,
  position = 'top-right',
  maxVisible = 5
}: RealTimeNotificationsProps): React.ReactElement {
  const dispatch = useDispatch<AppDispatch>()
  const [isOpen, setIsOpen] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // FIXED: Use notification sync hook for better data synchronization
  const {
    notifications,
    unreadCount,
    toasts,
    removeToast,
    markNotificationAsRead
  } = useNotificationSync()

  // SECURITY FIX: WebSocket connection with proper cleanup
  useEffect(() => {
    let ws: WebSocket | null = null
    let reconnectTimeout: NodeJS.Timeout | null = null
    let isComponentMounted = true

    const connectWebSocket = (): void => {
      if (!isComponentMounted) return

      try {
        ws = new WebSocket(process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws/notifications/')

        ws.onopen = () => {
          console.log('✅ Notification WebSocket connected')
        }

        ws.onmessage = (event) => {
          if (!isComponentMounted) return

          try {
            const notification = JSON.parse(event.data)
            dispatch(addNotification(notification))
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error)
          }
        }

        ws.onerror = (error) => {
          console.error('❌ WebSocket error:', error)
        }

        ws.onclose = (event) => {
          console.log('🔌 WebSocket connection closed:', event.code, event.reason)

          // Attempt reconnection if component is still mounted and closure was unexpected
          if (isComponentMounted && event.code !== 1000) {
            console.log('🔄 Attempting WebSocket reconnection in 5 seconds...')
            reconnectTimeout = setTimeout(connectWebSocket, 5000)
          }
        }
      } catch (error) {
        console.error('Failed to create WebSocket connection:', error)
      }
    }

    // Initial connection
    connectWebSocket()

    // CRITICAL FIX: Proper cleanup function
    return () => {
      isComponentMounted = false

      // Clear reconnection timeout
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout)
      }

      // Close WebSocket connection
      if (ws) {
        ws.onclose = null // Prevent reconnection attempts
        ws.onerror = null
        ws.onmessage = null
        ws.onopen = null

        if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
          ws.close(1000, 'Component unmounting')
        }

        console.log('🧹 WebSocket connection cleaned up')
      }
    }
  }, [dispatch]) // Only depend on dispatch

  // Show toast notification
  const showToast = useCallback((notification: Notification) => {
    setVisibleToasts(prev => {
      const newToasts = [notification, ...prev].slice(0, maxVisible)
      return newToasts
    })

    // Auto-remove toast after 5 seconds for non-urgent notifications
    if (notification.priority !== 'urgent') {
      setTimeout(() => {
        setVisibleToasts(prev => prev.filter(t => t.id !== notification.id))
      }, 5000)
    }
  }, [maxVisible])

  // Get notification icon
  const getNotificationIcon = (type: string, category: string): void => {
    if (category === 'user') return User
    if (category === 'message') return MessageSquare
    if (category === 'calendar') return Calendar
    if (category === 'finance') return DollarSign
    if (category === 'project') return Briefcase
    if (category === 'system') return Settings

    switch (type) {
      case 'success': return CheckCircle
      case 'error': return XCircle
      case 'warning': return AlertTriangle
      default: return Info
    }
  }

  // Get notification color
  const getNotificationColor = (type: string, priority: string): void => {
    if (priority === 'urgent') return 'from-red-500 to-red-600'
    
    switch (type) {
      case 'success': return 'from-green-500 to-green-600'
      case 'error': return 'from-red-500 to-red-600'
      case 'warning': return 'from-yellow-500 to-yellow-600'
      default: return 'from-blue-500 to-blue-600'
    }
  }

  // Format time ago
  const formatTimeAgo = (timestamp: string | Date): string => {
    const now = new Date()
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp
    const diff = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diff < 60) return t.timeAgo.now
    if (diff < 3600) {
      const minutes = Math.floor(diff / 60)
      return minutes === 1 ? t.timeAgo.minute : t.timeAgo.minutes.replace('{count}', minutes.toString())
    }
    if (diff < 86400) {
      const hours = Math.floor(diff / 3600)
      return hours === 1 ? t.timeAgo.hour : t.timeAgo.hours.replace('{count}', hours.toString())
    }
    const days = Math.floor(diff / 86400)
    return days === 1 ? t.timeAgo.day : t.timeAgo.days.replace('{count}', days.toString())
  }

  // Handle notification click
  const handleNotificationClick = (notification: Notification): void => {
    if (!notification.isRead) {
      dispatch(markAsRead(notification.id))
    }

    if (notification.actions && notification.actions.length > 0) {
      // Handle the first action if available
      const firstAction = notification.actions[0]
      if (firstAction.action.startsWith('http')) {
        // FIXED: Use React Router for internal navigation
        if (firstAction.action.includes(window.location.origin)) {
          // Internal link - use React Router
          const path = firstAction.action.replace(window.location.origin, '')
          window.history.pushState({}, '', path)
          window.dispatchEvent(new PopStateEvent('popstate'))
        } else {
          // External link - open in new tab to avoid page reload
          window.open(firstAction.action, '_blank')
        }
      }
    }
  }

  // Remove toast
  const removeToast = (id: string): void => {
    setVisibleToasts(prev => prev.filter(t => t.id !== id))
  }

  // Position classes
  const getPositionClasses = (): void => {
    switch (position) {
      case 'top-left': return 'top-4 left-4'
      case 'bottom-right': return 'bottom-4 right-4'
      case 'bottom-left': return 'bottom-4 left-4'
      default: return 'top-4 right-4'
    }
  }

  return (
    <>
      {/* Notification Bell Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative text-white hover:bg-white/10"
      >
        <Bell className="h-4 w-4" />
        {unreadCount > 0 && (
          <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-red-500 text-white text-xs animate-pulse">
            {unreadCount > 9 ? '9+' : unreadCount}
          </Badge>
        )}
      </Button>

      {/* Toast Notifications */}
      <div className={`fixed z-50 ${getPositionClasses()} space-y-2 pointer-events-none`}>
        <AnimatePresence>
          {visibleToasts.map((notification) => {
            const Icon = getNotificationIcon(notification.type, notification.category)
            return (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, y: -50, scale: (0).9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -50, scale: (0).9 }}
                className="pointer-events-auto"
              >
                <div className={`
                  glass-card p-4 rounded-lg border border-white/20 shadow-xl max-w-sm
                  bg-gradient-to-r ${getNotificationColor(notification.type, notification.priority)}
                  ${isRTL ? 'text-right' : 'text-left'}
                `}>
                  <div className="flex items-start gap-3">
                    <Icon className="h-5 w-5 text-white mt-(0).5 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-white">
                        {language === 'ar' ? notification.titleAr : notification.title}
                      </h4>
                      <p className="text-xs text-white/80 mt-1">
                        {language === 'ar' ? notification.messageAr : notification.message}
                      </p>
                      <p className="text-xs text-white/60 mt-2">
                        {formatTimeAgo(notification.timestamp)}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeToast(notification.id)}
                      className="h-6 w-6 p-0 text-white/60 hover:text-white hover:bg-white/20"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </AnimatePresence>
      </div>

      {/* Notification Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: (0).95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: (0).95 }}
            className={`
              absolute top-full mt-2 w-80 glass-card rounded-lg border border-white/20 shadow-xl z-50
              ${isRTL ? 'left-0' : 'right-0'}
            `}
          >
            <div className="p-4 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">{t.notifications}</h3>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      notifications.forEach(n => {
                        if (!n.isRead) dispatch(markAsRead(n.id))
                      })
                    }}
                    className="text-xs text-white/70 hover:text-white"
                  >
                    {t.markAllRead}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="h-6 w-6 p-0 text-white/60 hover:text-white"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {notifications.length === 0 ? (<div className="p-8 text-center text-white/60">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>{t.noNotifications}</p>
                </div>
              ) : (<div className="space-y-1">
                  {notifications.slice(0, 10).map((notification) => {
                    const Icon = getNotificationIcon(notification.type, notification.category)
                    return (<div
                        key={notification.id}
                        onClick={() => handleNotificationClick(notification)}
                        className={`
                          p-3 hover:bg-white/5 cursor-pointer transition-colors
                          ${!notification.isRead ? 'bg-white/5' : ''}
                          ${isRTL ? 'text-right' : 'text-left'}
                        `}
                      >
                        <div className="flex items-start gap-3">
                          <Icon className={`h-4 w-4 mt-(0).5 flex-shrink-0 ${
                            notification.isRead ? 'text-white/50' : 'text-white'
                          }`} />
                          <div className="flex-1 min-w-0">
                            <h4 className={`text-sm font-medium ${
                              notification.isRead ? 'text-white/70' : 'text-white'
                            }`}>
                              {language === 'ar' ? notification.titleAr : notification.title}
                            </h4>
                            <p className="text-xs text-white/60 mt-1">
                              {language === 'ar' ? notification.messageAr : notification.message}
                            </p>
                            <p className="text-xs text-white/40 mt-1">
                              {formatTimeAgo(notification.timestamp)}
                            </p>
                          </div>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0 mt-2" />
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>

            {notifications.length > 10 && (<div className="p-3 border-t border-white/20 text-center">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-white/70 hover:text-white text-xs"
                >
                  {t.viewAll}
                </Button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  )
}
