import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import {
  TrendingUp,
  TrendingDown,
  Brain,
  Target,
  AlertTriangle,
  CheckCircle,
  Calendar,
  DollarSign,
  Users,
  Briefcase
} from 'lucide-react'

interface PredictiveAnalyticsProps {
  language: 'ar' | 'en'
  userRole: string
}

interface PredictionData {
  period: string
  actual?: number
  predicted: number
  confidence: number
  category: string
}

interface Insight {
  id: string
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
  type: 'opportunity' | 'risk' | 'trend'
  confidence: number
  icon: any
}

const translations = {
  ar: {
    predictiveAnalytics: 'التحليلات التنبؤية',
    insights: 'الرؤى الذكية',
    predictions: 'التوقعات',
    confidence: 'مستوى الثقة',
    timeframe: 'الإطار الزمني',
    next30Days: 'الـ 30 يوم القادمة',
    next90Days: 'الـ 90 يوم القادمة',
    nextYear: 'العام القادم',
    revenueGrowth: 'نمو الإيرادات',
    employeeTurnover: 'دوران الموظفين',
    projectCompletion: 'إنجاز المشاريع',
    budgetUtilization: 'استخدام الميزانية',
    highImpact: 'تأثير عالي',
    mediumImpact: 'تأثير متوسط',
    lowImpact: 'تأثير منخفض',
    opportunity: 'فرصة',
    risk: 'مخاطرة',
    trend: 'اتجاه',
    actualVsPredicted: 'الفعلي مقابل المتوقع',
    generateReport: 'إنشاء تقرير'
  },
  en: {
    predictiveAnalytics: 'Predictive Analytics',
    insights: 'Smart Insights',
    predictions: 'Predictions',
    confidence: 'Confidence Level',
    timeframe: 'Timeframe',
    next30Days: 'Next 30 Days',
    next90Days: 'Next 90 Days',
    nextYear: 'Next Year',
    revenueGrowth: 'Revenue Growth',
    employeeTurnover: 'Employee Turnover',
    projectCompletion: 'Project Completion',
    budgetUtilization: 'Budget Utilization',
    highImpact: 'High Impact',
    mediumImpact: 'Medium Impact',
    lowImpact: 'Low Impact',
    opportunity: 'Opportunity',
    risk: 'Risk',
    trend: 'Trend',
    actualVsPredicted: 'Actual vs Predicted',
    generateReport: 'Generate Report'
  }
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']
export default function PredictiveAnalytics({ language, userRole }: PredictiveAnalyticsProps): React.ReactElement {
  const [timeframe, setTimeframe] = useState('next30Days')
  const [selectedMetric, setSelectedMetric] = useState('revenue')
  const [predictions, setPredictions] = useState<PredictionData[]>([])
  const [insights, setInsights] = useState<Insight[]>([])
  const t = translations[language]
  const isRTL = language === 'ar'

  // Load prediction data from API
  useEffect(() => {
    const loadPredictions = async () => {
      try {
        // TODO: Replace with real predictive analytics API when available
        // For now, return empty data instead of mock predictions
        setPredictions([])
      } catch (error) {
        console.error('Error loading predictions:', error)
        setPredictions([])
      }
    }
    loadPredictions()
  }, [timeframe, selectedMetric])

  // Generate insights
  useEffect(() => {
    const generateInsights = (): void => {
      const insightsList: Insight[] = [
        {
          id: '1',
          title: language === 'ar' ? 'نمو متوقع في الإيرادات' : 'Expected Revenue Growth',
          description: language === 'ar' 
            ? 'من المتوقع زيادة الإيرادات بنسبة 15% خلال الربع القادم'
            : 'Revenue is expected to increase by 15% in the next quarter',
          impact: 'high',
          type: 'opportunity',
          confidence: 87,
          icon: TrendingUp
        },
        {
          id: '2',
          title: language === 'ar' ? 'مخاطر في دوران الموظفين' : 'Employee Turnover Risk',
          description: language === 'ar'
            ? 'احتمالية زيادة معدل ترك الموظفين في قسم التقنية'
            : 'Potential increase in turnover rate in the IT department',
          impact: 'medium',
          type: 'risk',
          confidence: 72,
          icon: AlertTriangle
        },
        {
          id: '3',
          title: language === 'ar' ? 'تحسن في إنتاجية المشاريع' : 'Project Productivity Improvement',
          description: language === 'ar'
            ? 'اتجاه إيجابي في معدلات إنجاز المشاريع'
            : 'Positive trend in project completion rates',
          impact: 'high',
          type: 'trend',
          confidence: 91,
          icon: CheckCircle
        }
      ]
      
      setInsights(insightsList)
    }
    generateInsights()
  }, [language])

  const getImpactColor = (impact: string): void => {
    switch (impact) {
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getTypeIcon = (type: string): void => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'risk': return <AlertTriangle className="h-4 w-4 text-red-400" />
      case 'trend': return <Target className="h-4 w-4 text-blue-400" />
      default: return <Brain className="h-4 w-4 text-gray-400" />
    }
  }

  return (<div className="space-y-6">
      {/* Header */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Brain className="h-5 w-5" />
              {t.predictiveAnalytics}
            </CardTitle>
            <div className="flex items-center gap-3">
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="glass-button w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="next30Days">{t.next30Days}</SelectItem>
                  <SelectItem value="next90Days">{t.next90Days}</SelectItem>
                  <SelectItem value="nextYear">{t.nextYear}</SelectItem>
                </SelectContent>
              </Select>
              <Button className="glass-button">
                {t.generateReport}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Predictions Chart */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-lg">{t.actualVsPredicted}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={predictions}>
                <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,(0).1)" />
                <XAxis 
                  dataKey="period" 
                  stroke="rgba(255,255,255,(0).7)"
                  fontSize={12}
                />
                <YAxis 
                  stroke="rgba(255,255,255,(0).7)"
                  fontSize={12}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(0,0,0,(0).8)',
                    border: '1px solid rgba(255,255,255,(0).2)',
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
                <Line 
                  type="monotone" 
                  dataKey="actual" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  name="Actual"
                />
                <Line 
                  type="monotone" 
                  dataKey="predicted" 
                  stroke="#10B981" 
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  name="Predicted"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Smart Insights */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <Target className="h-5 w-5" />
            {t.insights}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {insights.map((insight) => (<div
                key={insight.id}
                className="p-4 glass-card border-white/10 hover:border-white/30 transition-all duration-300"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    {getTypeIcon(insight.type)}
                    <div>
                      <h4 className="text-white font-medium">{insight.title}</h4>
                      <p className="text-white/70 text-sm">{insight.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`text-sm font-medium ${getImpactColor(insight.impact)}`}>
                      {t[`${insight.impact}Impact` as keyof typeof t]}
                    </span>
                    <p className="text-white/60 text-xs">
                      {t.confidence}: {insight.confidence}%
                    </p>
                  </div>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-1000"
                    style={{ width: `${insight.confidence}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
