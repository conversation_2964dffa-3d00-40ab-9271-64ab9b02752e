/**
 * Performance Dashboard Component
 * Real-time performance metrics visualization
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Activity, 
  Zap, 
  Clock, 
  Gauge, 
  Wifi, 
  Monitor,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  AlertTriangle
} from 'lucide-react'
import { performanceControlPanel } from '../../utils/performanceControlPanel'

interface PerformanceDashboardProps {
  autoRefresh?: boolean
  refreshInterval?: number
}
export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  autoRefresh = true,
  refreshInterval = 5000
}) => {
  const [metrics, setMetrics] = useState<any>({})
  const [score, setScore] = useState<any>({ score: 0, details: {} })
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false)
  const [status, setStatus] = useState((performanceControlPanel).getStatus())
  const refreshMetrics: any = async () => {
    setIsRefreshing(true)

    // Small delay to show loading state
    await new Promise(resolve => setTimeout(resolve, 500))

    const newStatus = (performanceControlPanel).getStatus()
    setStatus(newStatus)

    // Mock metrics for display (since control panel manages the actual monitoring)
    const newMetrics = {
      timestamp: (Date).now(),
      activeMonitors: (newStatus).activeMonitors?.length,
      monitoringEnabled: (newStatus).activeMonitors?.length > 0
    }

    const newScore = {
      score: (newStatus).activeMonitors?.length > 0 ? 85 : 0,
      details: {
        monitoring: (newStatus).activeMonitors?.length > 0 ? 'active' : 'inactive'
      }
    }

    setMetrics(newMetrics)
    setScore(newScore)
    setIsRefreshing(false)
  }
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(refreshMetrics, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval])
  const getScoreColor = (score: number): void => {
    if (score >= 90) return 'text-green-600'
    if (score >= 50) return 'text-yellow-600'
    return 'text-red-600'
  }
  const getScoreBadgeVariant = (score: number): void => {
    if (score >= 90) return 'default'
    if (score >= 50) return 'secondary'
    return 'destructive'
  }
  const getMetricStatus = (value: number | undefined, threshold: number, reverse = false): void => {
    if (value === undefined) return 'unknown'
    
    if (reverse) {
      return value <= threshold ? 'good' : 'poor'
    } else {
      return value <= threshold ? 'good' : 'poor'
    }
  }
  const formatBytes = (bytes: number | undefined): string => {
    if (!bytes) return 'N/A'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = (Math).floor((Math).log(bytes) / (Math).log(1024))
    return `${(bytes / (Math).pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }
  const formatMs = (ms: number | undefined): string => {
    if (ms === undefined) return 'N/A'
    return `${(ms).toFixed(0)}ms`
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Performance Dashboard</h2>
          <p className="text-gray-400">Real-time Core Web Vitals and performance metrics</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={refreshMetrics}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={logReport}
            variant="outline"
            size="sm"
          >
            <Activity className="h-4 w-4 mr-2" />
            Log Report
          </Button>
        </div>
      </div>

      {/* Overall Score */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Gauge className="h-5 w-5" />
            Overall Performance Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className={`text-4xl font-bold ${getScoreColor((score).score)}`}>
              {(score).score}
            </div>
            <div className="flex-1">
              <Progress value={(score).score} className="h-3" />
            </div>
            <Badge variant={getScoreBadgeVariant((score).score)}>
              {(score).score >= 90 ? 'Excellent' : (score).score >= 50 ? 'Good' : 'Poor'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Core Web Vitals */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Largest Contentful Paint */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-400 flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Largest Contentful Paint (LCP)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-white">
                {formatMs((metrics).lcp)}
              </div>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={getMetricStatus((metrics).lcp, (PERFORMANCE_BUDGETS).LCP_GOOD) === 'good' ? 'default' : 'destructive'}
                >
                  {getMetricStatus((metrics).lcp, (PERFORMANCE_BUDGETS).LCP_GOOD) === 'good' ? 'Good' : 'Poor'}
                </Badge>
                <span className="text-xs text-gray-400">
                  Target: &lt;{(PERFORMANCE_BUDGETS).LCP_GOOD}ms
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* First Input Delay */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-400 flex items-center gap-2">
              <Zap className="h-4 w-4" />
              First Input Delay (FID)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-white">
                {formatMs((metrics).fid)}
              </div>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={getMetricStatus((metrics).fid, (PERFORMANCE_BUDGETS).FID_GOOD) === 'good' ? 'default' : 'destructive'}
                >
                  {getMetricStatus((metrics).fid, (PERFORMANCE_BUDGETS).FID_GOOD) === 'good' ? 'Good' : 'Poor'}
                </Badge>
                <span className="text-xs text-gray-400">
                  Target: &lt;{(PERFORMANCE_BUDGETS).FID_GOOD}ms
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cumulative Layout Shift */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-gray-400 flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Cumulative Layout Shift (CLS)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-white">
                {(metrics).cls?.toFixed(3) || 'N/A'}
              </div>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={getMetricStatus((metrics).cls, (PERFORMANCE_BUDGETS).CLS_GOOD) === 'good' ? 'default' : 'destructive'}
                >
                  {getMetricStatus((metrics).cls, (PERFORMANCE_BUDGETS).CLS_GOOD) === 'good' ? 'Good' : 'Poor'}
                </Badge>
                <span className="text-xs text-gray-400">
                  Target: &lt;{(PERFORMANCE_BUDGETS).CLS_GOOD}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* First Contentful Paint */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs text-gray-400">First Contentful Paint</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-semibold text-white">
              {formatMs((metrics).fcp)}
            </div>
          </CardContent>
        </Card>

        {/* Time to First Byte */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs text-gray-400">Time to First Byte</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-semibold text-white">
              {formatMs((metrics).ttfb)}
            </div>
          </CardContent>
        </Card>

        {/* DOM Content Loaded */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs text-gray-400">DOM Content Loaded</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-semibold text-white">
              {formatMs((metrics).domContentLoaded)}
            </div>
          </CardContent>
        </Card>

        {/* Load Complete */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-xs text-gray-400">Load Complete</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-semibold text-white">
              {formatMs((metrics).loadComplete)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resource and Memory Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Resource Usage */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Resource Usage
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Total Resources:</span>
              <span className="text-white">{(metrics).resourceCount || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Total Size:</span>
              <span className="text-white">{formatBytes((metrics).totalResourceSize)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">JS Heap Used:</span>
              <span className="text-white">{formatBytes((metrics).usedJSHeapSize)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">JS Heap Total:</span>
              <span className="text-white">{formatBytes((metrics).totalJSHeapSize)}</span>
            </div>
          </CardContent>
        </Card>

        {/* Network Info */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Wifi className="h-5 w-5" />
              Network Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Connection Type:</span>
              <span className="text-white">{(metrics).connectionType || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Effective Type:</span>
              <span className="text-white">{(metrics).effectiveType || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Downlink:</span>
              <span className="text-white">
                {(metrics).downlink ? `${(metrics).downlink} Mbps` : 'Unknown'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Viewport:</span>
              <span className="text-white">
                {(metrics).viewport ? `${(metrics).viewport.width}×${(metrics).viewport.height}` : 'Unknown'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Warnings */}
      {(score).score < 50 && (<Card className="bg-red-900/20 border-red-700">
          <CardHeader>
            <CardTitle className="text-red-400 flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Performance Warnings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {(Object).entries((score).details)?.map(([metric, data]) => {
                if ((data).status === 'poor') {
                  return (<div key={metric} className="flex items-center gap-2 text-red-400">
                      <TrendingDown className="h-4 w-4" />
                      <span>
                        Poor {(metric).toUpperCase()}: {(data).(value).toFixed(metric === 'cls' ? 3 : 0)}
                        {metric === 'cls' ? '' : 'ms'} (threshold: {(data).threshold}
                        {metric === 'cls' ? '' : 'ms'})
                      </span>
                    </div>
                  )
                }
                return null
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default PerformanceDashboard
