import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { 
  Eye, 
  EyeOff, 
  CheckCircle, 
  AlertCircle, 
  Lock, 
  User, 
  Shield, 
  Zap,
  Check,
  X,
  Loader2
} from 'lucide-react'

interface PasswordValidation {
  is_valid: boolean
  strength_score: number
  strength_level: string
  feedback: string[]
  errors?: string[]
}

interface EmployeeInfo {
  name: string
  email: string
  position: string
  department?: string
}

interface ActivationResponse {
  message: string
  employee?: EmployeeInfo
  errors?: string[]
  feedback?: string[]
  strength_score?: number
}
export default function EnhancedEmployeeActivation(): React.ReactElement {
  const { token } = useParams<{ token: string }>()
  const navigate = useNavigate()

  // State management
  const [loading, setLoading] = useState(true)
  const [activating, setActivating] = useState(false)
  const [isActivated, setIsActivated] = useState(false)
  const [employee, setEmployee] = useState<EmployeeInfo | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  // Password form state
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordError, setPasswordError] = useState<string | null>(null)

  // Password validation state
  const [passwordValidation, setPasswordValidation] = useState<PasswordValidation | null>(null)
  const [validatingPassword, setValidatingPassword] = useState(false)

  // Mobile detection
  const [isMobile, setIsMobile] = useState(false)
  useEffect(() => {
    // Detect mobile device
    setIsMobile(window.innerWidth < 768)
    
    // Handle window resize
    const handleResize = (): any => setIsMobile(window.innerWidth < 768)
    window.addEventListener('resize', handleResize)
    
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  useEffect(() => {
    if (token) {
      checkActivationToken()
    }
  }, [token])

  // Real-time password validation
  useEffect(() => {
    if ((password).length > 0) {
      validatePasswordStrength()
    } else {
      setPasswordValidation(null)
    }
  }, [password])

  const checkActivationToken = async () => {
    try {
      setLoading(true)
      setError(null)
      const API_BASE_URL = (import).meta.(env).VITE_API_BASE_URL || 'http://localhost:8000/api'
      const response = await fetch(`${API_BASE_URL}/auth/activate/${token}/`)
      const data: ActivationResponse = await (response).json()

      if (!(response).ok) {
        setError((data).message || 'رابط التفعيل غير صالح أو منتهي الصلاحية.')
        return
      }

      if ((data).employee) {
        setEmployee((data).employee)
      }
    } catch (error) {
      console.error('Error checking activation token:', error)
      setError('حدث خطأ في التحقق من رابط التفعيل.')
    } finally {
      setLoading(false)
    }
  }

  const validatePasswordStrength = async () => {
    if (!password || (password).length < 3) return

    try {
      setValidatingPassword(true)
      const API_BASE_URL = (import).meta.(env).VITE_API_BASE_URL || 'http://localhost:8000/api'
      const response = await fetch(`${API_BASE_URL}/auth/validate-password/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password })
      })
      const data: PasswordValidation = await (response).json()
      setPasswordValidation(data)
    } catch (error) {
      console.error('Error validating password:', error)
    } finally {
      setValidatingPassword(false)
    }
  }
  const handleActivation = async (e: React.FormEvent) => {
    (e).preventDefault()

    if (!password || !confirmPassword) {
      setPasswordError('يرجى إدخال كلمة المرور وتأكيدها.')
      return
    }

    if (password !== confirmPassword) {
      setPasswordError('كلمات المرور غير متطابقة.')
      return
    }

    if (passwordValidation && !(passwordValidation).is_valid) {
      setPasswordError('كلمة المرور لا تلبي متطلبات الأمان.')
      return
    }

    try {
      setActivating(true)
      setError(null)
      setPasswordError(null)
      const API_BASE_URL = (import).meta.(env).VITE_API_BASE_URL || 'http://localhost:8000/api'
      const response = await fetch(`${API_BASE_URL}/auth/activate/${token}/complete/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          password,
          confirm_password: confirmPassword
        })
      })
      const data: ActivationResponse = await (response).json()

      if (!(response).ok) {
        if ((data).errors && (data).errors.length > 0) {
          setPasswordError((data).errors.join(', '))
        } else {
          setError((data).message || 'حدث خطأ أثناء تفعيل الحساب.')
        }
        return
      }

      setSuccess('تم تفعيل الحساب بنجاح! يمكنك الآن تسجيل الدخول.')
      setIsActivated(true)

      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login')
      }, 3000)

    } catch (error) {
      console.error('Error activating account:', error)
      setError('حدث خطأ أثناء تفعيل الحساب.')
    } finally {
      setActivating(false)
    }
  }

  const getStrengthColor = (level: string): void => {
    switch (level) {
      case 'very_strong': return 'text-green-500'
      case 'strong': return 'text-blue-500'
      case 'medium': return 'text-yellow-500'
      case 'weak': return 'text-orange-500'
      case 'very_weak': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getStrengthText = (level: string): void => {
    switch (level) {
      case 'very_strong': return 'قوية جداً'
      case 'strong': return 'قوية'
      case 'medium': return 'متوسطة'
      case 'weak': return 'ضعيفة'
      case 'very_weak': return 'ضعيفة جداً'
      default: return 'غير محددة'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center p-4">
        <Card className="w-full max-w-md glass-card border-white/20">
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-white" />
            <span className="ml-2 text-white">جاري التحقق من رابط التفعيل...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-600 to-pink-700 flex items-center justify-center p-4">
        <Card className="w-full max-w-md glass-card border-white/20">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <CardTitle className="text-white">خطأ في التفعيل</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className="border-red-500/50 bg-red-500/10">
              <AlertDescription className="text-white">
                {error}
              </AlertDescription>
            </Alert>
            <Button 
              onClick={() => navigate('/login')} 
              className="w-full mt-4"
              variant="outline"
            >
              العودة إلى تسجيل الدخول
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isActivated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-600 to-emerald-700 flex items-center justify-center p-4">
        <Card className="w-full max-w-md glass-card border-white/20">
          <CardHeader className="text-center">
            <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
            <CardTitle className="text-white">تم التفعيل بنجاح!</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert className="border-green-500/50 bg-green-500/10">
              <AlertDescription className="text-white">
                {success}
              </AlertDescription>
            </Alert>
            <div className="mt-4 text-center text-white/70">
              سيتم توجيهك إلى صفحة تسجيل الدخول خلال 3 ثوانٍ...
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (<div className="min-h-screen bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center p-4">
      <Card className={`w-full ${isMobile ? 'max-w-sm' : 'max-w-md'} glass-card border-white/20`}>
        <CardHeader className="text-center">
          <Shield className="h-12 w-12 text-blue-400 mx-auto mb-4" />
          <CardTitle className="text-white text-xl">تفعيل حساب الموظف</CardTitle>
          <CardDescription className="text-white/70">
            {employee ? `مرحباً ${(employee).name}` : 'قم بإنشاء كلمة مرور لتفعيل حسابك'}
          </CardDescription>
        </CardHeader>

        <CardContent>
          {employee && (<div className="mb-6 p-4 bg-white/10 rounded-lg border border-white/20">
              <div className="flex items-center gap-2 mb-2">
                <User className="h-4 w-4 text-blue-400" />
                <span className="text-white font-medium">{(employee).name}</span>
              </div>
              <div className="text-sm text-white/70 space-y-1">
                <div>{(employee).email}</div>
                <div>{(employee).position}</div>
                {(employee).department && <div>{(employee).department}</div>}
              </div>
            </div>
          )}

          <form onSubmit={handleActivation} className="space-y-4">
            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-white">
                كلمة المرور الجديدة
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e: any) => setPassword((e).target.value)}
                  className="pl-10 pr-10 glass-input"
                  placeholder="أدخل كلمة مرور قوية"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>

              {/* Password Strength Indicator */}
              {passwordValidation && (<div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-white/70">قوة كلمة المرور:</span>
                    <Badge 
                      variant="outline" 
                      className={`${getStrengthColor((passwordValidation).strength_level)} border-current`}
                    >
                      {getStrengthText((passwordValidation).strength_level)}
                    </Badge>
                  </div>
                  <Progress 
                    value={(passwordValidation).strength_score} 
                    className="h-2"
                  />
                  {(passwordValidation).feedback && (passwordValidation).feedback.length > 0 && (<div className="text-xs text-white/60 space-y-1">
                      {(passwordValidation).feedback.map((tip, index) => (
                        <div key={index} className="flex items-center gap-1">
                          <Zap className="h-3 w-3" />
                          {tip}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Confirm Password Field */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-white">
                تأكيد كلمة المرور
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(e: any) => setConfirmPassword((e).target.value)}
                  className="pl-10 pr-10 glass-input"
                  placeholder="أعد إدخال كلمة المرور"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white"
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>

              {/* Password Match Indicator */}
              {confirmPassword && (
                <div className="flex items-center gap-2 text-sm">
                  {password === confirmPassword ? (
                    <>
                      <Check className="h-4 w-4 text-green-400" />
                      <span className="text-green-400">كلمات المرور متطابقة</span>
                    </>
                  ) : (
                    <>
                      <X className="h-4 w-4 text-red-400" />
                      <span className="text-red-400">كلمات المرور غير متطابقة</span>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Error Display */}
            {passwordError && (
              <Alert className="border-red-500/50 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-white">
                  {passwordError}
                </AlertDescription>
              </Alert>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700"
              disabled={
                activating || 
                !password || 
                !confirmPassword || 
                password !== confirmPassword ||
                (passwordValidation && !(passwordValidation).is_valid)
              }
            >
              {activating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  جاري التفعيل...
                </>
              ) : (
                'تفعيل الحساب'
              )}
            </Button>
          </form>

          {/* Help Text */}
          <div className="mt-4 text-xs text-white/60 text-center">
            بتفعيل حسابك، فإنك توافق على شروط الاستخدام وسياسة الخصوصية
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
