/**
 * Global Search Component
 * Advanced search with real-time suggestions, filters, and results
 */

import React, { useState, useEffect, useRef, memo, useCallback, useMemo } from 'react'
import { Search, Filter, X, Clock, TrendingUp, FileText, Users, Building, Package } from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { searchService, SearchResult, SearchQuery } from '../../services/search'
import { debounce } from '../../utils/performance'

interface GlobalSearchProps {
  isOpen: boolean
  onClose: () => void
  language: 'ar' | 'en'
  placeholder?: string
  onResultClick?: (result: SearchResult) => void
}

const translations = {
  ar: {
    search: 'بحث',
    searchPlaceholder: 'ابحث في جميع أنحاء النظام...',
    recentSearches: 'عمليات البحث الأخيرة',
    trending: 'الأكثر بحثاً',
    suggestions: 'اقتراحات',
    filters: 'المرشحات',
    results: 'النتائج',
    noResults: 'لا توجد نتائج',
    searchIn: 'البحث في',
    allModules: 'جميع الوحدات',
    employees: 'الموظفين',
    departments: 'الأقسام',
    projects: 'المشاريع',
    documents: 'المستندات',
    clearHistory: 'مسح السجل',
    viewAll: 'عرض الكل',
    loading: 'جاري البحث...',
    searchTips: 'نصائح البحث',
    useQuotes: 'استخدم علامات التنصيص للبحث الدقيق',
    useOperators: 'استخدم + للكلمات المطلوبة و - للاستبعاد'
  },
  en: {
    search: 'Search',
    searchPlaceholder: 'Search across the entire system...',
    recentSearches: 'Recent Searches',
    trending: 'Trending',
    suggestions: 'Suggestions',
    filters: 'Filters',
    results: 'Results',
    noResults: 'No results found',
    searchIn: 'Search in',
    allModules: 'All Modules',
    employees: 'Employees',
    departments: 'Departments',
    projects: 'Projects',
    documents: 'Documents',
    clearHistory: 'Clear History',
    viewAll: 'View All',
    loading: 'Searching...',
    searchTips: 'Search Tips',
    useQuotes: 'Use quotes for exact phrases',
    useOperators: 'Use + for required words and - to exclude'
  }
}

const moduleIcons = {
  employees: Users,
  departments: Building,
  projects: Package,
  documents: FileText,
  default: Search
}
const GlobalSearch: React.FC<GlobalSearchProps> = memo(({
  isOpen,
  onClose,
  language,
  placeholder,
  onResultClick
}) => {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [trendingSearches, setTrendingSearches] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [selectedModule, setSelectedModule] = useState<string>('all')
  const [activeTab, setActiveTab] = useState<'suggestions' | 'recent' | 'trending'>('suggestions')
  const inputRef = useRef<HTMLInputElement>(null)
  const t = useMemo(() => translations[language], [language])
  const isRTL = useMemo(() => language === 'ar', [language])

  // FIXED: Debounced search function with proper closure handling
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string, moduleContext?: string) => {
      if (searchQuery.trim().length < 2) {
        setResults([])
        return
      }

      setIsLoading(true)
      try {
        const searchParams: SearchQuery = {
          query: searchQuery,
          pagination: { page: 1, limit: 10 }
        }

        // FIXED: Use parameter instead of closure to avoid stale values
        const currentModule = moduleContext || selectedModule
        const response = currentModule === 'all'
          ? await searchService.globalSearch(searchParams)
          : await searchService.moduleSearch(currentModule, searchParams)

        setResults(response.results)
      } catch (error) {
        console.error('Search error:', error)
        setResults([])
      } finally {
        setIsLoading(false)
      }
    }, 300),
    [] // FIXED: Remove selectedModule from dependencies to prevent recreation
  )

  // Debounced suggestions function
  const debouncedSuggestions = useCallback(
    debounce(async (searchQuery: string) => {
      if (searchQuery.trim().length < 1) {
        setSuggestions([])
        return
      }

      try {
        const suggestions = await searchService.getSuggestions(searchQuery,
          selectedModule === 'all' ? undefined : selectedModule)
        setSuggestions(suggestions)
      } catch (error) {
        console.error('Suggestions error:', error)
        setSuggestions([])
      }
    }, 200),
    [selectedModule]
  )

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)

    if (value.trim()) {
      // FIXED: Pass current selectedModule to avoid stale closure
      debouncedSearch(value, selectedModule)
      debouncedSuggestions(value)
      setActiveTab('suggestions')
    } else {
      setResults([])
      setSuggestions([])
      setActiveTab('recent')
    }
  }, [debouncedSearch, debouncedSuggestions, selectedModule])

  // Handle suggestion click
  const handleSuggestionClick = useCallback((suggestion: string) => {
    setQuery(suggestion)
    // FIXED: Pass current selectedModule to avoid stale closure
    debouncedSearch(suggestion, selectedModule)
    setActiveTab('suggestions')
  }, [debouncedSearch, selectedModule])

  // Handle result click
  const handleResultClick = useCallback((result: SearchResult) => {
    if (onResultClick) {
      onResultClick(result)
    }
    onClose()
  }, [onResultClick, onClose])

  // Load initial data
  useEffect(() => {
    if (isOpen) {
      setRecentSearches(searchService.getRecentSearches())

      // Load trending searches
      searchService.getTrendingSearches().then(trending => {
        setTrendingSearches(trending)
      })

      // Focus input
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [isOpen])

  // Clear search history
  const clearHistory = useCallback(() => {
    searchService.clearSearchHistory()
    setRecentSearches([])
  }, [])

  if (!isOpen) return null

  return (<div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
      <div className="flex items-start justify-center min-h-screen pt-20 px-4">
        <div
          className="w-full max-w-2xl bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl transform transition-all duration-200 ease-out"
          style={{
            minHeight: '400px', // FIXED: Reserve minimum space to prevent layout shift
            maxHeight: '80vh'   // FIXED: Prevent modal from growing too large
          }}
        >
          {/* Header */}
          <div className="flex items-center gap-4 p-6 border-b border-white/10">
            <div className="flex-1 relative">
              <Search className={`absolute top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/60 ${
                isRTL ? 'right-3' : 'left-3'
              }`} />
              <Input
                ref={inputRef}
                value={query}
                onChange={handleInputChange}
                placeholder={placeholder || t.searchPlaceholder}
                className={`w-full bg-white/5 border-white/20 text-white placeholder-white/60 ${
                  isRTL ? 'pr-10 text-right' : 'pl-10'
                }`}
              />
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="text-white hover:bg-white/10"
            >
              <Filter className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-white hover:bg-white/10"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Filters */}
          {showFilters && (<div className="p-4 border-b border-white/10">
              <div className="flex flex-wrap gap-2">
                {['all', 'employees', 'departments', 'projects', 'documents'].map((module) => (
                  <Button
                    key={module}
                    variant={selectedModule === module ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedModule(module)}
                    className={`text-xs ${
                      selectedModule === module
                        ? 'bg-blue-500 text-white'
                        : 'bg-white/5 text-white border-white/20 hover:bg-white/10'
                    }`}
                  >
                    {module === 'all' ? t.allModules : t[module as keyof typeof t] || module}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            {query.trim() ? (// Search Results
              <div className="p-4">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                    <span className="ml-3 text-white">{t.loading}</span>
                  </div>
                ) : results.length > 0 ? (<div className="space-y-2">
                    <h3 className="text-sm font-medium text-white/80 mb-3">{t.results}</h3>
                    {results.map((result) => {
                      const IconComponent = moduleIcons[result.type as keyof typeof moduleIcons] || moduleIcons.default
                      return (<div
                          key={result.id}
                          onClick={() => handleResultClick(result)}
                          className="p-3 rounded-lg bg-white/5 hover:bg-white/10 cursor-pointer transition-all duration-200 border border-white/10"
                        >
                          <div className="flex items-start gap-3">
                            <div className="p-2 rounded-lg bg-blue-500/20">
                              <IconComponent className="h-4 w-4 text-blue-400" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h4 className="text-white font-medium truncate">{result.title}</h4>
                              <p className="text-white/60 text-sm mt-1 line-clamp-2">{result.description}</p>
                              <div className="flex items-center gap-2 mt-2">
                                <span className="text-xs px-2 py-1 rounded bg-white/10 text-white/80">
                                  {result.type}
                                </span>
                                <span className="text-xs text-white/40">
                                  {new Date(result.updatedAt).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ) : (<div className="text-center py-8">
                    <Search className="h-12 w-12 text-white/40 mx-auto mb-3" />
                    <p className="text-white/60">{t.noResults}</p>
                  </div>
                )}

                {/* Suggestions */}
                {suggestions.length > 0 && (<div className="mt-6">
                    <h3 className="text-sm font-medium text-white/80 mb-3">{t.suggestions}</h3>
                    <div className="space-y-1">
                      {suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion)}
                          className="w-full text-left p-2 rounded-lg hover:bg-white/5 text-white/80 text-sm transition-colors"
                        >
                          <Search className="h-3 w-3 inline mr-2" />
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              // Empty State with Tabs
              <div className="p-4">
                {/* Tabs */}
                <div className="flex gap-1 mb-4">
                  {(['recent', 'trending'] as const).map((tab) => (
                    <button
                      key={tab}
                      onClick={() => setActiveTab(tab)}
                      className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                        activeTab === tab
                          ? 'bg-white/10 text-white'
                          : 'text-white/60 hover:text-white hover:bg-white/5'
                      }`}
                    >
                      {tab === 'recent' ? t.recentSearches : t.trending}
                    </button>
                  ))}
                </div>

                {/* Tab Content */}
                {activeTab === 'recent' && (<div>
                    {recentSearches.length > 0 ? (<div className="space-y-1">
                        {recentSearches.map((search, index) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(search)}
                            className="w-full text-left p-2 rounded-lg hover:bg-white/5 text-white/80 text-sm transition-colors flex items-center gap-2"
                          >
                            <Clock className="h-3 w-3" />
                            {search}
                          </button>
                        ))}
                        <button
                          onClick={clearHistory}
                          className="w-full text-left p-2 text-red-400 hover:bg-red-500/10 text-sm rounded-lg transition-colors"
                        >
                          {t.clearHistory}
                        </button>
                      </div>
                    ) : (
                      <p className="text-white/40 text-sm text-center py-4">
                        لا توجد عمليات بحث سابقة
                      </p>
                    )}
                  </div>
                )}

                {activeTab === 'trending' && (<div>
                    {trendingSearches.length > 0 ? (<div className="space-y-1">
                        {trendingSearches.map((search, index) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(search)}
                            className="w-full text-left p-2 rounded-lg hover:bg-white/5 text-white/80 text-sm transition-colors flex items-center gap-2"
                          >
                            <TrendingUp className="h-3 w-3" />
                            {search}
                          </button>
                        ))}
                      </div>
                    ) : (
                      <p className="text-white/40 text-sm text-center py-4">
                        لا توجد عمليات بحث رائجة
                      </p>
                    )}
                  </div>
                )}

                {/* Search Tips */}
                <div className="mt-6 p-3 rounded-lg bg-white/5 border border-white/10">
                  <h4 className="text-white font-medium text-sm mb-2">{t.searchTips}</h4>
                  <ul className="text-white/60 text-xs space-y-1">
                    <li>• {t.useQuotes}</li>
                    <li>• {t.useOperators}</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
})

GlobalSearch.displayName = 'GlobalSearch'

export default GlobalSearch
