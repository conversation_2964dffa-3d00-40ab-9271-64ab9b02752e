import React from 'react';
import { useSelector } from 'react-redux';
import type { RootState } from '../../store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const RoleDebugger: React.FC = () => {
  const { user, isAuthenticated } = useSelector((state: RootState) => state.auth);

  if (!isAuthenticated) {
    return (
      <Card className="m-4">
        <CardHeader>
          <CardTitle>Role Debugger - Not Authenticated</CardTitle>
        </CardHeader>
        <CardContent>
          <p>User is not authenticated</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="m-4">
      <CardHeader>
        <CardTitle>Role Debugger</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-semibold">User Info:</h3>
          <p><strong>ID:</strong> {user?.id}</p>
          <p><strong>Username:</strong> {user?.username}</p>
          <p><strong>Email:</strong> {user?.email}</p>
          <p><strong>Name:</strong> {user?.first_name} {user?.last_name}</p>
        </div>

        <div>
          <h3 className="font-semibold">Role Info:</h3>
          {user?.role ? (
            <div className="space-y-2">
              <p><strong>Role ID:</strong> <Badge>{user.role.id}</Badge></p>
              <p><strong>Role Name:</strong> <Badge>{user.role.name}</Badge></p>
              <p><strong>Role Name (AR):</strong> <Badge>{user.role.nameAr}</Badge></p>
              <p><strong>Role Level:</strong> <Badge>{(user.role).level}</Badge></p>
              
              <div>
                <strong>Permissions:</strong>
                <div className="flex flex-wrap gap-1 mt-1">
                  {user.role.permissions && user.role.permissions.length > 0 ? (
                    user.role.permissions.map((permission: any, index: number) => (
                      <Badge key={index} variant="outline">
                        {typeof permission === 'string' ? permission : JSON.stringify(permission)}
                      </Badge>
                    ))
                  ) : (
                    <Badge variant="destructive">No permissions</Badge>
                  )}
                </div>
              </div>

              <div>
                <strong>Dashboard Config:</strong>
                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                  {JSON.stringify((user.role).dashboardConfig, null, 2)}
                </pre>
              </div>
            </div>
          ) : (
            <Badge variant="destructive">No role assigned</Badge>
          )}
        </div>

        <div>
          <h3 className="font-semibold">Role Mapping Test:</h3>
          <div className="space-y-1">
            {user?.role && (
              <>
                <p><strong>Current Role Name:</strong> {user.role.name}</p>
                <p><strong>Is SUPERADMIN:</strong> {user.role.name === 'SUPERADMIN' ? '✅' : '❌'}</p>
                <p><strong>Is ADMIN:</strong> {user.role.name === 'ADMIN' ? '✅' : '❌'}</p>
                <p><strong>Is HR_MANAGER:</strong> {user.role.name === 'HR_MANAGER' ? '✅' : '❌'}</p>
                <p><strong>Is FINANCE_MANAGER:</strong> {user.role.name === 'FINANCE_MANAGER' ? '✅' : '❌'}</p>
                <p><strong>Is EMPLOYEE:</strong> {user.role.name === 'EMPLOYEE' ? '✅' : '❌'}</p>
              </>
            )}
          </div>
        </div>

        <div>
          <h3 className="font-semibold">Route Access Test:</h3>
          <div className="space-y-1">
            {user?.role && (
              <>
                <p><strong>Can access finance_manager routes:</strong> {
                  user.role.name === 'SUPERADMIN' || 
                  user.role.name === 'ADMIN' || 
                  user.role.name === 'FINANCE_MANAGER' ? '✅' : '❌'
                }</p>
                <p><strong>Can access hr_manager routes:</strong> {
                  user.role.name === 'SUPERADMIN' || 
                  user.role.name === 'ADMIN' || 
                  user.role.name === 'HR_MANAGER' ? '✅' : '❌'
                }</p>
                <p><strong>Can access admin routes:</strong> {
                  user.role.name === 'SUPERADMIN' || 
                  user.role.name === 'ADMIN' ? '✅' : '❌'
                }</p>
              </>
            )}
          </div>
        </div>

        <div>
          <h3 className="font-semibold">Full User Object:</h3>
          <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto max-h-64">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>
      </CardContent>
    </Card>
  );
};

export default RoleDebugger;
