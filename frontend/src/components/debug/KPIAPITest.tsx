/**
 * KPI API Test Component
 * Simple component to test KPI CRUD operations
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { kpiService } from '@/services/crudService'
export default function KPIAPITest(): React.ReactElement {
  const [results, setResults] = useState<string[]>([])
  const [loading, setLoading] = useState(false)

  const addResult = (message: string): void => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testGetAll = async () => {
    setLoading(true)
    try {
      addResult('🔍 Testing GET /api/kpi-metrics/')
      const response = await (kpiService).getAll()
      addResult(`✅ GET Success: Found ${(response).data?.length || 0} KPIs`)
      console.log('KPI GET Response:', response)
    } catch (error) {
      addResult(`❌ GET Error: ${error}`)
      console.error('KPI GET Error:', error)
    }
    setLoading(false)
  }

  const testCreate = async () => {
    setLoading(true)
    try {
      addResult('📝 Testing POST /api/kpi-metrics/')
      const testData = {
        name: 'Test KPI',
        name_ar: 'مؤشر اختبار',
        description: 'Test KPI for API validation',
        description_ar: 'مؤشر اختبار للتحقق من API',
        metric_type: 'FINANCIAL',
        calculation_method: 'MANUAL',
        target_value: 100,
        unit: '%',
        frequency: 'MONTHLY',
        is_active: true,
        is_higher_better: true
      }
      
      const response = await (kpiService).create(testData)
      addResult(`✅ CREATE Success: Created KPI with ID ${(response).id}`)
      console.log('KPI CREATE Response:', response)
    } catch (error) {
      addResult(`❌ CREATE Error: ${error}`)
      console.error('KPI CREATE Error:', error)
    }
    setLoading(false)
  }

  const clearResults = (): void => {
    setResults([])
  }

  return (<div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">KPI API Test</h2>
      
      <div className="space-y-4">
        <div className="flex gap-2">
          <Button onClick={testGetAll} disabled={loading}>
            Test GET All KPIs
          </Button>
          <Button onClick={testCreate} disabled={loading}>
            Test Create KPI
          </Button>
          <Button onClick={clearResults} variant="outline">
            Clear Results
          </Button>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg h-64 overflow-y-auto">
          <h3 className="font-semibold mb-2">Test Results:</h3>
          {(results).length === 0 ? (
            <p className="text-gray-500">No tests run yet</p>
          ) : (<div className="space-y-1">
              {(results).map((result, index) => (
                <div key={index} className="text-sm font-mono">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
