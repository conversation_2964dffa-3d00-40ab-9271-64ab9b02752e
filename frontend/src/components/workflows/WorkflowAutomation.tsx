import React from 'react';
/**
 * Workflow Automation Component with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Workflow,
  Play,
  Edit,
  Trash2,
  Clock,
  Zap,
  Users,
  FileText,
  Database,
  GitBranch,
  Eye
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { workflowService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface WorkflowAutomationProps {
  language: 'ar' | 'en'
}

interface WorkflowRule {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  triggerType: string
  triggerCondition: string
  triggerValue: string
  actions: string // JSON string of actions array
  status: 'active' | 'inactive' | 'draft'
  category: string
  categoryAr: string
  lastRun?: string
  runCount: number
  createdDate: string
  updatedDate: string
}

const translations = {
  ar: {
    workflows: 'أتمتة سير العمل',
    addWorkflow: 'إضافة سير عمل',
    editWorkflow: 'تعديل سير العمل',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف سير العمل هذا؟',
    searchPlaceholder: 'البحث في سير العمل...',
    name: 'الاسم',
    description: 'الوصف',
    triggerType: 'نوع المحفز',
    triggerCondition: 'شرط المحفز',
    triggerValue: 'قيمة المحفز',
    actions: 'الإجراءات',
    status: 'الحالة',
    category: 'الفئة',
    lastRun: 'آخر تشغيل',
    runCount: 'عدد مرات التشغيل',
    createdDate: 'تاريخ الإنشاء',
    updatedDate: 'تاريخ التحديث',
    active: 'نشط',
    inactive: 'غير نشط',
    draft: 'مسودة',
    triggers: {
      newEmployee: 'موظف جديد',
      leaveRequest: 'طلب إجازة',
      lowStock: 'مخزون منخفض',
      projectDeadline: 'موعد تسليم مشروع',
      salesTarget: 'هدف مبيعات',
      attendanceLate: 'تأخير في الحضور'
    },
    categories: {
      hr: 'الموارد البشرية',
      sales: 'المبيعات',
      inventory: 'المخزون',
      projects: 'المشاريع',
      finance: 'المالية'
    }
  },
  en: {
    workflows: 'Workflow Automation',
    addWorkflow: 'Add Workflow',
    editWorkflow: 'Edit Workflow',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this workflow?',
    searchPlaceholder: 'Search workflows...',
    name: 'Name',
    description: 'Description',
    triggerType: 'Trigger Type',
    triggerCondition: 'Trigger Condition',
    triggerValue: 'Trigger Value',
    actions: 'Actions',
    status: 'Status',
    category: 'Category',
    lastRun: 'Last Run',
    runCount: 'Run Count',
    createdDate: 'Created Date',
    updatedDate: 'Updated Date',
    active: 'Active',
    inactive: 'Inactive',
    draft: 'Draft',
    triggers: {
      newEmployee: 'New Employee',
      leaveRequest: 'Leave Request',
      lowStock: 'Low Stock',
      projectDeadline: 'Project Deadline',
      salesTarget: 'Sales Target',
      attendanceLate: 'Late Attendance'
    },
    categories: {
      hr: 'Human Resources',
      sales: 'Sales',
      inventory: 'Inventory',
      projects: 'Projects',
      finance: 'Finance'
    }
  }
}
export default function WorkflowAutomation({ language }: WorkflowAutomationProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: workflows,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<WorkflowRule>({
    service: workflowService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200'
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getCategoryIcon = (category: string): void => {
    switch (category) {
      case 'hr': return <Users className="h-4 w-4 text-blue-400" />
      case 'sales': return <Zap className="h-4 w-4 text-green-400" />
      case 'inventory': return <Database className="h-4 w-4 text-purple-400" />
      case 'projects': return <GitBranch className="h-4 w-4 text-orange-400" />
      case 'finance': return <FileText className="h-4 w-4 text-red-400" />
      default: return <Workflow className="h-4 w-4 text-gray-400" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<WorkflowRule>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: WorkflowRule) => (<div className="flex items-center gap-2">
          {getCategoryIcon(item.category)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr?.substring(0, 50) + '...' : item.description?.substring(0, 50) + '...'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      render: (item: WorkflowRule) => (<span className="text-white/80">
          {language === 'ar' ? item.categoryAr : item.category}
        </span>
      )
    },
    {
      key: 'triggerType',
      label: t.triggerType,
      render: (item: WorkflowRule) => (<div className="flex items-center gap-1">
          <Zap className="h-3 w-3 text-yellow-400" />
          <span className="text-white/80">
            {t.triggers[item.triggerType as keyof typeof t.triggers] || item.triggerType}
          </span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: WorkflowRule) => (<Badge className={getStatusColor(item.status)}>
          {t[item.status] as string}
        </Badge>
      )
    },
    {
      key: 'runCount',
      label: t.runCount,
      sortable: true,
      render: (item: WorkflowRule) => (<div className="flex items-center gap-1">
          <Play className="h-3 w-3 text-green-400" />
          <span className="text-white font-medium">{item.runCount}</span>
        </div>
      )
    },
    {
      key: 'lastRun',
      label: t.lastRun,
      sortable: true,
      render: (item: WorkflowRule) => (item.lastRun ? (<div className="flex items-center gap-1">
            <Clock className="h-3 w-3 text-blue-400" />
            <span className="text-white/80">{item.lastRun}</span>
          </div>
        ) : (
          <span className="text-white/50">Never</span>
        )
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Record<string, unknown>) => {
        selectItem(item as unknown as WorkflowRule)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Record<string, unknown>) => {
        selectItem(item as unknown as WorkflowRule)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Record<string, unknown>) => {
        if (window.confirm(t.confirmDelete)) {
          deleteItem((item as unknown as WorkflowRule).id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.draft, value: 'draft' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.hr, value: 'hr' },
        { label: t.categories.sales, value: 'sales' },
        { label: t.categories.inventory, value: 'inventory' },
        { label: t.categories.projects, value: 'projects' },
        { label: t.categories.finance, value: 'finance' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'triggerType',
      label: t.triggerType,
      type: 'select',
      required: true,
      options: [
        { label: t.triggers.newEmployee, value: 'newEmployee' },
        { label: t.triggers.leaveRequest, value: 'leaveRequest' },
        { label: t.triggers.lowStock, value: 'lowStock' },
        { label: t.triggers.projectDeadline, value: 'projectDeadline' },
        { label: t.triggers.salesTarget, value: 'salesTarget' },
        { label: t.triggers.attendanceLate, value: 'attendanceLate' }
      ]
    },
    {
      name: 'triggerCondition',
      label: t.triggerCondition,
      type: 'text',
      required: true
    },
    {
      name: 'triggerValue',
      label: t.triggerValue,
      type: 'text',
      required: true
    },
    {
      name: 'actions',
      label: t.actions,
      type: 'textarea',
      placeholder: 'JSON array of actions'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.draft, value: 'draft' }
      ]
    },
    {
      name: 'runCount',
      label: t.runCount,
      type: 'number',
      min: 0
    },
    {
      name: 'lastRun',
      label: t.lastRun,
      type: 'datetime-local'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<WorkflowRule>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (<div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.workflows}
        data={workflows}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addWorkflow : modalMode === 'edit' ? t.editWorkflow : t.view}
        fields={formFields}
        initialData={selectedItem ? {
          name: selectedItem.name,
          nameAr: selectedItem.nameAr,
          description: selectedItem.description,
          descriptionAr: selectedItem.descriptionAr,
          triggerType: selectedItem.triggerType,
          triggerCondition: selectedItem.triggerCondition,
          triggerValue: selectedItem.triggerValue,
          actions: selectedItem.actions,
          status: selectedItem.status,
          category: selectedItem.category
        } : undefined}
        language={language}
        loading={creating || updating}
      />
    </div>
  )

}
