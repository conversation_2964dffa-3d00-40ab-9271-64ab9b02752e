/**
 * Employee Form Component with Advanced Validation
 * Demonstrates comprehensive form validation and API integration
 */

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Save, X } from 'lucide-react'
import { useEnhancedForm } from '@/hooks/useEnhancedForm'
import { employeeValidationSchema } from '@/utils/validation'
import { formValidationManager, useFormValidation } from '@/utils/formValidation'
import { useFormLoadingStates } from '@/hooks/useLoadingStates'
import { useFormErrorHandling } from '@/hooks/useErrorHandling'
import { SmartButton } from '@/components/ui/enhanced-loading'
import { enhancedAPI } from '@/services/enhancedAPI'
import { departmentAPI, Department } from '@/services/employeeAPI'

export interface EmployeeData {
  id?: number
  employee_id?: string
  first_name?: string
  last_name?: string
  first_name_ar?: string
  last_name_ar?: string
  email?: string
  phone?: string
  department?: string | number
  position?: string
  position_ar?: string
  hire_date?: string
  salary?: number
  employment_status?: string
  manager?: string | number
  work_location?: string
  gender?: string
  address?: string
  emergency_contact?: string
  emergency_phone?: string
  national_id?: string
  mobile?: string
  date_of_birth?: string
  nationality?: string
  passport_number?: string
  marital_status?: string
  is_active?: boolean
  bank_account?: string
  skills?: string
  education?: string
  certifications?: string
}

interface EmployeeFormProps {
  initialData?: EmployeeData
  onSuccess?: (data: EmployeeData) => void
  onCancel?: () => void
  language: 'ar' | 'en'
  mode?: 'create' | 'edit'
}

const translations = {
  ar: {
    employeeForm: 'نموذج الموظف',
    personalInfo: 'المعلومات الشخصية',
    workInfo: 'معلومات العمل',
    contactInfo: 'معلومات الاتصال',
    firstName: 'الاسم الأول',
    lastName: 'الاسم الأخير',
    email: 'البريد الإلكتروني',
    phone: 'رقم الهاتف',
    department: 'القسم',
    position: 'المنصب',
    salary: 'الراتب',
    hireDate: 'تاريخ التوظيف',
    address: 'العنوان',
    emergencyContact: 'جهة الاتصال الطارئة',
    nationalId: 'رقم الهوية الوطنية',
    save: 'حفظ',
    cancel: 'إلغاء',
    saving: 'جاري الحفظ...',
    selectDepartment: 'اختر القسم'
  },
  en: {
    employeeForm: 'Employee Form',
    personalInfo: 'Personal Information',
    workInfo: 'Work Information',
    contactInfo: 'Contact Information',
    firstName: 'First Name',
    lastName: 'Last Name',
    email: 'Email Address',
    phone: 'Phone Number',
    department: 'Department',
    position: 'Position',
    salary: 'Salary',
    hireDate: 'Hire Date',
    address: 'Address',
    emergencyContact: 'Emergency Contact',
    nationalId: 'National ID',
    save: 'Save',
    cancel: 'Cancel',
    saving: 'Saving...',
    selectDepartment: 'Select Department'
  }
}

export default function EmployeeForm({
  initialData,
  onSuccess,
  onCancel,
  language,
  mode = 'create'
}: EmployeeFormProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  // VALIDATION FIX: Register validation schema
  React.useEffect(() => {
    formValidationManager.registerSchema('employee', employeeValidationSchema)
  }, [])

  // State for departments
  const [departments, setDepartments] = useState<Department[]>([])
  const [loadingDepartments, setLoadingDepartments] = useState(true)

  // VALIDATION FIX: Use enhanced form validation
  const { validateField } = useFormValidation('employee', {
    language,
    realTimeValidation: true
  })

  // UX FIX: Enhanced loading states for better user feedback
  const formLoading = useFormLoadingStates(language)

  // ERROR FIX: Enhanced error handling for forms
  const { handleFormSubmit, hasError, error, clearError } = useFormErrorHandling(language)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    isSubmitting,
    getFieldError,
    loadingStates
  } = useEnhancedForm({
    defaultValues: initialData || {
      first_name: '',
      last_name: '',
      first_name_ar: '',
      last_name_ar: '',
      email: '',
      phone: '',
      department: '',
      position: '',
      position_ar: '',
      salary: 0,
      hire_date: '',
      address: '',
      emergency_contact: '',
      national_id: '',
      employee_id: ''
    },
    validationSchema: employeeValidationSchema,
    language,
    resetOnSuccess: false
  })

  // VALIDATION FIX: Debounced field validation to improve performance
  const [fieldErrors, setFieldErrors] = React.useState<Record<string, string>>({})
  const handleFieldChange = React.useCallback((fieldName: string, value: string | number) => {
    setValue(fieldName, value)

    // Debounce validation to avoid excessive calls
    const timeoutId = setTimeout(() => {
      const formData = watch()
      const error = validateField(fieldName, value, formData)

      setFieldErrors(prev => ({
        ...prev,
        [fieldName]: error || ''
      }))
    }, 300) // 300ms debounce

    return () => clearTimeout(timeoutId)
  }, [setValue, watch, validateField])

  // Load departments on component mount with caching
  useEffect(() => {
    const loadDepartments = async () => {
      try {
        setLoadingDepartments(true)

        // Check if departments are already cached
        const cachedDepartments = sessionStorage.getItem('departments')
        if (cachedDepartments) {
          const parsed = JSON.parse(cachedDepartments)
          const cacheTime = sessionStorage.getItem('departments_cache_time')
          const now = Date.now()

          // Use cache if less than 5 minutes old
          if (cacheTime && (now - parseInt(cacheTime)) < 5 * 60 * 1000) {
            setDepartments(parsed)
            setLoadingDepartments(false)
            return
          }
        }
        const departmentsData = await departmentAPI.getAll()
        setDepartments(departmentsData)

        // Cache departments for 5 minutes
        sessionStorage.setItem('departments', JSON.stringify(departmentsData))
        sessionStorage.setItem('departments_cache_time', Date.now().toString())
      } catch (error) {
        console.error('Failed to load departments:', error)
      } finally {
        setLoadingDepartments(false)
      }
    }
    loadDepartments()
  }, [])
  const onSubmit = handleSubmit(async (data) => {
    try {
      // Clear any previous errors
      clearError()

      if (mode === 'create') {
        await enhancedAPI.createEmployee(data, language)
      } else if (mode === 'edit' && initialData?.id) {
        await enhancedAPI.updateEmployee(initialData.id, data, language)
      }

      if (onSuccess) {
        onSuccess(data)
      }
    } catch (error) {
      console.error('Form submission error:', error)
      // Error handling is done by enhancedAPI and useEnhancedForm
    }
  })

  const departmentValue = watch('department')

  return (<div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.employeeForm}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit} className="space-y-6">
            {/* Personal Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">
                {t.personalInfo}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName" className="text-white">
                    {t.firstName} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="first_name"
                    {...register('first_name')}
                    className={`glass-input ${getFieldError('first_name') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل الاسم الأول' : 'Enter first name'}
                    disabled={isSubmitting}
                  />
                  {getFieldError('first_name') && (<p className="text-red-400 text-sm mt-1">{getFieldError('first_name')}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="lastName" className="text-white">
                    {t.lastName} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="last_name"
                    {...register('last_name')}
                    className={`glass-input ${getFieldError('last_name') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل الاسم الأخير' : 'Enter last name'}
                  />
                  {getFieldError('last_name') && (<p className="text-red-400 text-sm mt-1">{getFieldError('last_name')}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="nationalId" className="text-white">
                    {t.nationalId} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="national_id"
                    {...register('national_id')}
                    className={`glass-input ${getFieldError('national_id') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل رقم الهوية' : 'Enter national ID'}
                  />
                  {getFieldError('national_id') && (<p className="text-red-400 text-sm mt-1">{getFieldError('national_id')}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Work Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">
                {t.workInfo}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="departmentId" className="text-white">
                    {t.department} <span className="text-red-400">*</span>
                  </Label>
                  <Select
                    value={String(departmentValue || '')}
                    onValueChange={(value) => setValue('department', value as string)}
                    disabled={loadingDepartments}
                  >
                    <SelectTrigger className={`glass-input ${getFieldError('department') ? 'border-red-500' : ''}`}>
                      <SelectValue placeholder={loadingDepartments ? 'جاري التحميل...' : t.selectDepartment} />
                    </SelectTrigger>
                    <SelectContent className="glass-card border-white/20">
                      {loadingDepartments ? (
                        <SelectItem value="__loading__" disabled>
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            {language === 'ar' ? 'جاري تحميل الأقسام...' : 'Loading departments...'}
                          </div>
                        </SelectItem>
                      ) : departments.length > 0 ? (
                        departments.map((dept) => (<SelectItem key={dept.id} value={String(dept.id)}>
                            {language === 'ar' ? dept.name_ar || dept.name : dept.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="__no_data__" disabled>
                          {language === 'ar' ? 'لا توجد أقسام متاحة' : 'No departments available'}
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {getFieldError('department') && (<p className="text-red-400 text-sm mt-1">{getFieldError('department')}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="position" className="text-white">
                    {t.position} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="position"
                    {...register('position')}
                    className={`glass-input ${getFieldError('position') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل المنصب' : 'Enter position'}
                  />
                  {getFieldError('position') && (<p className="text-red-400 text-sm mt-1">{getFieldError('position')}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="salary" className="text-white">
                    {t.salary} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="salary"
                    type="number"
                    {...register('salary')}
                    className={`glass-input ${getFieldError('salary') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل الراتب' : 'Enter salary'}
                    min="1000"
                    max="1000000"
                  />
                  {getFieldError('salary') && (<p className="text-red-400 text-sm mt-1">{getFieldError('salary')}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="hireDate" className="text-white">
                    {t.hireDate} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="hire_date"
                    type="date"
                    {...register('hire_date')}
                    className={`glass-input ${getFieldError('hire_date') ? 'border-red-500' : ''}`}
                  />
                  {getFieldError('hire_date') && (<p className="text-red-400 text-sm mt-1">{getFieldError('hire_date')}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white border-b border-white/20 pb-2">
                {t.contactInfo}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email" className="text-white">
                    {t.email} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    className={`glass-input ${getFieldError('email') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل البريد الإلكتروني' : 'Enter email address'}
                  />
                  {getFieldError('email') && (<p className="text-red-400 text-sm mt-1">{getFieldError('email')}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="phone" className="text-white">
                    {t.phone} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    {...register('phone')}
                    className={`glass-input ${getFieldError('phone') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل رقم الهاتف' : 'Enter phone number'}
                  />
                  {getFieldError('phone') && (<p className="text-red-400 text-sm mt-1">{getFieldError('phone')}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="emergencyContact" className="text-white">
                    {t.emergencyContact} <span className="text-red-400">*</span>
                  </Label>
                  <Input
                    id="emergency_contact"
                    type="tel"
                    {...register('emergency_contact')}
                    className={`glass-input ${getFieldError('emergency_contact') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل رقم الاتصال الطارئ' : 'Enter emergency contact'}
                  />
                  {getFieldError('emergency_contact') && (<p className="text-red-400 text-sm mt-1">{getFieldError('emergency_contact')}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="address" className="text-white">
                    {t.address} <span className="text-red-400">*</span>
                  </Label>
                  <Textarea
                    id="address"
                    {...register('address')}
                    className={`glass-input ${getFieldError('address') ? 'border-red-500' : ''}`}
                    placeholder={language === 'ar' ? 'أدخل العنوان' : 'Enter address'}
                    rows={3}
                  />
                  {getFieldError('address') && (<p className="text-red-400 text-sm mt-1">{getFieldError('address')}</p>
                  )}
                </div>
              </div>
            </div>

            {/* UX FIX: Enhanced Form Actions with Smart Buttons */}
            <div className="flex gap-4 pt-4">
              <SmartButton
                type="submit"
                className="glass-button flex-1"
                isLoading={isSubmitting || formLoading.isSubmitting()}
                loadingText={t.saving}
                loadingTextAr="جاري الحفظ..."
                success={false} // Will be handled by form submission
                language={language}
                disabled={Object.keysloadingStates.some(key => loadingStates[key])}
              >
                <Save className="h-4 w-4 mr-2" />
                {t.save}
              </SmartButton>

              {onCancel && (<Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  className="glass-button"
                  disabled={isSubmitting || formLoading.isSubmitting()}
                >
                  <X className="h-4 w-4 mr-2" />
                  {t.cancel}
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
