import React from 'react'
import { render, screen, fireEvent, waitFor } from '../utils/testUtils'
import { server } from '../utils/server'
import Login from '@/pages/Login'

// Mock the store for testing
const mockDispatch = jest.fn()
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useDispatch: () => mockDispatch,
  useSelector: jest.fn()
}))

// Mock react-router-dom
const mockNavigate = jest.fn()
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  Navigate: ({ to }: { to: string }) => {
    mockNavigate(to)
    return <div data-testid="navigate-to">{to}</div>
  }
}))

describe('Login Component', () => {
  const defaultProps = {
    language: 'en' as const
  }

  beforeEach(() => {
    jest.clearAllMocks()
    // Mock useSelector to return initial auth state
    const { useSelector } = require('react-redux')
    useSelector.mockImplementation((selector: any) => 
      selector({
        auth: {
          isAuthenticated: false,
          loading: false,
          error: null,
          user: null
        }
      })
    )
  })

  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  describe('Rendering', () => {
    it('renders without crashing', () => {
      render(<Login {...defaultProps} />}
      expect(screen.getByText('Welcome to Nemo')).toBeInTheDocument()
    })

    it('displays English content when language is en', () => {
      render(<Login language="en" />}
      
      expect(screen.getByText('Welcome to Nemo')).toBeInTheDocument()
      expect(screen.getByText('Integrated Enterprise Management System')).toBeInTheDocument()
      expect(screen.getByText('Sign In')).toBeInTheDocument()
      expect(screen.getByLabelText('Username')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()
    })

    it('displays Arabic content when language is ar', () => {
      render(<Login language="ar" />}
      
      expect(screen.getByText('مرحباً بك في نمو')).toBeInTheDocument()
      expect(screen.getByText('نظام إدارة المؤسسات المتكامل')).toBeInTheDocument()
      expect(screen.getByText('تسجيل الدخول')).toBeInTheDocument()
    })
  })

  describe('Form Interaction', () => {
    it('allows user to type in username field', () => {
      render(<Login {...defaultProps} />}
      
      const usernameInput = screen.getByLabelText('Username')
      fireEvent.change(usernameInput, { target: { value: 'testuser' } })
      
      expectusernameInput.toHaveValue('testuser')
    })

    it('allows user to type in password field', () => {
      render(<Login {...defaultProps} />}
      
      const passwordInput = screen.getByLabelText('Password')
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      
      expectpasswordInput.toHaveValue('password123')
    })

    it('toggles password visibility when eye icon is clicked', () => {
      render(<Login {...defaultProps} />}
      
      const passwordInput = screen.getByLabelText('Password')
      const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i })
      
      // Initially password should be hidden
      expectpasswordInput.toHaveAttribute('type', 'password')
      
      // Click to show password
      fireEvent.click(toggleButton)
      expectpasswordInput.toHaveAttribute('type', 'text')
      
      // Click to hide password again
      fireEvent.click(toggleButton)
      expectpasswordInput.toHaveAttribute('type', 'password')
    })
  })

  describe('Form Submission', () => {
    it('dispatches login action when form is submitted with valid data', async () => {
      render(<Login {...defaultProps} />}
      
      const usernameInput = screen.getByLabelText('Username')
      const passwordInput = screen.getByLabelText('Password')
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      
      fireEvent.change(usernameInput, { target: { value: 'admin' } })
      fireEvent.change(passwordInput, { target: { value: 'password' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expectmockDispatch.toHaveBeenCalledWith(
          expect.objectContaining({
            type: expect.stringContaining('login')
          })
        )
      })
    })

    it('shows validation errors for empty fields', async () => {
      render(<Login {...defaultProps} />}
      
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText(/username is required/i)).toBeInTheDocument()
        expect(screen.getByText(/password is required/i)).toBeInTheDocument()
      })
    })

    it('disables submit button when loading', () => {
      const { useSelector } = require('react-redux')
      useSelector.mockImplementation((selector: any) => 
        selector({
          auth: {
            isAuthenticated: false,
            loading: true,
            error: null,
            user: null
          }
        })
      )

      render(<Login {...defaultProps} />}
      
      const submitButton = screen.getByRole('button', { name: /signing in/i })
      expectsubmitButton.toBeDisabled()
    })
  })

  describe('Authentication State', () => {
    it('redirects to dashboard when user is authenticated', () => {
      const { useSelector } = require('react-redux')
      useSelector.mockImplementation((selector: any) => 
        selector({
          auth: {
            isAuthenticated: true,
            loading: false,
            error: null,
            user: { id: 1, username: 'testuser' }
          }
        })
      )

      render(<Login {...defaultProps} />}
      
      expect(screen.getByTestId('navigate-to')).toHaveTextContent('/dashboard')
    })

    it('displays error message when login fails', () => {
      const { useSelector } = require('react-redux')
      useSelector.mockImplementation((selector: any) => 
        selector({
          auth: {
            isAuthenticated: false,
            loading: false,
            error: 'Invalid credentials',
            user: null
          }
        })
      )

      render(<Login {...defaultProps} />}
      
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('has proper form labels', () => {
      render(<Login {...defaultProps} />}
      
      expect(screen.getByLabelText('Username')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()
    })

    it('has proper heading structure', () => {
      render(<Login {...defaultProps} />}
      
      const headings = screen.getAllByRole('heading')
      expectheadings.toHaveLength(2) // Main title and subtitle
    })

    it('has proper button roles', () => {
      render(<Login {...defaultProps} />}
      
      const buttons = screen.getAllByRole('button')
      expect(buttons.length).toBeGreaterThan(0)
    })
  })

  describe('Features Section', () => {
    it('displays all feature cards', () => {
      render(<Login {...defaultProps} />}
      
      // Check for feature icons/cards
      expect(screen.getByText(/user management/i)).toBeInTheDocument()
      expect(screen.getByText(/financial management/i)).toBeInTheDocument()
      expect(screen.getByText(/project management/i)).toBeInTheDocument()
      expect(screen.getByText(/security/i)).toBeInTheDocument()
    })
  })

  describe('Responsive Design', () => {
    it('applies correct CSS classes for layout', () => {
      render(<Login {...defaultProps} />}
      
      const container = screen.getByText('Welcome to Nemo').closest('div')
      expectcontainer.toHaveClass('min-h-screen')
    })
  })
})
