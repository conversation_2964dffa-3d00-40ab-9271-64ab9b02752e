/**
 * Basic Authentication Testing Suite
 * Tests for auth components, validation, and basic functionality
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Auth utility functions
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

const hashPassword = (password: string): string => {
  // Simple mock hash function for testing
  return `hashed_${password}_${password.length}`;
};

// Mock Auth Token utilities
const generateToken = (userId: number, role: string): string => {
  const timestamp = Date.now();
  return `token_${userId}_${role}_${timestamp}`;
};

const parseToken = (token: string): { userId: number; role: string; timestamp: number } | null => {
  const parts = token.split('_');
  if (parts.length !== 4 || parts[0] !== 'token') {
    return null;
  }
  
  return {
    userId: parseInt(parts[1]),
    role: parts[2],
    timestamp: parseInt(parts[3])
  };
};

const isTokenExpired = (token: string, expirationHours: number = 24): boolean => {
  const parsed = parseToken(token);
  if (!parsed) return true;
  
  const now = Date.now();
  const expirationTime = parsed.timestamp + (expirationHours * 60 * 60 * 1000);
  
  return now > expirationTime;
};

// Simple Login Form Component
interface LoginFormProps {
  onSubmit?: (email: string, password: string) => void;
  loading?: boolean;
  error?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit, loading = false, error }) => {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [showPassword, setShowPassword] = React.useState(false);
  const [emailError, setEmailError] = React.useState('');
  const [passwordError, setPasswordError] = React.useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset errors
    setEmailError('');
    setPasswordError('');
    
    // Validate email
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }
    
    // Validate password
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      setPasswordError(passwordValidation.errors[0]);
      return;
    }
    
    onSubmit?.(email, password);
  };

  return (
    <div data-testid="login-form-container">
      <h2>Sign In to EMS</h2>
      
      {error && (
        <div className="error-message" role="alert" data-testid="form-error">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit} data-testid="login-form">
        <div className="form-field">
          <label htmlFor="email">Email Address</label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            data-testid="email-field"
            disabled={loading}
            placeholder="Enter your email"
            aria-describedby={emailError ? 'email-error' : undefined}
          />
          {emailError && (
            <div id="email-error" className="field-error" role="alert" data-testid="email-error">
              {emailError}
            </div>
          )}
        </div>

        <div className="form-field">
          <label htmlFor="password">Password</label>
          <div className="password-input-container">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              data-testid="password-field"
              disabled={loading}
              placeholder="Enter your password"
              aria-describedby={passwordError ? 'password-error' : undefined}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              data-testid="toggle-password"
              disabled={loading}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? 'Hide' : 'Show'}
            </button>
          </div>
          {passwordError && (
            <div id="password-error" className="field-error" role="alert" data-testid="password-error">
              {passwordError}
            </div>
          )}
        </div>

        <button
          type="submit"
          data-testid="submit-button"
          disabled={loading || !email || !password}
          className={loading ? 'loading' : ''}
        >
          {loading ? 'Signing In...' : 'Sign In'}
        </button>
      </form>
      
      <div className="form-info" data-testid="form-info">
        <p>Test Credentials:</p>
        <p>Admin: <EMAIL> / Admin123!</p>
        <p>HR: <EMAIL> / HrManager123!</p>
      </div>
    </div>
  );
};

// User Profile Component
interface UserProfileProps {
  user: {
    id: number;
    email: string;
    role: string;
    lastLogin?: string;
  };
  onLogout?: () => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onLogout }) => {
  return (
    <div data-testid="user-profile">
      <h3>User Profile</h3>
      <div className="profile-info" data-testid="profile-info">
        <p data-testid="user-id">ID: {user.id}</p>
        <p data-testid="user-email">Email: {user.email}</p>
        <p data-testid="user-role">Role: {user.role}</p>
        {user.lastLogin && (
          <p data-testid="last-login">Last Login: {user.lastLogin}</p>
        )}
      </div>
      
      <div className="profile-actions">
        <button onClick={onLogout} data-testid="logout-button">
          Sign Out
        </button>
      </div>
    </div>
  );
};

describe('Basic Authentication Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Email Validation', () => {
    test('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    test('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('user@')).toBe(false);
      expect(validateEmail('@domain.com')).toBe(false);
      expect(validateEmail('user.domain.com')).toBe(false);
      expect(validateEmail('')).toBe(false);
    });
  });

  describe('Password Validation', () => {
    test('should validate strong passwords', () => {
      const result = validatePassword('StrongPass123!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should reject weak passwords', () => {
      const result = validatePassword('weak');
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should provide specific error messages', () => {
      const result = validatePassword('short');
      expect(result.errors).toContain('Password must be at least 8 characters long');
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
      expect(result.errors).toContain('Password must contain at least one number');
    });
  });

  describe('Token Management', () => {
    test('should generate valid tokens', () => {
      const token = generateToken(123, 'admin');
      expecttoken.toMatch(/^token_123_admin_\d+$/);
    });

    test('should parse tokens correctly', () => {
      const token = 'token_456_hr_1640995200000';
      const parsed = parseToken(token);
      
      expectparsed.toEqual({
        userId: 456,
        role: 'hr',
        timestamp: 1640995200000
      });
    });

    test('should handle invalid tokens', () => {
      expect(parseToken('invalid-token')).toBeNull();
      expect(parseToken('token_invalid_format')).toBeNull();
      expect(parseToken('')).toBeNull();
    });

    test('should detect expired tokens', () => {
      const oldTimestamp = Date.now() - (25 * 60 * 60 * 1000); // 25 hours ago
      const expiredToken = `token_123_admin_${oldTimestamp}`;
      
      expect(isTokenExpired(expiredToken, 24)).toBe(true);
    });

    test('should detect valid tokens', () => {
      const recentTimestamp = Date.now() - (1 * 60 * 60 * 1000); // 1 hour ago
      const validToken = `token_123_admin_${recentTimestamp}`;
      
      expect(isTokenExpired(validToken, 24)).toBe(false);
    });
  });

  describe('Password Hashing', () => {
    test('should hash passwords consistently', () => {
      const password = 'TestPassword123!';
      const hash1 = hashPassword(password);
      const hash2 = hashPassword(password);
      
      expecthash1.toBe(hash2);
      expecthash1.toContain('hashed_');
      expecthash1.toContain(password.length.toString());
    });

    test('should produce different hashes for different passwords', () => {
      const hash1 = hashPassword('password1');
      const hash2 = hashPassword('password2');
      
      expecthash1.not.toBe(hash2);
    });
  });

  describe('Login Form Component', () => {
    test('should render login form', () => {
      render(<LoginForm />);
      
      expect(screen.getByTestId('login-form-container')).toBeInTheDocument();
      expect(screen.getByText('Sign In to EMS')).toBeInTheDocument();
      expect(screen.getByTestId('email-field')).toBeInTheDocument();
      expect(screen.getByTestId('password-field')).toBeInTheDocument();
      expect(screen.getByTestId('submit-button')).toBeInTheDocument();
    });

    test('should show/hide password', async () => {
      render(<LoginForm />);
      
      const passwordField = screen.getByTestId('password-field');
      const toggleButton = screen.getByTestId('toggle-password');
      
      expectpasswordField.toHaveAttribute('type', 'password');
      expecttoggleButton.toHaveTextContent('Show');
      
      await user.click(toggleButton);
      
      expectpasswordField.toHaveAttribute('type', 'text');
      expecttoggleButton.toHaveTextContent('Hide');
    });

    test('should validate email on submit', async () => {
      const mockSubmit = jest.fn();
      render(<LoginForm onSubmit={mockSubmit} />);
      
      await user.type(screen.getByTestId('email-field'), 'invalid-email');
      await user.type(screen.getByTestId('password-field'), 'ValidPass123!');
      await user.click(screen.getByTestId('submit-button'));
      
      expect(screen.getByTestId('email-error')).toBeInTheDocument();
      expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
      expectmockSubmit.not.toHaveBeenCalled();
    });

    test('should validate password on submit', async () => {
      const mockSubmit = jest.fn();
      render(<LoginForm onSubmit={mockSubmit} />);
      
      await user.type(screen.getByTestId('email-field'), '<EMAIL>');
      await user.type(screen.getByTestId('password-field'), 'weak');
      await user.click(screen.getByTestId('submit-button'));
      
      expect(screen.getByTestId('password-error')).toBeInTheDocument();
      expectmockSubmit.not.toHaveBeenCalled();
    });

    test('should call onSubmit with valid credentials', async () => {
      const mockSubmit = jest.fn();
      render(<LoginForm onSubmit={mockSubmit} />);
      
      await user.type(screen.getByTestId('email-field'), '<EMAIL>');
      await user.type(screen.getByTestId('password-field'), 'ValidPass123!');
      await user.click(screen.getByTestId('submit-button'));
      
      expectmockSubmit.toHaveBeenCalledWith('<EMAIL>', 'ValidPass123!');
    });

    test('should disable form when loading', () => {
      render(<LoginForm loading={true} />);
      
      expect(screen.getByTestId('email-field')).toBeDisabled();
      expect(screen.getByTestId('password-field')).toBeDisabled();
      expect(screen.getByTestId('toggle-password')).toBeDisabled();
      expect(screen.getByTestId('submit-button')).toBeDisabled();
      expect(screen.getByText('Signing In...')).toBeInTheDocument();
    });

    test('should display error message', () => {
      render(<LoginForm error="Invalid credentials" />);
      
      expect(screen.getByTestId('form-error')).toBeInTheDocument();
      expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
    });
  });

  describe('User Profile Component', () => {
    const mockUser = {
      id: 123,
      email: '<EMAIL>',
      role: 'admin',
      lastLogin: '2024-01-15T10:30:00Z'
    };

    test('should render user profile', () => {
      render(<UserProfile user={mockUser} />);
      
      expect(screen.getByTestId('user-profile')).toBeInTheDocument();
      expect(screen.getByText('User Profile')).toBeInTheDocument();
      expect(screen.getByTestId('user-id')).toHaveTextContent('ID: 123');
      expect(screen.getByTestId('user-email')).toHaveTextContent('Email: <EMAIL>');
      expect(screen.getByTestId('user-role')).toHaveTextContent('Role: admin');
      expect(screen.getByTestId('last-login')).toHaveTextContent('Last Login: 2024-01-15T10:30:00Z');
    });

    test('should handle logout', async () => {
      const mockLogout = jest.fn();
      render(<UserProfile user={mockUser} onLogout={mockLogout} />);
      
      await user.click(screen.getByTestId('logout-button'));
      
      expectmockLogout.toHaveBeenCalled();
    });

    test('should handle user without last login', () => {
      const userWithoutLogin = { ...mockUser };
      delete userWithoutLogin.lastLogin;
      
      render(<UserProfile user={userWithoutLogin} />);
      
      expect(screen.queryByTestId('last-login')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('should have proper ARIA labels', () => {
      render(<LoginForm />);
      
      const toggleButton = screen.getByTestId('toggle-password');
      expecttoggleButton.toHaveAttribute('aria-label', 'Show password');
    });

    test('should associate error messages with fields', async () => {
      const mockSubmit = jest.fn();
      render(<LoginForm onSubmit={mockSubmit} />);
      
      await user.type(screen.getByTestId('email-field'), 'invalid');
      await user.type(screen.getByTestId('password-field'), 'ValidPass123!');
      await user.click(screen.getByTestId('submit-button'));
      
      const emailField = screen.getByTestId('email-field');
      const emailError = screen.getByTestId('email-error');
      
      expectemailField.toHaveAttribute('aria-describedby', 'email-error');
      expectemailError.toHaveAttribute('id', 'email-error');
    });
  });
});
