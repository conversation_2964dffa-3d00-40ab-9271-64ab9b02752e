/**
 * Authentication Testing Suite
 * Tests for login, logout, token management, and role-based authentication
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Mock authentication types
interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'hr' | 'finance' | 'sales' | 'user';
  permissions: string[];
  lastLogin?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
  refreshToken: string | null;
  loginAttempts: number;
  isLocked: boolean;
}

// Mock Auth Context
const MockAuthProvider: React.FC<{ children: React.ReactNode; initialState?: Partial<AuthState> }> = ({ 
  children, 
  initialState = {} 
}) => {
  const [authState, setAuthState] = React.useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    token: null,
    refreshToken: null,
    loginAttempts: 0,
    isLocked: false,
    ...initialState
  });

  const login = async (username: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock authentication logic
    if (username === '<EMAIL>' && password === 'admin123') {
      const user: User = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['read', 'write', 'delete', 'manage_users'],
        lastLogin: new Date().toISOString()
      };

      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
        loginAttempts: 0,
        isLocked: false
      });

      // Store in localStorage
      localStorage.setItem('auth_token', 'mock-jwt-token');
      localStorage.setItem('refresh_token', 'mock-refresh-token');
      localStorage.setItem('user', JSON.stringify(user));

      return { success: true };
    } else if (username === '<EMAIL>' && password === 'hr123') {
      const user: User = {
        id: 2,
        username: 'hr_manager',
        email: '<EMAIL>',
        role: 'hr',
        permissions: ['read', 'write', 'manage_employees'],
        lastLogin: new Date().toISOString()
      };

      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        token: 'mock-jwt-token-hr',
        refreshToken: 'mock-refresh-token-hr',
        loginAttempts: 0,
        isLocked: false
      });

      localStorage.setItem('auth_token', 'mock-jwt-token-hr');
      localStorage.setItem('refresh_token', 'mock-refresh-token-hr');
      localStorage.setItem('user', JSON.stringify(user));

      return { success: true };
    } else {
      // Handle failed login
      const newAttempts = authState.loginAttempts + 1;
      const isLocked = newAttempts >= 3;

      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: isLocked ? 'Account locked due to too many failed attempts' : 'Invalid credentials',
        loginAttempts: newAttempts,
        isLocked
      }));

      return { success: false, error: isLocked ? 'Account locked' : 'Invalid credentials' };
    }
  };

  const logout = () => {
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      token: null,
      refreshToken: null,
      loginAttempts: 0,
      isLocked: false
    });

    // Clear localStorage
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
  };

  const refreshAuthToken = async (): Promise<boolean> => {
    if (!authState.refreshToken) return false;

    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const newToken = 'new-mock-jwt-token';
      setAuthState(prev => ({
        ...prev,
        token: newToken,
        isLoading: false
      }));

      localStorage.setItem('auth_token', newToken);
      return true;
    } catch (error) {
      logout();
      return false;
    }
  };

  const checkPermission = (permission: string): boolean => {
    return authState.user?.permissions.includes(permission) || false;
  };

  const hasRole = (role: string): boolean => {
    return authState.user?.role === role;
  };

  const contextValue = {
    ...authState,
    login,
    logout,
    refreshAuthToken,
    checkPermission,
    hasRole
  };

  return (
    <div data-testid="auth-provider">
      {React.Children.map(children, child =>
        React.isValidElement(child)
          ? React.cloneElement(child as React.ReactElement<any>, { authContext: contextValue })
          : child
      )}
    </div>
  );
};

// Mock Login Component
interface LoginProps {
  authContext?: any;
  onLoginSuccess?: (user: User => void;
}

const MockLoginComponent: React.FC<LoginProps> = ({ authContext, onLoginSuccess }) => {
  const [formData, setFormData] = React.useState({ username: '', password: '', rememberMe: false });
  const [showPassword, setShowPassword] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!authContext) return;

    const result = await authContext.login(formData.username, formData.password);
    
    if (result.success && authContext.user && onLoginSuccess) {
      onLoginSuccess(authContext.user);
    }
  };

  if (authContext?.isAuthenticated) {
    return (
      <div data-testid="authenticated-view">
        <h1>Welcome, {authContext.user?.username}!</h1>
        <p>Role: {authContext.user?.role}</p>
        <p>Email: {authContext.user?.email}</p>
        <button onClick={authContext.logout} data-testid="logout-button">
          Logout
        </button>
      </div>
    );
  }

  return (
    <div data-testid="login-form">
      <h1>Login</h1>
      
      {authContext?.error && (
        <div className="error-message" role="alert" data-testid="error-message">
          {authContext.error}
        </div>
      )}

      {authContext?.isLocked && (
        <div className="locked-message" role="alert" data-testid="locked-message">
          Account is locked. Please contact administrator.
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="username">Email/Username:</label>
          <input
            id="username"
            type="email"
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            data-testid="username-input"
            disabled={authContext?.isLoading || authContext?.isLocked}
            required
          />
        </div>

        <div>
          <label htmlFor="password">Password:</label>
          <input
            id="password"
            type={showPassword ? 'text' : 'password'}
            value={formData.password}
            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
            data-testid="password-input"
            disabled={authContext?.isLoading || authContext?.isLocked}
            required
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            data-testid="toggle-password"
            disabled={authContext?.isLoading || authContext?.isLocked}
          >
            {showPassword ? 'Hide' : 'Show'}
          </button>
        </div>

        <div>
          <label>
            <input
              type="checkbox"
              checked={formData.rememberMe}
              onChange={(e) => setFormData({ ...formData, rememberMe: e.target.checked })}
              data-testid="remember-me"
              disabled={authContext?.isLoading || authContext?.isLocked}
            />
            Remember Me
          </label>
        </div>

        <button
          type="submit"
          data-testid="login-button"
          disabled={authContext?.isLoading || authContext?.isLocked}
        >
          {authContext?.isLoading ? 'Logging in...' : 'Login'}
        </button>
      </form>

      <div data-testid="login-attempts">
        Login attempts: {authContext?.loginAttempts || 0}/3
      </div>
    </div>
  );
};

describe('Authentication Tests', () => {
  const user = userEvent.setup();
  let mockOnLoginSuccess: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    mockOnLoginSuccess = jest.fn();
  });

  test('should render login form when not authenticated', () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.getByText('Login')).toBeInTheDocument();
    expect(screen.getByTestId('username-input')).toBeInTheDocument();
    expect(screen.getByTestId('password-input')).toBeInTheDocument();
    expect(screen.getByTestId('login-button')).toBeInTheDocument();
  });

  test('should handle successful admin login', async () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent onLoginSuccess={mockOnLoginSuccess} />
      </MockAuthProvider>
    );

    // Fill in credentials
    await user.type(screen.getByTestId('username-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'admin123');

    // Submit form
    await user.click(screen.getByTestId('login-button'));

    // Wait for authentication
    await waitFor(() => {
      expect(screen.getByTestId('authenticated-view')).toBeInTheDocument();
    });

    expect(screen.getByText('Welcome, admin!')).toBeInTheDocument();
    expect(screen.getByText('Role: admin')).toBeInTheDocument();
    expect(screen.getByText('Email: <EMAIL>')).toBeInTheDocument();
    expectmockOnLoginSuccess.toHaveBeenCalledWith(
      expect.objectContaining({
        username: 'admin',
        role: 'admin',
        email: '<EMAIL>'
      })
    );
  });

  test('should handle successful HR login', async () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent onLoginSuccess={mockOnLoginSuccess} />
      </MockAuthProvider>
    );

    await user.type(screen.getByTestId('username-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'hr123');
    await user.click(screen.getByTestId('login-button'));

    await waitFor(() => {
      expect(screen.getByTestId('authenticated-view')).toBeInTheDocument();
    });

    expect(screen.getByText('Welcome, hr_manager!')).toBeInTheDocument();
    expect(screen.getByText('Role: hr')).toBeInTheDocument();
  });

  test('should handle failed login attempts', async () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    await user.type(screen.getByTestId('username-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'wrongpassword');
    await user.click(screen.getByTestId('login-button'));

    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
    });

    expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
    expect(screen.getByText('Login attempts: 1/3')).toBeInTheDocument();
  });

  test('should lock account after 3 failed attempts', async () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    // Attempt 1
    await user.type(screen.getByTestId('username-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'wrong');
    await user.click(screen.getByTestId('login-button'));

    await waitFor(() => {
      expect(screen.getByText('Login attempts: 1/3')).toBeInTheDocument();
    });

    // Attempt 2
    await user.clear(screen.getByTestId('username-input'));
    await user.clear(screen.getByTestId('password-input'));
    await user.type(screen.getByTestId('username-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'wrong');
    await user.click(screen.getByTestId('login-button'));

    await waitFor(() => {
      expect(screen.getByText('Login attempts: 2/3')).toBeInTheDocument();
    });

    // Attempt 3 - should lock account
    await user.clear(screen.getByTestId('username-input'));
    await user.clear(screen.getByTestId('password-input'));
    await user.type(screen.getByTestId('username-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'wrong');
    await user.click(screen.getByTestId('login-button'));

    await waitFor(() => {
      expect(screen.getByTestId('locked-message')).toBeInTheDocument();
    });

    expect(screen.getByText('Account is locked. Please contact administrator.')).toBeInTheDocument();
    expect(screen.getByTestId('login-button')).toBeDisabled();
  });

  test('should handle logout functionality', async () => {
    render(
      <MockAuthProvider initialState={{ 
        isAuthenticated: true, 
        user: { 
          id: 1, 
          username: 'testuser', 
          email: '<EMAIL>', 
          role: 'admin' as const, 
          permissions: ['read', 'write'] 
        } 
      }}>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    expect(screen.getByTestId('authenticated-view')).toBeInTheDocument();

    await user.click(screen.getByTestId('logout-button'));

    expect(screen.getByTestId('login-form')).toBeInTheDocument();
    expect(screen.queryByTestId('authenticated-view')).not.toBeInTheDocument();
  });

  test('should toggle password visibility', async () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    const passwordInput = screen.getByTestId('password-input');
    const toggleButton = screen.getByTestId('toggle-password');

    expectpasswordInput.toHaveAttribute('type', 'password');
    expecttoggleButton.toHaveTextContent('Show');

    await user.click(toggleButton);

    expectpasswordInput.toHaveAttribute('type', 'text');
    expecttoggleButton.toHaveTextContent('Hide');

    await user.click(toggleButton);

    expectpasswordInput.toHaveAttribute('type', 'password');
    expecttoggleButton.toHaveTextContent('Show');
  });

  test('should handle remember me functionality', async () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    const rememberMeCheckbox = screen.getByTestId('remember-me');

    expectrememberMeCheckbox.not.toBeChecked();

    await user.click(rememberMeCheckbox);

    expectrememberMeCheckbox.toBeChecked();
  });

  test('should show loading state during authentication', async () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    await user.type(screen.getByTestId('username-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'admin123');

    const loginButton = screen.getByTestId('login-button');
    await user.click(loginButton);

    // Should show loading state
    expectloginButton.toHaveTextContent('Logging in...');
    expectloginButton.toBeDisabled();
  });

  test('should persist authentication state in localStorage', async () => {
    render(
      <MockAuthProvider>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    await user.type(screen.getByTestId('username-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'admin123');
    await user.click(screen.getByTestId('login-button'));

    await waitFor(() => {
      expect(localStorage.getItem('auth_token')).toBe('mock-jwt-token');
      expect(localStorage.getItem('refresh_token')).toBe('mock-refresh-token');
      expect(localStorage.getItem('user')).toContain('<EMAIL>');
    });
  });

  test('should clear localStorage on logout', async () => {
    // Set initial localStorage data
    localStorage.setItem('auth_token', 'test-token');
    localStorage.setItem('refresh_token', 'test-refresh');
    localStorage.setItem('user', JSON.stringify({ username: 'test' }));

    render(
      <MockAuthProvider initialState={{ 
        isAuthenticated: true, 
        user: { 
          id: 1, 
          username: 'testuser', 
          email: '<EMAIL>', 
          role: 'admin' as const, 
          permissions: ['read'] 
        } 
      }}>
        <MockLoginComponent />
      </MockAuthProvider>
    );

    await user.click(screen.getByTestId('logout-button'));

    expect(localStorage.getItem('auth_token')).toBeNull();
    expect(localStorage.getItem('refresh_token')).toBeNull();
    expect(localStorage.getItem('user')).toBeNull();
  });
});
