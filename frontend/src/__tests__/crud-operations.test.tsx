/**
 * CRUD Operations Testing Suite
 * Tests for Create, Read, Update, Delete functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Mock data types
interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive';
  createdAt: string;
}

// Mock CRUD Component
interface CrudComponentProps {
  entityType: string;
  language: 'en' | 'ar';
}

const MockCrudComponent: React.FC<CrudComponentProps> = ({ entityType, language }) => {
  const [items, setItems] = React.useState<User[]>([
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'admin', status: 'active', createdAt: '2024-01-01' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'user', status: 'active', createdAt: '2024-01-02' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'user', status: 'inactive', createdAt: '2024-01-03' }
  ]);
  
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [editingItem, setEditingItem] = React.useState<User | null>(null);
  const [formData, setFormData] = React.useState<{ name: string; email: string; role: string; status: 'active' | 'inactive' }>({ name: '', email: '', role: '', status: 'active' });
  const [searchTerm, setSearchTerm] = React.useState('');
  const [sortField, setSortField] = React.useState<keyof User>('name');
  const [sortDirection, setSortDirection] = React.useState<'asc' | 'desc'>('asc');
  const [loading, setLoading] = React.useState(false);

  const filteredItems = items.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedItems = [...filteredItems].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    const direction = sortDirection === 'asc' ? 1 : -1;
    return aValue < bValue ? -direction : aValue > bValue ? direction : 0;
  });

  const handleCreate = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
    
    const newItem: User = {
      id: Math.max(...items.map(i => i.id)) + 1,
      ...formData,
      createdAt: new Date().toISOString().split('T')[0]
    };
    
    setItems([...items, newItem]);
    setFormData({ name: '', email: '', role: '', status: 'active' });
    setIsModalOpen(false);
    setLoading(false);
  };

  const handleUpdate = async () => {
    if (!editingItem) return;
    
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call
    
    setItems(items.map(item => 
      item.id === editingItem.id 
        ? { ...item, ...formData }
        : item
    ));
    
    setEditingItem(null);
    setFormData({ name: '', email: '', role: '', status: 'active' });
    setIsModalOpen(false);
    setLoading(false);
  };

  const handleDelete = async (id: number) => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API call
    
    setItems(items.filter(item => item.id !== id));
    setLoading(false);
  };

  const openCreateModal = () => {
    setEditingItem(null);
    setFormData({ name: '', email: '', role: '', status: 'active' });
    setIsModalOpen(true);
  };

  const openEditModal = (item: User) => {
    setEditingItem(item);
    setFormData({ name: item.name, email: item.email, role: item.role, status: item.status });
    setIsModalOpen(true);
  };

  return (
    <div className="crud-component" data-testid="crud-component">
      <header className="crud-header">
        <h1>{language === 'ar' ? `إدارة ${entityType}` : `${entityType} Management`}</h1>
        <button
          onClick={openCreateModal}
          data-testid="create-button"
          className="create-button"
        >
          {language === 'ar' ? 'إضافة جديد' : 'Add New'}
        </button>
      </header>

      <div className="crud-controls">
        <input
          type="text"
          placeholder={language === 'ar' ? 'البحث...' : 'Search...'}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          data-testid="search-input"
          className="search-input"
        />
        
        <select
          value={`${sortField}-${sortDirection}`}
          onChange={(e) => {
            const [field, direction] = e.target.value.split('-');
            setSortField(field as keyof User);
            setSortDirection(direction as 'asc' | 'desc');
          }}
          data-testid="sort-select"
        >
          <option value="name-asc">{language === 'ar' ? 'الاسم (أ-ي)' : 'Name (A-Z)'}</option>
          <option value="name-desc">{language === 'ar' ? 'الاسم (ي-أ)' : 'Name (Z-A)'}</option>
          <option value="email-asc">{language === 'ar' ? 'البريد (أ-ي)' : 'Email (A-Z)'}</option>
          <option value="email-desc">{language === 'ar' ? 'البريد (ي-أ)' : 'Email (Z-A)'}</option>
        </select>
      </div>

      <div className="crud-table" data-testid="crud-table">
        <table>
          <thead>
            <tr>
              <th>{language === 'ar' ? 'الاسم' : 'Name'}</th>
              <th>{language === 'ar' ? 'البريد الإلكتروني' : 'Email'}</th>
              <th>{language === 'ar' ? 'الدور' : 'Role'}</th>
              <th>{language === 'ar' ? 'الحالة' : 'Status'}</th>
              <th>{language === 'ar' ? 'الإجراءات' : 'Actions'}</th>
            </tr>
          </thead>
          <tbody>
            {sortedItems.map(item => (
              <tr key={item.id} data-testid={`item-${item.id}`}>
                <td>{item.name}</td>
                <td>{item.email}</td>
                <td>{item.role}</td>
                <td>
                  <span className={`status ${item.status}`}>
                    {language === 'ar' 
                      ? (item.status === 'active' ? 'نشط' : 'غير نشط')
                      : item.status
                    }
                  </span>
                </td>
                <td>
                  <button
                    onClick={() => openEditModal(item)}
                    data-testid={`edit-${item.id}`}
                    className="edit-button"
                  >
                    {language === 'ar' ? 'تعديل' : 'Edit'}
                  </button>
                  <button
                    onClick={() => handleDelete(item.id)}
                    data-testid={`delete-${item.id}`}
                    className="delete-button"
                    disabled={loading}
                  >
                    {language === 'ar' ? 'حذف' : 'Delete'}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {isModalOpen && (
        <div className="modal" data-testid="crud-modal">
          <div className="modal-content">
            <h2>
              {editingItem 
                ? (language === 'ar' ? 'تعديل العنصر' : 'Edit Item')
                : (language === 'ar' ? 'إضافة عنصر جديد' : 'Add New Item')
              }
            </h2>
            
            <form onSubmit={(e) => {
              e.preventDefault();
              editingItem ? handleUpdate() : handleCreate();
            }}>
              <input
                type="text"
                placeholder={language === 'ar' ? 'الاسم' : 'Name'}
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                data-testid="form-name"
                required
              />
              
              <input
                type="email"
                placeholder={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                data-testid="form-email"
                required
              />
              
              <input
                type="text"
                placeholder={language === 'ar' ? 'الدور' : 'Role'}
                value={formData.role}
                onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                data-testid="form-role"
                required
              />
              
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value as 'active' | 'inactive' })}
                data-testid="form-status"
              >
                <option value="active">{language === 'ar' ? 'نشط' : 'Active'}</option>
                <option value="inactive">{language === 'ar' ? 'غير نشط' : 'Inactive'}</option>
              </select>
              
              <div className="modal-actions">
                <button
                  type="submit"
                  data-testid="submit-button"
                  disabled={loading}
                >
                  {loading 
                    ? (language === 'ar' ? 'جاري الحفظ...' : 'Saving...')
                    : (editingItem 
                        ? (language === 'ar' ? 'تحديث' : 'Update')
                        : (language === 'ar' ? 'إضافة' : 'Add')
                      )
                  }
                </button>
                
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  data-testid="cancel-button"
                  disabled={loading}
                >
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

describe('CRUD Operations Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render CRUD component with initial data', () => {
    render(<MockCrudComponent entityType="Users" language="en" />);
    
    expect(screen.getByTestId('crud-component')).toBeInTheDocument();
    expect(screen.getByText('Users Management')).toBeInTheDocument();
    expect(screen.getByTestId('crud-table')).toBeInTheDocument();
    expect(screen.getByTestId('item-1')).toBeInTheDocument();
    expect(screen.getByTestId('item-2')).toBeInTheDocument();
    expect(screen.getByTestId('item-3')).toBeInTheDocument();
  });

  test('should handle search functionality', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);

    const searchInput = screen.getByTestId('search-input');
    await user.type(searchInput, 'John');

    // Wait for search to filter results
    await waitFor(() => {
      expect(screen.getByTestId('item-1')).toBeInTheDocument(); // John Doe
      expect(screen.getByTestId('item-3')).toBeInTheDocument(); // Bob Johnson
    });

    // Jane Smith should not be visible after search
    expect(screen.queryByTestId('item-2')).not.toBeInTheDocument();
  });

  test('should handle sorting functionality', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);
    
    const sortSelect = screen.getByTestId('sort-select');
    await user.selectOptions(sortSelect, 'email-asc');
    
    // Check if items are sorted by email
    const table = screen.getByTestId('crud-table');
    expecttable.toBeInTheDocument();
  });

  test('should open create modal', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);
    
    const createButton = screen.getByTestId('create-button');
    await user.click(createButton);
    
    expect(screen.getByTestId('crud-modal')).toBeInTheDocument();
    expect(screen.getByText('Add New Item')).toBeInTheDocument();
  });

  test('should create new item', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);

    // Open create modal
    await user.click(screen.getByTestId('create-button'));

    // Fill form
    await user.type(screen.getByTestId('form-name'), 'New User');
    await user.type(screen.getByTestId('form-email'), '<EMAIL>');
    await user.type(screen.getByTestId('form-role'), 'user');

    // Submit form
    await user.click(screen.getByTestId('submit-button'));

    // Wait for creation to complete with longer timeout
    await waitFor(() => {
      expect(screen.queryByTestId('crud-modal')).not.toBeInTheDocument();
    }, { timeout: 2000 });

    // Check if new item appears in table
    await waitFor(() => {
      expect(screen.getByText('New User')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  test('should open edit modal with existing data', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);
    
    const editButton = screen.getByTestId('edit-1');
    await user.click(editButton);
    
    expect(screen.getByTestId('crud-modal')).toBeInTheDocument();
    expect(screen.getByText('Edit Item')).toBeInTheDocument();
    expect(screen.getByTestId('form-name')).toHaveValue('John Doe');
    expect(screen.getByTestId('form-email')).toHaveValue('<EMAIL>');
  });

  test('should update existing item', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);

    // Open edit modal
    await user.click(screen.getByTestId('edit-1'));

    // Update name - use triple click to select all text first
    const nameInput = screen.getByTestId('form-name');
    await user.tripleClick(nameInput);
    await user.type(nameInput, 'John Updated');

    // Submit form
    await user.click(screen.getByTestId('submit-button'));

    // Wait for update to complete
    await waitFor(() => {
      expect(screen.queryByTestId('crud-modal')).not.toBeInTheDocument();
    }, { timeout: 2000 });

    // Check if item is updated
    await waitFor(() => {
      expect(screen.getByText('John Updated')).toBeInTheDocument();
    });
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });

  test('should delete item', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);
    
    const deleteButton = screen.getByTestId('delete-1');
    await user.click(deleteButton);
    
    // Wait for deletion to complete
    await waitFor(() => {
      expect(screen.queryByTestId('item-1')).not.toBeInTheDocument();
    });
    
    // Check if item is removed
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
  });

  test('should render in Arabic', () => {
    render(<MockCrudComponent entityType="المستخدمين" language="ar" />);
    
    expect(screen.getByText('إدارة المستخدمين')).toBeInTheDocument();
    expect(screen.getByText('إضافة جديد')).toBeInTheDocument();
    expect(screen.getByText('الاسم')).toBeInTheDocument();
    expect(screen.getByText('البريد الإلكتروني')).toBeInTheDocument();
    expect(screen.getByText('الدور')).toBeInTheDocument();
    expect(screen.getByText('الحالة')).toBeInTheDocument();
    expect(screen.getByText('الإجراءات')).toBeInTheDocument();
  });

  test('should handle modal cancellation', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);
    
    // Open create modal
    await user.click(screen.getByTestId('create-button'));
    expect(screen.getByTestId('crud-modal')).toBeInTheDocument();
    
    // Cancel modal
    await user.click(screen.getByTestId('cancel-button'));
    expect(screen.queryByTestId('crud-modal')).not.toBeInTheDocument();
  });

  test('should disable buttons during loading', async () => {
    render(<MockCrudComponent entityType="Users" language="en" />);
    
    const deleteButton = screen.getByTestId('delete-1');
    await user.click(deleteButton);
    
    // Check if delete buttons are disabled during loading
    const allDeleteButtons = screen.getAllByText('Delete');
    allDeleteButtons.forEach(button => {
      expectbutton.toBeDisabled();
    });
  });
});
