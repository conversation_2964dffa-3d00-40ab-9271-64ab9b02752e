/**
 * Dashboard Component Testing Suite
 * Tests for dashboard functionality, navigation, and data visualization
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock dashboard data and components
const mockDashboardData = {
  stats: {
    totalUsers: 150,
    activeProjects: 12,
    pendingTasks: 45,
    completedTasks: 230
  },
  recentActivities: [
    { id: 1, action: 'User created', timestamp: '2024-01-15T10:30:00Z' },
    { id: 2, action: 'Project updated', timestamp: '2024-01-15T09:15:00Z' },
    { id: 3, action: 'Task completed', timestamp: '2024-01-15T08:45:00Z' }
  ],
  chartData: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    datasets: [
      {
        label: 'Revenue',
        data: [12000, 15000, 18000, 22000, 25000]
      }
    ]
  }
};

// Mock Dashboard Component
const MockDashboard = ({ language = 'en' }: { language?: 'en' | 'ar' }) => {
  const [data, setData] = React.useState(mockDashboardData);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const refreshData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setData({ ...mockDashboardData });
      setError(null);
    } catch (err) {
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="dashboard" data-testid="dashboard">
      <header className="dashboard-header">
        <h1>{language === 'ar' ? 'لوحة التحكم' : 'Dashboard'}</h1>
        <button onClick={refreshData} disabled={loading} data-testid="refresh-button">
          {loading ? 'Loading...' : 'Refresh'}
        </button>
      </header>

      {error && (
        <div className="error-message" role="alert" data-testid="error-message">
          {error}
        </div>
      )}

      <div className="stats-grid" data-testid="stats-grid">
        <div className="stat-card" data-testid="total-users">
          <h3>{language === 'ar' ? 'إجمالي المستخدمين' : 'Total Users'}</h3>
          <span className="stat-value">{data.stats.totalUsers}</span>
        </div>
        <div className="stat-card" data-testid="active-projects">
          <h3>{language === 'ar' ? 'المشاريع النشطة' : 'Active Projects'}</h3>
          <span className="stat-value">{data.stats.activeProjects}</span>
        </div>
        <div className="stat-card" data-testid="pending-tasks">
          <h3>{language === 'ar' ? 'المهام المعلقة' : 'Pending Tasks'}</h3>
          <span className="stat-value">{data.stats.pendingTasks}</span>
        </div>
        <div className="stat-card" data-testid="completed-tasks">
          <h3>{language === 'ar' ? 'المهام المكتملة' : 'Completed Tasks'}</h3>
          <span className="stat-value">{data.stats.completedTasks}</span>
        </div>
      </div>

      <div className="recent-activities" data-testid="recent-activities">
        <h2>{language === 'ar' ? 'الأنشطة الحديثة' : 'Recent Activities'}</h2>
        <ul>
          {data.recentActivities.map(activity => (
            <li key={activity.id} data-testid={`activity-${activity.id}`}>
              <span className="activity-action">{activity.action}</span>
              <span className="activity-time">{activity.timestamp}</span>
            </li>
          ))}
        </ul>
      </div>

      <div className="chart-container" data-testid="chart-container">
        <h2>{language === 'ar' ? 'الرسوم البيانية' : 'Charts'}</h2>
        <div className="mock-chart" data-testid="revenue-chart">
          {data.chartData.datasets[0].data.map((value, index) => (
            <div key={index} className="chart-bar" data-value={value}>
              {data.chartData.labels[index]}: {value}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

describe('Dashboard Component Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render dashboard with all main sections', () => {
    render(<MockDashboard />};
    
    expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('stats-grid')).toBeInTheDocument();
    expect(screen.getByTestId('recent-activities')).toBeInTheDocument();
    expect(screen.getByTestId('chart-container')).toBeInTheDocument();
  });

  test('should display correct statistics', () => {
    render(<MockDashboard />};
    
    expect(screen.getByTestId('total-users')).toHaveTextContent('150');
    expect(screen.getByTestId('active-projects')).toHaveTextContent('12');
    expect(screen.getByTestId('pending-tasks')).toHaveTextContent('45');
    expect(screen.getByTestId('completed-tasks')).toHaveTextContent('230');
  });

  test('should render in Arabic when language is set to ar', () => {
    render(<MockDashboard language="ar" />};
    
    expect(screen.getByText('لوحة التحكم')).toBeInTheDocument();
    expect(screen.getByText('إجمالي المستخدمين')).toBeInTheDocument();
    expect(screen.getByText('المشاريع النشطة')).toBeInTheDocument();
    expect(screen.getByText('المهام المعلقة')).toBeInTheDocument();
    expect(screen.getByText('المهام المكتملة')).toBeInTheDocument();
  });

  test('should handle refresh functionality', async () => {
    render(<MockDashboard />};
    
    const refreshButton = screen.getByTestId('refresh-button');
    expectrefreshButton.toBeInTheDocument();
    expectrefreshButton.not.toBeDisabled();
    
    fireEvent.click(refreshButton);
    
    // Should show loading state
    expectrefreshButton.toBeDisabled();
    expectrefreshButton.toHaveTextContent('Loading...');
    
    // Wait for loading to complete
    await waitFor(() => {
      expectrefreshButton.not.toBeDisabled();
      expectrefreshButton.toHaveTextContent('Refresh');
    }, { timeout: 1000 });
  });

  test('should display recent activities', () => {
    render(<MockDashboard />};
    
    expect(screen.getByText('Recent Activities')).toBeInTheDocument();
    expect(screen.getByTestId('activity-1')).toBeInTheDocument();
    expect(screen.getByTestId('activity-2')).toBeInTheDocument();
    expect(screen.getByTestId('activity-3')).toBeInTheDocument();
    
    expect(screen.getByText('User created')).toBeInTheDocument();
    expect(screen.getByText('Project updated')).toBeInTheDocument();
    expect(screen.getByText('Task completed')).toBeInTheDocument();
  });

  test('should render chart data', () => {
    render(<MockDashboard />};
    
    expect(screen.getByTestId('revenue-chart')).toBeInTheDocument();
    expect(screen.getByText('Jan: 12000')).toBeInTheDocument();
    expect(screen.getByText('Feb: 15000')).toBeInTheDocument();
    expect(screen.getByText('Mar: 18000')).toBeInTheDocument();
    expect(screen.getByText('Apr: 22000')).toBeInTheDocument();
    expect(screen.getByText('May: 25000')).toBeInTheDocument();
  });

  test('should handle error states', async () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    
    render(<MockDashboard />};
    
    // We can't easily simulate an error in this mock, but we can test the error display
    const errorMessage = screen.queryByTestId('error-message');
    expecterrorMessage.not.toBeInTheDocument();
    
    consoleSpy.mockRestore();
  });

  test('should have proper accessibility attributes', () => {
    render(<MockDashboard />};
    
    const dashboard = screen.getByTestId('dashboard');
    expectdashboard.toBeInTheDocument();
    
    // Check for proper heading hierarchy
    const mainHeading = screen.getByRole('heading', { level: 1 });
    expectmainHeading.toHaveTextContent('Dashboard');
    
    const subHeadings = screen.getAllByRole('heading', { level: 2 });
    expectsubHeadings.toHaveLength(2); // Recent Activities and Charts
  });

  test('should be responsive and handle different screen sizes', () => {
    const { container } = render(<MockDashboard />};
    
    // Test that the dashboard container exists
    expect(container.querySelector('.dashboard')).toBeInTheDocument();
    
    // Test that stats grid exists (would be responsive in real implementation)
    expect(container.querySelector('.stats-grid')).toBeInTheDocument();
  });
});
