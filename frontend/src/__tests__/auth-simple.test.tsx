/**
 * Simple Authentication Testing Suite
 * Tests for basic auth functionality without complex context
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Simple Auth Types
interface AuthUser {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'hr' | 'finance' | 'user';
}

// Simple Login Component
interface SimpleLoginProps {
  onLogin?: (user: AuthUser) => void;
  onError?: (error: string) => void;
  initialAttempts?: number;
}

const SimpleLogin: React.FC<SimpleLoginProps> = ({ onLogin, onError, initialAttempts = 0 }) => {
  const [formData, setFormData] = React.useState({ email: '', password: '' });
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [attempts, setAttempts] = React.useState(initialAttempts);
  const [isLocked, setIsLocked] = React.useState(false);
  const [showPassword, setShowPassword] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isLocked) return;

    setIsLoading(true);
    setError(null);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    // Mock authentication logic
    if (formData.email === '<EMAIL>' && formData.password === 'admin123') {
      const user: AuthUser = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin'
      };
      
      setIsLoading(false);
      setAttempts(0);
      onLogin?.(user);
      
    } else if (formData.email === '<EMAIL>' && formData.password === 'hr123') {
      const user: AuthUser = {
        id: 2,
        username: 'hr_manager',
        email: '<EMAIL>',
        role: 'hr'
      };
      
      setIsLoading(false);
      setAttempts(0);
      onLogin?.(user);
      
    } else {
      // Failed login
      const newAttempts = attempts + 1;
      setAttempts(newAttempts);
      
      if (newAttempts >= 3) {
        setIsLocked(true);
        setError('Account locked due to too many failed attempts');
        onError?.('Account locked');
      } else {
        setError('Invalid email or password');
        onError?.('Invalid credentials');
      }
      
      setIsLoading(false);
    }
  };

  return (
    <div data-testid="simple-login">
      <h1>Login to EMS</h1>
      
      {error && (
        <div className="error-alert" role="alert" data-testid="error-alert">
          {error}
        </div>
      )}

      {isLocked && (
        <div className="locked-alert" role="alert" data-testid="locked-alert">
          Account is locked. Contact administrator.
        </div>
      )}

      <form onSubmit={handleSubmit} data-testid="login-form">
        <div className="form-group">
          <label htmlFor="email">Email:</label>
          <input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            data-testid="email-input"
            disabled={isLoading || isLocked}
            required
            placeholder="Enter your email"
          />
        </div>

        <div className="form-group">
          <label htmlFor="password">Password:</label>
          <div className="password-field">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              data-testid="password-input"
              disabled={isLoading || isLocked}
              required
              placeholder="Enter your password"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              data-testid="toggle-password-visibility"
              disabled={isLoading || isLocked}
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? '👁️‍🗨️' : '👁️'}
            </button>
          </div>
        </div>

        <button
          type="submit"
          data-testid="submit-login"
          disabled={isLoading || isLocked}
          className={`login-button ${isLoading ? 'loading' : ''}`}
        >
          {isLoading ? 'Signing in...' : 'Sign In'}
        </button>
      </form>

      <div className="login-info" data-testid="login-info">
        <p>Attempts: {attempts}/3</p>
        {attempts > 0 && attempts < 3 && (
          <p className="warning">Warning: {3 - attempts} attempts remaining</p>
        )}
      </div>
    </div>
  );
};

// Simple Dashboard Component
interface SimpleDashboardProps {
  user: AuthUser;
  onLogout?: () => void;
}

const SimpleDashboard: React.FC<SimpleDashboardProps> = ({ user, onLogout }) => {
  return (
    <div data-testid="dashboard">
      <header className="dashboard-header">
        <h1>Welcome to EMS Dashboard</h1>
        <div className="user-info" data-testid="user-info">
          <span>Hello, {user.username}!</span>
          <span>Role: {user.role}</span>
          <span>Email: {user.email}</span>
        </div>
        <button onClick={onLogout} data-testid="logout-btn">
          Logout
        </button>
      </header>
      
      <main className="dashboard-content">
        <div className="role-specific-content" data-testid="role-content">
          {user.role === 'admin' && (
            <div>
              <h2>Admin Panel</h2>
              <p>You have full system access</p>
            </div>
          )}
          {user.role === 'hr' && (
            <div>
              <h2>HR Management</h2>
              <p>Manage employees and HR processes</p>
            </div>
          )}
          {user.role === 'finance' && (
            <div>
              <h2>Finance Dashboard</h2>
              <p>Manage financial operations</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

// Main App Component for Testing
const SimpleAuthApp: React.FC = () => {
  const [user, setUser] = React.useState<AuthUser | null>(null);
  const [loginError, setLoginError] = React.useState<string | null>(null);

  const handleLogin = (loggedInUser: AuthUser) => {
    setUser(loggedInUser);
    setLoginError(null);
    
    // Simulate storing in localStorage
    localStorage.setItem('ems_user', JSON.stringify(loggedInUser));
    localStorage.setItem('ems_token', `token_${loggedInUser.id}_${Date.now()}`);
  };

  const handleLogout = () => {
    setUser(null);
    setLoginError(null);
    
    // Clear localStorage
    localStorage.removeItem('ems_user');
    localStorage.removeItem('ems_token');
  };

  const handleLoginError = (error: string) => {
    setLoginError(error);
  };

  // Check for existing session on mount
  React.useEffect(() => {
    const storedUser = localStorage.getItem('ems_user');
    const storedToken = localStorage.getItem('ems_token');
    
    if (storedUser && storedToken) {
      try {
        const parsedUser = JSON.parse(storedUser);
        setUser(parsedUser);
      } catch (error) {
        // Invalid stored data, clear it
        localStorage.removeItem('ems_user');
        localStorage.removeItem('ems_token');
      }
    }
  }, []);

  if (user) {
    return <SimpleDashboard user={user} onLogout={handleLogout} />;
  }

  return <SimpleLogin onLogin={handleLogin} onError={handleLoginError} />;
};

describe('Simple Authentication Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    localStorage.clear();
    jest.clearAllMocks();
  });

  test('should render login form initially', () => {
    render(<SimpleAuthApp />);
    
    expect(screen.getByTestId('simple-login')).toBeInTheDocument();
    expect(screen.getByText('Login to EMS')).toBeInTheDocument();
    expect(screen.getByTestId('email-input')).toBeInTheDocument();
    expect(screen.getByTestId('password-input')).toBeInTheDocument();
    expect(screen.getByTestId('submit-login')).toBeInTheDocument();
  });

  test('should handle successful admin login', async () => {
    render(<SimpleAuthApp />);
    
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'admin123');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    });

    expect(screen.getByText('Welcome to EMS Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Hello, admin!')).toBeInTheDocument();
    expect(screen.getByText('Role: admin')).toBeInTheDocument();
    expect(screen.getByText('You have full system access')).toBeInTheDocument();
  });

  test('should handle successful HR login', async () => {
    render(<SimpleAuthApp />);
    
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'hr123');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    });

    expect(screen.getByText('Hello, hr_manager!')).toBeInTheDocument();
    expect(screen.getByText('Role: hr')).toBeInTheDocument();
    expect(screen.getByText('Manage employees and HR processes')).toBeInTheDocument();
  });

  test('should handle failed login attempts', async () => {
    render(<SimpleAuthApp />);
    
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'wrongpass');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(screen.getByTestId('error-alert')).toBeInTheDocument();
    });

    expect(screen.getByText('Invalid email or password')).toBeInTheDocument();
    expect(screen.getByText('Attempts: 1/3')).toBeInTheDocument();
    expect(screen.getByText('Warning: 2 attempts remaining')).toBeInTheDocument();
  });

  test('should lock account after 3 failed attempts', async () => {
    render(<SimpleAuthApp />);
    
    // First failed attempt
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'wrong');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(screen.getByText('Attempts: 1/3')).toBeInTheDocument();
    });

    // Second failed attempt
    await user.clear(screen.getByTestId('email-input'));
    await user.clear(screen.getByTestId('password-input'));
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'wrong');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(screen.getByText('Attempts: 2/3')).toBeInTheDocument();
    });

    // Third failed attempt - should lock
    await user.clear(screen.getByTestId('email-input'));
    await user.clear(screen.getByTestId('password-input'));
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'wrong');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(screen.getByTestId('locked-alert')).toBeInTheDocument();
    });

    expect(screen.getByText('Account locked due to too many failed attempts')).toBeInTheDocument();
    expect(screen.getByTestId('submit-login')).toBeDisabled();
  });

  test('should handle logout functionality', async () => {
    render(<SimpleAuthApp />);
    
    // Login first
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'admin123');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    });

    // Now logout
    await user.click(screen.getByTestId('logout-btn'));

    expect(screen.getByTestId('simple-login')).toBeInTheDocument();
    expect(screen.queryByTestId('dashboard')).not.toBeInTheDocument();
  });

  test('should toggle password visibility', async () => {
    render(<SimpleAuthApp />);
    
    const passwordInput = screen.getByTestId('password-input');
    const toggleButton = screen.getByTestId('toggle-password-visibility');

    expectpasswordInput.toHaveAttribute('type', 'password');

    await user.click(toggleButton);
    expectpasswordInput.toHaveAttribute('type', 'text');

    await user.click(toggleButton);
    expectpasswordInput.toHaveAttribute('type', 'password');
  });

  test('should show loading state during login', async () => {
    render(<SimpleAuthApp />);
    
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'admin123');
    
    const submitButton = screen.getByTestId('submit-login');
    await user.click(submitButton);

    // Should briefly show loading state
    expectsubmitButton.toHaveTextContent('Signing in...');
    expectsubmitButton.toBeDisabled();
  });

  test('should persist user session in localStorage', async () => {
    render(<SimpleAuthApp />);
    
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'admin123');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(localStorage.getItem('ems_user')).toContain('<EMAIL>');
      expect(localStorage.getItem('ems_token')).toContain('token_1_');
    });
  });

  test('should clear localStorage on logout', async () => {
    // Set initial data
    localStorage.setItem('ems_user', JSON.stringify({ username: 'test' }));
    localStorage.setItem('ems_token', 'test-token');

    render(<SimpleAuthApp />);
    
    // Login first
    await user.type(screen.getByTestId('email-input'), '<EMAIL>');
    await user.type(screen.getByTestId('password-input'), 'admin123');
    await user.click(screen.getByTestId('submit-login'));

    await waitFor(() => {
      expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    });

    // Logout
    await user.click(screen.getByTestId('logout-btn'));

    expect(localStorage.getItem('ems_user')).toBeNull();
    expect(localStorage.getItem('ems_token')).toBeNull();
  });

  test('should restore session from localStorage on app load', () => {
    const mockUser = {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin' as const
    };

    localStorage.setItem('ems_user', JSON.stringify(mockUser));
    localStorage.setItem('ems_token', 'valid-token');

    render(<SimpleAuthApp />);

    expect(screen.getByTestId('dashboard')).toBeInTheDocument();
    expect(screen.getByText('Hello, admin!')).toBeInTheDocument();
  });
});
