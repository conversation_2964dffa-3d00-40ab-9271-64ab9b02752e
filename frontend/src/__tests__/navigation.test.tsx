/**
 * Navigation Component Testing Suite
 * Tests for navigation functionality, routing, and role-based access
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock navigation data
const mockNavigationItems = {
  admin: [
    { id: 'dashboard', label: 'Dashboard', labelAr: 'لوحة التحكم', path: '/admin/dashboard', icon: 'dashboard' },
    { id: 'users', label: 'User Management', labelAr: 'إدارة المستخدمين', path: '/admin/users', icon: 'users' },
    { id: 'reports', label: 'Reports', labelAr: 'التقارير', path: '/admin/reports', icon: 'reports' },
    { id: 'settings', label: 'Settings', labelAr: 'الإعدادات', path: '/admin/settings', icon: 'settings' }
  ],
  hr: [
    { id: 'dashboard', label: 'HR Dashboard', labelAr: 'لوحة الموارد البشرية', path: '/hr/dashboard', icon: 'dashboard' },
    { id: 'employees', label: 'Employees', labelAr: 'الموظفين', path: '/hr/employees', icon: 'employees' },
    { id: 'attendance', label: 'Attendance', labelAr: 'الحضور', path: '/hr/attendance', icon: 'attendance' }
  ],
  finance: [
    { id: 'dashboard', label: 'Finance Dashboard', labelAr: 'لوحة المالية', path: '/finance/dashboard', icon: 'dashboard' },
    { id: 'invoices', label: 'Invoices', labelAr: 'الفواتير', path: '/finance/invoices', icon: 'invoices' },
    { id: 'expenses', label: 'Expenses', labelAr: 'المصروفات', path: '/finance/expenses', icon: 'expenses' }
  ]
};

// Mock Navigation Component
interface NavigationProps {
  userRole: 'admin' | 'hr' | 'finance';
  language: 'en' | 'ar';
  currentPath: string;
  onNavigate: (path: string) => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const MockNavigation: React.FC<NavigationProps> = ({
  userRole,
  language,
  currentPath,
  onNavigate,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const navigationItems = mockNavigationItems[userRole] || [];

  return (
    <nav 
      className={`navigation ${isCollapsed ? 'collapsed' : 'expanded'} ${language === 'ar' ? 'rtl' : 'ltr'}`}
      data-testid="navigation"
      role="navigation"
      aria-label={language === 'ar' ? 'التنقل الرئيسي' : 'Main navigation'}
    >
      <div className="nav-header">
        <h2 className="nav-title">
          {language === 'ar' ? 'نظام إدارة المؤسسات' : 'EMS'}
        </h2>
        {onToggleCollapse && (
          <button
            onClick={onToggleCollapse}
            data-testid="toggle-collapse"
            aria-label={language === 'ar' ? 'طي/توسيع القائمة' : 'Toggle navigation'}
          >
            {isCollapsed ? '→' : '←'}
          </button>
        )}
      </div>

      <div className="user-role-indicator" data-testid="user-role">
        <span className="role-label">
          {language === 'ar' ? 'الدور:' : 'Role:'}
        </span>
        <span className="role-value">{userRole.toUpperCase()}</span>
      </div>

      <ul className="nav-items" role="menubar">
        {navigationItems.map(item => (
          <li key={item.id} role="none">
            <button
              className={`nav-item ${currentPath === item.path ? 'active' : ''}`}
              onClick={() => onNavigate(item.path)}
              data-testid={`nav-${item.id}`}
              role="menuitem"
              aria-current={currentPath === item.path ? 'page' : undefined}
            >
              <span className="nav-icon" data-icon={item.icon}>
                {item.icon}
              </span>
              {!isCollapsed && (
                <span className="nav-label">
                  {language === 'ar' ? item.labelAr : item.label}
                </span>
              )}
            </button>
          </li>
        ))}
      </ul>

      <div className="nav-footer" data-testid="nav-footer">
        <button
          className="logout-button"
          data-testid="logout-button"
          onClick={() => onNavigate('/logout')}
        >
          {language === 'ar' ? 'تسجيل الخروج' : 'Logout'}
        </button>
      </div>
    </nav>
  );
};

describe('Navigation Component Tests', () => {
  const mockOnNavigate = jest.fn();
  const mockOnToggleCollapse = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should render navigation with admin role items', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="en"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    expect(screen.getByTestId('navigation')).toBeInTheDocument();
    expect(screen.getByTestId('user-role')).toHaveTextContent('ADMIN');
    expect(screen.getByTestId('nav-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('nav-users')).toBeInTheDocument();
    expect(screen.getByTestId('nav-reports')).toBeInTheDocument();
    expect(screen.getByTestId('nav-settings')).toBeInTheDocument();
  });

  test('should render navigation with HR role items', () => {
    render(
      <MockNavigation
        userRole="hr"
        language="en"
        currentPath="/hr/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    expect(screen.getByTestId('user-role')).toHaveTextContent('HR');
    expect(screen.getByTestId('nav-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('nav-employees')).toBeInTheDocument();
    expect(screen.getByTestId('nav-attendance')).toBeInTheDocument();
    
    // Should not show admin-only items
    expect(screen.queryByTestId('nav-users')).not.toBeInTheDocument();
    expect(screen.queryByTestId('nav-settings')).not.toBeInTheDocument();
  });

  test('should render navigation with Finance role items', () => {
    render(
      <MockNavigation
        userRole="finance"
        language="en"
        currentPath="/finance/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    expect(screen.getByTestId('user-role')).toHaveTextContent('FINANCE');
    expect(screen.getByTestId('nav-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('nav-invoices')).toBeInTheDocument();
    expect(screen.getByTestId('nav-expenses')).toBeInTheDocument();
  });

  test('should render in Arabic when language is set to ar', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="ar"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    expect(screen.getByText('نظام إدارة المؤسسات')).toBeInTheDocument();
    expect(screen.getByText('الدور:')).toBeInTheDocument();
    expect(screen.getByText('لوحة التحكم')).toBeInTheDocument();
    expect(screen.getByText('إدارة المستخدمين')).toBeInTheDocument();
    expect(screen.getByText('التقارير')).toBeInTheDocument();
    expect(screen.getByText('الإعدادات')).toBeInTheDocument();
    expect(screen.getByText('تسجيل الخروج')).toBeInTheDocument();
  });

  test('should handle navigation clicks', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="en"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    fireEvent.click(screen.getByTestId('nav-users'));
    expectmockOnNavigate.toHaveBeenCalledWith('/admin/users');

    fireEvent.click(screen.getByTestId('nav-reports'));
    expectmockOnNavigate.toHaveBeenCalledWith('/admin/reports');
  });

  test('should highlight active navigation item', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="en"
        currentPath="/admin/users"
        onNavigate={mockOnNavigate}
      />
    );

    const activeItem = screen.getByTestId('nav-users');
    expectactiveItem.toHaveClass('active');
    expectactiveItem.toHaveAttribute('aria-current', 'page');

    const inactiveItem = screen.getByTestId('nav-dashboard');
    expectinactiveItem.not.toHaveClass('active');
    expectinactiveItem.not.toHaveAttribute('aria-current');
  });

  test('should handle collapse/expand functionality', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="en"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
        isCollapsed={false}
        onToggleCollapse={mockOnToggleCollapse}
      />
    );

    const toggleButton = screen.getByTestId('toggle-collapse');
    expecttoggleButton.toBeInTheDocument();

    fireEvent.click(toggleButton);
    expectmockOnToggleCollapse.toHaveBeenCalled();
  });

  test('should hide labels when collapsed', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="en"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
        isCollapsed={true}
        onToggleCollapse={mockOnToggleCollapse}
      />
    );

    const navigation = screen.getByTestId('navigation');
    expectnavigation.toHaveClass('collapsed');

    // Labels should not be visible when collapsed
    expect(screen.queryByText('Dashboard')).not.toBeInTheDocument();
    expect(screen.queryByText('User Management')).not.toBeInTheDocument();
  });

  test('should handle logout functionality', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="en"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    fireEvent.click(screen.getByTestId('logout-button'));
    expectmockOnNavigate.toHaveBeenCalledWith('/logout');
  });

  test('should have proper accessibility attributes', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="en"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    const navigation = screen.getByRole('navigation');
    expectnavigation.toHaveAttribute('aria-label', 'Main navigation');

    const menubar = screen.getByRole('menubar');
    expectmenubar.toBeInTheDocument();

    const menuItems = screen.getAllByRole('menuitem');
    expectmenuItems.toHaveLength(4); // 4 admin navigation items
  });

  test('should apply RTL class for Arabic language', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="ar"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    const navigation = screen.getByTestId('navigation');
    expectnavigation.toHaveClass('rtl');
  });

  test('should apply LTR class for English language', () => {
    render(
      <MockNavigation
        userRole="admin"
        language="en"
        currentPath="/admin/dashboard"
        onNavigate={mockOnNavigate}
      />
    );

    const navigation = screen.getByTestId('navigation');
    expectnavigation.toHaveClass('ltr');
  });
});
