import React from 'react';
/**
 * Browser Caching Utilities
 * Advanced browser caching strategies and cache management
 */

// Cache storage types
export type CacheStorageType = 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB'

// Cache entry interface
interface BrowserCacheEntry<T = any> {
  data: T
  timestamp: number
  ttl: number
  version: string
  compressed?: boolean
}

// Cache configuration
interface BrowserCacheConfig {
  storageType: CacheStorageType
  defaultTTL: number
  maxSize: number
  compression: boolean
  encryption: boolean
  keyPrefix: string
}

class BrowserCache<T = any> {
  private config: BrowserCacheConfig
  private memoryCache = new Map<string, BrowserCacheEntry<T>>()

  constructor(config: Partial<BrowserCacheConfig> = {}) {
    this.config = {
      storageType: 'localStorage',
      defaultTTL: 30 * 60 * 1000, // 30 minutes
      maxSize: 50,
      compression: false,
      encryption: false,
      keyPrefix: 'bc_',
      ...config
    }
  }

  /**
   * Generate storage key
   */
  private getStorageKey(key: string): string {
    return `${this.config.keyPrefix}${key}`
  }

  /**
   * Get storage interface
   */
  private getStorage(): Storage | null {
    try {
      switch (this.config.storageType) {
        case 'localStorage':
          return localStorage
        case 'sessionStorage':
          return sessionStorage
        default:
          return null
      }
    } catch {
      return null
    }
  }

  /**
   * Compress data
   */
  private compressData(data: T): string {
    if (!this.config.compression) {
      return JSON.stringify(data)
    }
    
    // Simple compression - in production, use a proper compression library
    const jsonString = JSON.stringify(data)
    return btoa(jsonString)
  }

  /**
   * Decompress data
   */
  private decompressData(data: string): T {
    if (!this.config.compression) {
      return JSON.parse(data)
    }
    
    try {
      const decompressed = atob(data)
      return JSON.parse(decompressed)
    } catch {
      // Fallback to regular JSON parse
      return JSON.parse(data)
    }
  }

  /**
   * Check if entry is valid
   */
  private isValid(entry: BrowserCacheEntry<T>): boolean {
    return Date.now() - entry.timestamp < entry.ttl
  }

  /**
   * Set cache entry
   */
  async set(key: string, data: T, ttl?: number): Promise<boolean> {
    const entry: BrowserCacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL,
      version: '1.0.0',
      compressed: this.config.compression
    }

    try {
      // Store in memory cache
      if (this.config.storageType === 'memory') {
        this.memoryCache.set(key, entry)
        return true
      }

      // Store in browser storage
      const storage = this.getStorage()
      if (storage) {
        const storageKey = this.getStorageKey(key)
        const serialized = this.compressData(entry)
        storage.setItem(storageKey, serialized)
        return true
      }

      return false
    } catch (error) {
      console.warn('Failed to set cache entry:', error)
      return false
    }
  }

  /**
   * Get cache entry
   */
  async get(key: string): Promise<T | null> {
    try {
      let entry: BrowserCacheEntry<T> | null = null

      // Get from memory cache
      if (this.config.storageType === 'memory') {
        entry = this.memoryCache.get(key) || null
      } else {
        // Get from browser storage
        const storage = this.getStorage()
        if (storage) {
          const storageKey = this.getStorageKey(key)
          const serialized = storage.getItem(storageKey)
          if (serialized) {
            entry = this.decompressData(serialized) as BrowserCacheEntry<T>
          }
        }
      }

      if (!entry || !this.isValid(entry)) {
        if (entry) {
          await this.remove(key)
        }
        return null
      }

      return entry.data
    } catch (error) {
      console.warn('Failed to get cache entry:', error)
      return null
    }
  }

  /**
   * Check if key exists and is valid
   */
  async has(key: string): Promise<boolean> {
    const data = await this.get(key)
    return data !== null
  }

  /**
   * Remove cache entry
   */
  async remove(key: string): Promise<boolean> {
    try {
      if (this.config.storageType === 'memory') {
        return this.memoryCache.delete(key)
      }

      const storage = this.getStorage()
      if (storage) {
        const storageKey = this.getStorageKey(key)
        storage.removeItem(storageKey)
        return true
      }

      return false
    } catch (error) {
      console.warn('Failed to remove cache entry:', error)
      return false
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<boolean> {
    try {
      if (this.config.storageType === 'memory') {
        this.memoryCache.clear()
        return true
      }

      const storage = this.getStorage()
      if (storage) {
        const keys = Object.keys(storage).filter(key => 
          key.startsWith(this.config.keyPrefix)
        )
        keys.forEach(key => storage.removeItem(key))
        return true
      }

      return false
    } catch (error) {
      console.warn('Failed to clear cache:', error)
      return false
    }
  }

  /**
   * Get cache size
   */
  async size(): Promise<number> {
    if (this.config.storageType === 'memory') {
      return this.memoryCache.size
    }

    const storage = this.getStorage()
    if (storage) {
      return Object.keys(storage).filter(key => 
        key.startsWith(this.config.keyPrefix)
      ).length
    }

    return 0
  }

  /**
   * Get all cache keys
   */
  async keys(): Promise<string[]> {
    if (this.config.storageType === 'memory') {
      return Array.from(this.memoryCache.keys())
    }

    const storage = this.getStorage()
    if (storage) {
      return Object.keys(storage)
        .filter(key => key.startsWith(this.config.keyPrefix))
        .map(key => key.replace(this.config.keyPrefix, ''))
    }

    return []
  }

  /**
   * Cleanup expired entries
   */
  async cleanup(): Promise<number> {
    let cleanedCount = 0

    if (this.config.storageType === 'memory') {
      const expiredKeys: string[] = []
      for (const [key, entry] of this.memoryCache.entries()) {
        if (!this.isValid(entry)) {
          expiredKeys.push(key)
        }
      }
      expiredKeys.forEach(key => {
        this.memoryCache.delete(key)
        cleanedCount++
      })
    } else {
      const storage = this.getStorage()
      if (storage) {
        const keys = Object.keys(storage).filter(key => 
          key.startsWith(this.config.keyPrefix)
        )
        
        for (const storageKey of keys) {
          try {
            const serialized = storage.getItem(storageKey)
            if (serialized) {
              const entry = this.decompressData(serialized) as BrowserCacheEntry<T>
              if (!this.isValid(entry)) {
                storage.removeItem(storageKey)
                cleanedCount++
              }
            }
          } catch {
            // Remove corrupted entries
            storage.removeItem(storageKey)
            cleanedCount++
          }
        }
      }
    }

    return cleanedCount
  }
}

// Pre-configured cache instances
export const memoryCache = new BrowserCache({
  storageType: 'memory',
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  keyPrefix: 'mem_'
})

export const sessionCache = new BrowserCache({
  storageType: 'sessionStorage',
  defaultTTL: 30 * 60 * 1000, // 30 minutes
  keyPrefix: 'sess_'
})

export const persistentCache = new BrowserCache({
  storageType: 'localStorage',
  defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
  keyPrefix: 'pers_'
})

// Cache strategies
export const CacheStrategies = {
  /**
   * Cache first, then network
   */
  cacheFirst: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    cache: BrowserCache<T> = memoryCache,
    ttl?: number
  ): Promise<T> => {
    // Try cache first
    const cached = await cache.get(key)
    if (cached !== null) {
      return cached
    }

    // Fetch from network
    const data = await fetcher()
    await cache.set(key, data, ttl)
    return data
  },

  /**
   * Network first, fallback to cache
   */
  networkFirst: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    cache: BrowserCache<T> = memoryCache,
    ttl?: number
  ): Promise<T> => {
    try {
      // Try network first
      const data = await fetcher()
      await cache.set(key, data, ttl)
      return data
    } catch (error) {
      // Fallback to cache
      const cached = await cache.get(key)
      if (cached !== null) {
        return cached
      }
      throw error
    }
  },

  /**
   * Stale while revalidate
   */
  staleWhileRevalidate: async <T>(
    key: string,
    fetcher: () => Promise<T>,
    cache: BrowserCache<T> = memoryCache,
    ttl?: number
  ): Promise<T> => {
    // Get cached data
    const cached = await cache.get(key)

    // Start background fetch
    const fetchPromise = fetcher().then(data => {
      cache.set(key, data, ttl)
      return data
    })

    // Return cached data immediately if available
    if (cached !== null) {
      return cached
    }

    // Wait for network if no cache
    return fetchPromise
  }
}

// Cache management utilities
export const CacheManager = {
  /**
   * Initialize cache system
   */
  init: async (): Promise<void> => {
    // Cleanup expired entries on startup
    await Promise.all([
      memoryCache.cleanup(),
      sessionCache.cleanup(),
      persistentCache.cleanup()
    ])

    // Set up periodic cleanup
    setInterval(async () => {
      await Promise.all([
        memoryCache.cleanup(),
        sessionCache.cleanup(),
        persistentCache.cleanup()
      ])
    }, 10 * 60 * 1000) // Every 10 minutes
  },

  /**
   * Clear all caches
   */
  clearAll: async (): Promise<void> => {
    await Promise.all([
      memoryCache.clear(),
      sessionCache.clear(),
      persistentCache.clear()
    ])
  },

  /**
   * Get cache statistics
   */
  getStats: async () => {
    const [memorySize, sessionSize, persistentSize] = await Promise.all([
      memoryCache.size(),
      sessionCache.size(),
      persistentCache.size()
    ])

    return {
      memory: memorySize,
      session: sessionSize,
      persistent: persistentSize,
      total: memorySize + sessionSize + persistentSize
    }
  }
}

export default BrowserCache
