import React from 'react';
/**
 * CRITICAL FIX: Token Refresh Manager
 * Prevents race conditions in authentication token refresh
 */

import { apiClient } from '../services/api'

interface RefreshQueueItem {
  resolve: (value: boolean) => void
  reject: (error: Error => void
  timestamp: number
}

class TokenRefreshManager {
  private static instance: TokenRefreshManager
  private refreshPromise: Promise<boolean> | null = null
  private refreshQueue: RefreshQueueItem[] = []
  private isRefreshing = false
  private lastRefreshAttempt = 0
  private refreshAttempts = 0
  private maxRefreshAttempts = 3
  private refreshCooldown = 5000 // 5 seconds

  private constructor() {
    // Singleton pattern
  }

  static getInstance(): TokenRefreshManager {
    if (!TokenRefreshManager.instance) {
      TokenRefreshManager.instance = new TokenRefreshManager()
    }
    return TokenRefreshManager.instance
  }

  /**
   * CRITICAL FIX: Queue-based token refresh to prevent race conditions
   */
  async refreshToken(): Promise<boolean> {
    // CRITICAL FIX: Don't attempt refresh if no cookies exist (user logged out)
    const hasAuthCookies = document.cookie.includes('access_token') || document.cookie.includes('refresh_token')
    if (!hasAuthCookies) {
      if (process.env.NODE_ENV === "development") {
        console.warn('No auth cookies found - skipping refresh attempt')
      }
      return false
    }

    // Check if we've exceeded max attempts
    if (this.refreshAttempts >= this.maxRefreshAttempts) {
      if (process.env.NODE_ENV === "development") {
        console.error('Max refresh attempts exceeded, forcing logout')
      }
      this.handleRefreshFailure(new Error('Max refresh attempts exceeded'))
      return false
    }

    // Check cooldown period
    const now = Date.now()
    if (now - this.lastRefreshAttempt < this.refreshCooldown) {
      if (process.env.NODE_ENV === "development") {
        console.warn('Token refresh in cooldown period')
      }
      return false
    }

    // If refresh is already in progress, queue this request
    if (this.isRefreshing && this.refreshPromise) {
      return this.queueRefreshRequest()
    }

    // Start new refresh process
    return this.performRefresh()
  }

  private async queueRefreshRequest(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      // Add to queue with timeout
      const queueItem: RefreshQueueItem = {
        resolve,
        reject,
        timestamp: Date.now()
      }

      this.refreshQueue.push(queueItem)

      // Set timeout for queued request
      setTimeout(() => {
        const index = this.refreshQueue.indexOf(queueItem)
        if (index > -1) {
          this.refreshQueue.splice(index, 1)
          reject(new Error('Token refresh timeout'))
        }
      }, 10000) // 10 second timeout
    })
  }

  private async performRefresh(): Promise<boolean> {
    if (this.refreshAttempts >= this.maxRefreshAttempts) {
      console.error('Max refresh attempts exceeded')
      this.handleRefreshFailure(new Error('Max refresh attempts exceeded'))
      return false
    }

    this.isRefreshing = true
    this.lastRefreshAttempt = Date.now()
    this.refreshAttempts++

    try {
      if (process.env.NODE_ENV === "development") {

        console.log('🔄 Starting token refresh...')

      }
      
      // Create refresh promise
      this.refreshPromise = this.executeRefresh()
      const success = await this.refreshPromise

      if (success) {
        if (process.env.NODE_ENV === "development") {

          console.log('✅ Token refresh successful')

        }
        this.handleRefreshSuccess()
        return true
      } else {
        console.error('❌ Token refresh failed')
        this.handleRefreshFailure(new Error('Token refresh failed'))
        return false
      }

    } catch (error) {
      console.error('💥 Token refresh error:', error)
      this.handleRefreshFailure(error as Error)
      return false
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  private async executeRefresh(): Promise<boolean> {
    try {
      // FIXED: Use correct API base URL for token refresh
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
      const refreshUrl = `${API_BASE_URL}/auth/refresh/`

      if (process.env.NODE_ENV === "development") {


        console.log('🔄 Token refresh URL:', refreshUrl)


      }

      const response = await fetch(refreshUrl, {
        method: 'POST',
        credentials: 'include', // Include httpOnly cookies
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (process.env.NODE_ENV === "development") {


        console.log('🔄 Token refresh response status:', response.status)


      }

      if (response.ok) {
        // Reset attempt counter on success
        this.refreshAttempts = 0
        if (process.env.NODE_ENV === "development") {

          console.log('✅ Token refresh successful')

        }
        return true
      } else if (response.status === 401) {
        // Refresh token is invalid - user needs to login again
        if (process.env.NODE_ENV === "development") {

          console.warn('🔒 Refresh token invalid - authentication required')

        }
        this.handleAuthenticationFailure()
        return false
      } else if (response.status === 404) {
        // Endpoint not found - likely configuration issue
        console.error('❌ Token refresh endpoint not found (404)')
        throw new Error(`Token refresh endpoint not found: ${refreshUrl}`)
      } else {
        const errorText = await response.text()
        console.error('❌ Token refresh failed:', response.status, errorText)
        throw new Error(`Refresh failed with status: ${response.status}`)
      }

    } catch (error) {
      console.error('💥 Token refresh error:', error)
      throw error
    }
  }

  private handleRefreshSuccess(): void {
    // Resolve all queued requests
    this.refreshQueue.forEach(item => {
      try {
        item.resolve(true)
      } catch (error) {
        console.error('Error resolving queued refresh request:', error)
      }
    })
    this.refreshQueue = []

    // Emit success event
    window.dispatchEvent(new CustomEvent('auth:refresh-success'))
  }

  private handleRefreshFailure(error: Error): void {
    // Reject all queued requests
    this.refreshQueue.forEach(item => {
      try {
        item.reject(error)
      } catch (err) {
        console.error('Error rejecting queued refresh request:', err)
      }
    })
    this.refreshQueue = []

    // Check if we should retry or give up
    if (this.refreshAttempts >= this.maxRefreshAttempts) {
      this.handleAuthenticationFailure()
    }
  }

  private handleAuthenticationFailure(): void {
    if (process.env.NODE_ENV === "development") {
      console.warn('Authentication failure - emitting logout event')
    }

    // Reset state
    this.refreshAttempts = 0
    this.isRefreshing = false
    this.refreshPromise = null
    this.refreshQueue = []

    // Emit logout event - let the app handle navigation
    window.dispatchEvent(new CustomEvent('auth:logout', {
      detail: { reason: 'Token refresh failed' }
    }))

    // FIXED: Don't force page reload - let React Router handle navigation
    // The auth slice will handle the logout and navigation properly
    if (process.env.NODE_ENV === "development") {
      console.log('Auth failure event emitted - letting React Router handle navigation')
    }
  }

  /**
   * Reset the refresh manager state
   */
  reset(): void {
    this.refreshAttempts = 0
    this.isRefreshing = false
    this.refreshPromise = null
    this.lastRefreshAttempt = 0

    // Clear queue
    this.refreshQueue.forEach(item => {
      item.reject(new Error('Refresh manager reset'))
    })
    this.refreshQueue = []

    if (process.env.NODE_ENV === "development") {
      console.log('🔄 TokenRefreshManager reset - all refresh attempts cleared')
    }
  }

  /**
   * Get current refresh status
   */
  getStatus() {
    return {
      isRefreshing: this.isRefreshing,
      queueLength: this.refreshQueue.length,
      refreshAttempts: this.refreshAttempts,
      lastRefreshAttempt: this.lastRefreshAttempt,
      cooldownRemaining: Math.max(0, this.refreshCooldown - (Date.now() - this.lastRefreshAttempt))
    }
  }

  /**
   * CRITICAL FIX: Advanced token validation and refresh
   */
  async validateAndRefreshToken(): Promise<boolean> {
    try {
      // First, check if we have a valid token
      const tokenValid = await this.validateCurrentToken()

      if (tokenValid) {
        return true
      }

      // If token is invalid, attempt refresh
      return await this.refreshToken()

    } catch (error) {
      console.error('Token validation failed:', error)
      return false
    }
  }

  private async validateCurrentToken(): Promise<boolean> {
    try {
      // FIXED: Use correct API base URL for token validation
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
      const validateUrl = `${API_BASE_URL}/auth/user/`

      const response = await fetch(validateUrl, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (process.env.NODE_ENV === "development") {


        console.log('🔍 Token validation response:', response.status)


      }
      return response.ok
    } catch (error) {
      console.error('💥 Token validation failed:', error)
      return false
    }
  }

  /**
   * CRITICAL FIX: Preemptive token refresh
   * Refresh token before it expires to prevent authentication failures
   */
  startPreemptiveRefresh(): void {
    // Refresh token every 14 minutes (assuming 15-minute token lifetime)
    const refreshInterval = 14 * 60 * 1000 // 14 minutes

    setInterval(async () => {
      if (!this.isRefreshing) {
        if (process.env.NODE_ENV === "development") {

          console.log('🔄 Preemptive token refresh...')

        }
        try {
          await this.refreshToken()
        } catch (error) {
          if (process.env.NODE_ENV === "development") {

            console.warn('Preemptive refresh failed:', error)

          }
        }
      }
    }, refreshInterval)
  }

  /**
   * CRITICAL FIX: Handle network connectivity issues
   */
  private async handleNetworkError(error: Error): Promise<void> {
    if (process.env.NODE_ENV === "development") {

      console.warn('Network error during token refresh:', error)

    }

    // Implement exponential backoff
    const backoffDelay = Math.min(1000 * Math.pow(2, this.refreshAttempts), 30000)

    await new Promise(resolve => setTimeout(resolve, backoffDelay))
  }
}

// Export singleton instance
export const tokenRefreshManager = TokenRefreshManager.getInstance()

// Export for testing
export { TokenRefreshManager }
