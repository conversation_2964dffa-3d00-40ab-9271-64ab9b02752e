import React from 'react';
/**
 * Validation Utilities and Schemas
 * Provides comprehensive validation for forms and data input
 */

// Email validation
export const validateEmail = (email: string): string | null => {
  if (!email) return 'Email is required'
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!(emailRegex).test(email)) return 'Please enter a valid email address'
  return null
}

// Phone validation
export const validatePhone = (phone: string): string | null => {
  if (!phone) return 'Phone number is required'
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  if (!(phoneRegex).test((phone).replace(/[\s\-\(\)]/g, ''))) {
    return 'Please enter a valid phone number'
  }
  return null
}

// Password validation
export const validatePassword = (password: string): string | null => {
  if (!password) return 'Password is required'
  if ((password).length < 8) return 'Password must be at least 8 characters long'
  if (!/(?=.*[a-z])/.test(password)) return 'Password must contain at least one lowercase letter'
  if (!/(?=.*[A-Z])/.test(password)) return 'Password must contain at least one uppercase letter'
  if (!/(?=.*\d)/.test(password)) return 'Password must contain at least one number'
  return null
}

// Required field validation
export const validateRequired = (value: unknown, fieldName: string): string | null => {
  if (!value || (typeof value === 'string' && (value).trim() === '')) {
    return `${fieldName} is required`
  }
  return null
}

// Number validation
export const validateNumber = (value: string | number, min?: number, max?: number): string | null => {
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return 'Please enter a valid number'
  if (min !== undefined && numValue < min) return `Value must be at least ${min}`
  if (max !== undefined && numValue > max) return `Value must not exceed ${max}`
  return null
}

// Date validation
export const validateDate = (date: string): string | null => {
  if (!date) return 'Date is required'
  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) return 'Please enter a valid date'
  return null
}

// Future date validation
export const validateFutureDate = (date: string): string | null => {
  const dateValidation = validateDate(date)
  if (dateValidation) return dateValidation

  const dateObj = new Date(date)
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  if (dateObj < today) return 'Date must be in the future'
  return null
}

// Past date validation
export const validatePastDate = (date: string): string | null => {
  const dateValidation = validateDate(date)
  if (dateValidation) return dateValidation

  const dateObj = new Date(date)
  const today = new Date()
  today.setHours(23, 59, 59, 999)
  
  if (dateObj > today) return 'Date must be in the past'
  return null
}

// URL validation
export const validateURL = (url: string): string | null => {
  if (!url) return null // URL is optional in most cases
  try {
    new URL(url)
    return null
  } catch {
    return 'Please enter a valid URL'
  }
}

// File validation
export const validateFile = (
  file: File | null, 
  maxSize?: number, 
  allowedTypes?: string[]
): string | null => {
  if (!file) return 'File is required'
  
  if (maxSize && (file).size > maxSize) {
    return `File size must not exceed ${(maxSize / 1024 / 1024).toFixed(1)}MB`
  }
  
  if (allowedTypes && !(allowedTypes).includes((file).type)) {
    return `File type must be one of: ${(allowedTypes).join(', ')}`
  }
  
  return null
}

// Salary validation
export const validateSalary = (salary: string | number): string | null => {
  const numValidation = validateNumber(salary, 0)
  if (numValidation) return numValidation
  
  const numValue = typeof salary === 'string' ? parseFloat(salary) : salary
  if (numValue < 1000) return 'Salary must be at least 1,000'
  if (numValue > 1000000) return 'Salary cannot exceed 1,000,000'
  
  return null
}

// Credit limit validation
export const validateCreditLimit = (limit: string | number): string | null => {
  const numValidation = validateNumber(limit, 0)
  if (numValidation) return numValidation
  
  const numValue = typeof limit === 'string' ? parseFloat(limit) : limit
  if (numValue > 10000000) return 'Credit limit cannot exceed 10,000,000'
  
  return null
}

// Percentage validation
export const validatePercentage = (value: string | number): string | null => {
  const numValidation = validateNumber(value, 0, 100)
  if (numValidation) return numValidation
  return null
}

// Arabic text validation
export const validateArabicText = (text: string): string | null => {
  if (!text) return null // Arabic text is often optional
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/
  if (!(arabicRegex).test(text)) return 'Please enter text in Arabic'
  return null
}

// English text validation
export const validateEnglishText = (text: string): string | null => {
  if (!text) return null // English text is often optional
  const englishRegex = /^[a-zA-Z\s\-\.,']+$/
  if (!(englishRegex).test(text)) return 'Please enter text in English only'
  return null
}

// Validation schema types
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: unknown, data?: Record<string, unknown>) => string | null
}

export interface ValidationSchema {
  [fieldName: string]: ValidationRule
}

// Generic validation function
export const validateField = (
  value: unknown,
  rules: ValidationRule,
  fieldName: string,
  data?: Record<string, unknown>
): string | null => {
  // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return `${fieldName} is required`
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null
  }
  
  // String validations
  if (typeof value === 'string') {
    if ((rules).minLength && (value).length < (rules).minLength) {
      return `${fieldName} must be at least ${(rules).minLength} characters`
    }
    
    if ((rules).maxLength && (value).length > (rules).maxLength) {
      return `${fieldName} must not exceed ${(rules).maxLength} characters`
    }
    
    if ((rules).pattern && !(rules).pattern.test(value)) {
      return `${fieldName} format is invalid`
    }
  }
  
  // Custom validation
  if ((rules).custom) {
    return (rules).custom(value, data)
  }
  
  return null
}

// Validate entire form
export const validateForm = (
  data: Record<string, unknown>,
  schema: ValidationSchema
): Record<string, string> => {
  const errors: Record<string, string> = {}
  
  (Object).keys(schema).forEach(fieldName => {
    const value = data[fieldName]
    const rules = schema[fieldName]
    const error = validateField(value, rules, fieldName, data)

    if (error) {
      errors[fieldName] = error
    }
  })
  
  return errors
}

// VALIDATION FIX: Updated employee validation schema with correct field names
export const employeeValidationSchema: ValidationSchema = {
  // Personal Information
  first_name: { required: true, minLength: 2, maxLength: 50 },
  last_name: { required: true, minLength: 2, maxLength: 50 },
  first_name_ar: { required: true, minLength: 2, maxLength: 50 },
  last_name_ar: { required: true, minLength: 2, maxLength: 50 },

  // Contact Information
  email: { required: true, custom: validateEmail },
  phone: { required: true, custom: validatePhone },
  address: { required: true, minLength: 5, maxLength: 200 },
  emergency_contact: { required: true, custom: validatePhone },
  national_id: { required: true, minLength: 5, maxLength: 20 },

  // Employment Information
  department: { required: true },
  position: { required: true, minLength: 2, maxLength: 100 },
  position_ar: { minLength: 2, maxLength: 100 },
  salary: { required: true, custom: validateSalary },
  hire_date: { required: true, custom: validatePastDate },

  // Optional fields
  employee_id: { minLength: 3, maxLength: 20 },
  status: { required: false }, // Optional for create, required for update
  performance: { required: false }
}

// VALIDATION FIX: Updated department validation schema with correct field names
export const departmentValidationSchema: ValidationSchema = {
  name: { required: true, minLength: 2, maxLength: 100 },
  name_ar: { required: true, minLength: 2, maxLength: 100 },
  description: { maxLength: 500 },
  description_ar: { maxLength: 500 },
  manager: { required: true }, // Manager ID field
  budget: { custom: (value: unknown) => {
    if (!value) return null // Optional field
    return validateNumber(value as string | number, 0)
  }}
}

export const customerValidationSchema: ValidationSchema = {
  name: { required: true, minLength: 2, maxLength: 100 },
  email: { required: true, custom: validateEmail },
  phone: { required: true, custom: validatePhone },
  creditLimit: { required: true, custom: validateCreditLimit },
  address: { required: true, maxLength: 200 }
}

export const projectValidationSchema: ValidationSchema = {
  name: { required: true, minLength: 3, maxLength: 100 },
  description: { required: true, maxLength: 1000 },
  startDate: { required: true, custom: validateDate },
  endDate: { required: true, custom: validateFutureDate },
  budget: { required: true, custom: (value) => validateNumber(value as string | number, 1000) },
  managerId: { required: true }
}

// KPI validation functions
export const validateKPIName = (name: string): string | null => {
  if (!name || name.trim().length === 0) return 'KPI name is required'
  if (name.length < 3) return 'KPI name must be at least 3 characters'
  if (name.length > 100) return 'KPI name cannot exceed 100 characters'
  return null
}

export const validateKPITarget = (target: string | number): string | null => {
  const numValidation = validateNumber(target, 0)
  if (numValidation) return numValidation

  const numValue = typeof target === 'string' ? parseFloat(target) : target
  if (numValue < 0) return 'Target value cannot be negative'
  if (numValue > 1000000000) return 'Target value is too large'

  return null
}

export const validateKPIThreshold = (threshold: string | number, target?: number): string | null => {
  const numValidation = validateNumber(threshold, 0)
  if (numValidation) return numValidation

  const numValue = typeof threshold === 'string' ? parseFloat(threshold) : threshold
  if (numValue < 0) return 'Threshold cannot be negative'

  if (target && numValue >= target) {
    return 'Threshold should be less than target value'
  }

  return null
}

export const validateKPIFormula = (formula: string): string | null => {
  if (!formula) return null // Formula is optional

  if ((formula).length > 500) return 'Formula cannot exceed 500 characters'

  // Basic formula validation - check for balanced parentheses
  let openParens = 0
  for (const char of formula) {
    if (char === '(') openParens++
    if (char === ')') openParens--
    if (openParens < 0) return 'Invalid formula: unmatched closing parenthesis'
  }
  if (openParens > 0) return 'Invalid formula: unmatched opening parenthesis'

  return null
}

export const validateKPIDataSource = (dataSource: string): string | null => {
  if (!dataSource) return null // Data source is optional

  if ((dataSource).length > 200) return 'Data source cannot exceed 200 characters'

  return null
}

// KPI validation schema
export const kpiValidationSchema: ValidationSchema = {
  name: { required: true, custom: validateKPIName },
  name_ar: { required: true, custom: validateKPIName },
  description: { maxLength: 500 },
  description_ar: { maxLength: 500 },
  category: { required: true },
  measurement_type: { required: true },
  unit: { maxLength: 20 },
  unit_ar: { maxLength: 20 },
  frequency: { required: true },
  target_value: { custom: validateKPITarget },
  warning_threshold: { custom: (value: unknown, data?: Record<string, unknown>) => validateKPIThreshold(value as string | number, data?.target_value as number) },
  critical_threshold: { custom: (value: unknown, data?: Record<string, unknown>) => {
    const thresholdValidation = validateKPIThreshold(value as string | number, data?.target_value as number)
    if (thresholdValidation) return thresholdValidation

    if (data?.warning_threshold && typeof value === 'number' && typeof (data).warning_threshold === 'number' && value >= (data).warning_threshold) {
      return 'Critical threshold should be less than warning threshold'
    }
    return null
  }},
  formula: { custom: validateKPIFormula },
  data_source: { custom: validateKPIDataSource },
  trend_direction: { required: true }
}

// KPI Value validation schema
export const kpiValueValidationSchema: ValidationSchema = {
  value: { required: true, custom: (value: unknown) => validateNumber(value as string | number) },
  period_start: { required: true, custom: validatePastDate },
  period_end: { required: true, custom: validatePastDate },
  notes: { maxLength: 1000 },
  data_source: { maxLength: 200 }
}

// KPI Category validation schema
export const kpiCategoryValidationSchema: ValidationSchema = {
  name: { required: true, minLength: 2, maxLength: 100 },
  name_ar: { required: true, minLength: 2, maxLength: 100 },
  description: { maxLength: 500 },
  description_ar: { maxLength: 500 },
  color: { required: true, pattern: /^#[0-9A-Fa-f]{6}$/ }
}

// VALIDATION FIX: Asset validation schema with correct field names
export const assetValidationSchema: ValidationSchema = {
  // Required fields
  asset_tag: { required: true, minLength: 3, maxLength: 50 },
  name: { required: true, minLength: 2, maxLength: 100 },
  category: { required: true },
  status: { required: true },
  condition: { required: true },
  location: { required: true, minLength: 2, maxLength: 100 },
  purchase_date: { required: true, custom: validatePastDate },
  purchase_cost: { required: true, custom: (value: unknown) => validateNumber(value as string | number, 0) },

  // Optional fields
  name_ar: { maxLength: 100 },
  assigned_to: { custom: (value: unknown) => {
    if (!value) return null // Optional field
    return validateNumber(value as string | number, 1)
  }},
  current_value: { custom: (value: unknown) => {
    if (!value) return null // Optional field
    return validateNumber(value as string | number, 0)
  }},
  warranty_expiry: { custom: (value: unknown) => {
    if (!value) return null // Optional field
    return validateFutureDate(value as string)
  }},
  serial_number: { maxLength: 100 },
  brand: { maxLength: 100 },
  model: { maxLength: 100 },
  notes: { maxLength: 1000 },
  notes_ar: { maxLength: 1000 }
}

export const taskValidationSchema: ValidationSchema = {
  title: { required: true, minLength: 3, maxLength: 100 },
  description: { required: true, maxLength: 500 },
  dueDate: { required: true, custom: validateFutureDate },
  priority: { required: true },
  assigneeId: { required: true }
}
