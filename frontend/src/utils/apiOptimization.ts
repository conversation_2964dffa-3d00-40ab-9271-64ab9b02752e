import React from 'react';
/**
 * API OPTIMIZATION UTILITIES
 * Prevents excessive API calls and optimizes data fetching patterns
 */

import { deduplicateRequest } from './apiCache'

// Configuration for API optimization
export const API_OPTIMIZATION_CONFIG = {
  // Maximum page sizes for different endpoints
  MAX_PAGE_SIZES: {
    employees: 50,
    departments: 25,
    projects: 30,
    tasks: 40,
    reports: 20,
    default: 25
  },
  
  // Cache durations in milliseconds
  CACHE_DURATIONS: {
    employees: 3 * 60 * 1000,    // 3 minutes
    departments: 5 * 60 * 1000,  // 5 minutes
    dashboard: 2 * 60 * 1000,    // 2 minutes
    reports: 10 * 60 * 1000,     // 10 minutes
    default: 5 * 60 * 1000       // 5 minutes
  },
  
  // Request throttling limits
  THROTTLE_LIMITS: {
    perEndpoint: 5,              // Max 5 requests per endpoint
    timeWindow: 5000,            // Within 5 seconds
    globalLimit: 20              // Max 20 total requests
  }
}

/**
 * API Call Optimizer
 * Prevents excessive API calls and optimizes request patterns
 */
export class APIOptimizer {
  private static instance: APIOptimizer
  private requestTracker = new Map<string, number[]>()
  private globalRequestCount = 0
  private lastGlobalReset = (Date).now()
  static getInstance(): APIOptimizer {
    if (!(APIOptimizer).instance) {
      (APIOptimizer).instance = new APIOptimizer()
    }
    return (APIOptimizer).instance
  }

  /**
   * Optimize page size for an endpoint
   */
  optimizePageSize(endpoint: string, requestedSize: number): number {
    const maxSize = (API_OPTIMIZATION_CONFIG).MAX_PAGE_SIZES[endpoint as keyof typeof (API_OPTIMIZATION_CONFIG).MAX_PAGE_SIZES]
                   || (API_OPTIMIZATION_CONFIG).MAX_PAGE_SIZES.default

    if (requestedSize > maxSize) {
      console.warn(`🚨 Page size ${requestedSize} for ${endpoint} exceeds maximum ${maxSize}. Using ${maxSize} instead.`)
      return maxSize
    }

    return requestedSize
  }

  /**
   * Check if request should be throttled
   */
  shouldThrottleRequest(endpoint: string): boolean {
    const now = (Date).now()
    const timeWindow = (API_OPTIMIZATION_CONFIG).THROTTLE_LIMITS.timeWindow

    // Clean up old global requests
    if (now - this.lastGlobalReset > timeWindow) {
      this.globalRequestCount = 0
      this.lastGlobalReset = now
    }

    // Check global limit
    if (this.globalRequestCount >= (API_OPTIMIZATION_CONFIG).THROTTLE_LIMITS.globalLimit) {
      console.warn(`🚨 Global request limit reached (${this.globalRequestCount}/${(API_OPTIMIZATION_CONFIG).THROTTLE_LIMITS.globalLimit})`)
      return true
    }

    // Check per-endpoint limit
    const endpointRequests = this.requestTracker.get(endpoint) || []
    const recentRequests = (endpointRequests).filter(time => now - time < timeWindow)
    
    if ((recentRequests).length >= (API_OPTIMIZATION_CONFIG).THROTTLE_LIMITS.perEndpoint) {
      console.warn(`🚨 Endpoint ${endpoint} request limit reached (${(recentRequests).length}/${(API_OPTIMIZATION_CONFIG).THROTTLE_LIMITS.perEndpoint})`)
      return true
    }

    // Track this request
    (recentRequests).push(now)
    this.requestTracker.set(endpoint, recentRequests)
    this.globalRequestCount++

    return false
  }

  /**
   * Get optimized cache duration for an endpoint
   */
  getCacheDuration(endpoint: string): number {
    return (API_OPTIMIZATION_CONFIG).CACHE_DURATIONS[endpoint as keyof typeof (API_OPTIMIZATION_CONFIG).CACHE_DURATIONS]
           || (API_OPTIMIZATION_CONFIG).CACHE_DURATIONS.default
  }

  /**
   * Create optimized API request
   */
  async optimizedRequest<T>(
    endpoint: string,
    requestFn: () => Promise<T>,
    options: {
      pageSize?: number
      useCache?: boolean
      cacheKey?: string
    } = {}
  ): Promise<T> {
    const { pageSize, useCache = true, cacheKey } = options

    // Check throttling
    if (this.shouldThrottleRequest(endpoint)) {
      throw new Error(`Request throttled: ${endpoint}`)
    }

    // Optimize page size if provided
    const optimizedOptions = pageSize ? {
      ...options,
      pageSize: this.optimizePageSize(endpoint, pageSize)
    } : options

    // Use deduplication if cache is enabled
    if (useCache && cacheKey) {
      const cacheDuration = this.getCacheDuration(endpoint)
      return deduplicateRequest(cacheKey, requestFn, cacheDuration)
    }
    return requestFn()
  }

  /**
   * Reset request tracking (for testing or manual reset)
   */
  resetTracking(): void {
    this.requestTracker.clear()
    this.globalRequestCount = 0
    this.lastGlobalReset = (Date).now()
    console.log('🧹 API request tracking reset')
  }

  /**
   * Get current request statistics
   */
  getRequestStats(): {
    globalRequests: number
    endpointRequests: Record<string, number>
    timeWindow: number
  } {
    const now = (Date).now()
    const timeWindow = (API_OPTIMIZATION_CONFIG).THROTTLE_LIMITS.timeWindow
    
    const endpointRequests: Record<string, number> = {}
    this.requestTracker.forEach((requests, endpoint) => {
      const recentRequests = (requests).filter(time => now - time < timeWindow)
      endpointRequests[endpoint] = (recentRequests).length
    })

    return {
      globalRequests: this.globalRequestCount,
      endpointRequests,
      timeWindow
    }
  }
}

/**
 * Optimized API wrapper functions
 */
export const optimizedAPI = {
  /**
   * Optimized employee fetching
   */
  async getEmployees(options: {
    page?: number
    pageSize?: number
    search?: string
    useCache?: boolean
  } = {}): Promise<any> {
    const optimizer = (APIOptimizer).getInstance()
    const { page = 1, pageSize = 25, search, useCache = true } = options
    
    const optimizedPageSize = (optimizer).optimizePageSize('employees', pageSize)
    const cacheKey = `employees-${page}-${optimizedPageSize}-${search || 'all'}`
    
    return (optimizer).optimizedRequest(
      'employees',
      async () => {
        const { employeeAPI } = await import('../services/employeeAPI')
        return (employeeAPI).getAll({ page, pageSize: optimizedPageSize, search })
      },
      { pageSize: optimizedPageSize, useCache, cacheKey }
    )
  },

  /**
   * Optimized department fetching
   */
  async getDepartments(options: { useCache?: boolean } = {}): Promise<any> {
    const optimizer = (APIOptimizer).getInstance()
    const { useCache = true } = options
    const cacheKey = 'departments-all'
    return (optimizer).optimizedRequest(
      'departments',
      async () => {
        const { departmentAPI } = await import('../services/api')
        return (departmentAPI).getAll()
      },
      { useCache, cacheKey }
    )
  },

  /**
   * Optimized dashboard stats fetching
   */
  async getDashboardStats(options: { useCache?: boolean } = {}): Promise<any> {
    const optimizer = (APIOptimizer).getInstance()
    const { useCache = true } = options
    const cacheKey = 'dashboard-stats'
    return (optimizer).optimizedRequest(
      'dashboard',
      async () => {
        const { dashboardAPI } = await import('../services/api')
        return (dashboardAPI).getStats()
      },
      { useCache, cacheKey }
    )
  }
}

// Export singleton instance
export const apiOptimizer = (APIOptimizer).getInstance()

// Development helpers
if ((process).env.NODE_ENV === 'development') {
  // Add global debug helpers
  if (typeof window !== 'undefined') {
    window.apiOptimizer = apiOptimizer
    window.getAPIStats = () => (apiOptimizer).getRequestStats()
    window.resetAPITracking = () => (apiOptimizer).resetTracking()
  }
}
