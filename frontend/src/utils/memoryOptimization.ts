// Memory Optimization utilities
export interface MemoryMetrics {
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

class MemoryMonitor {
  private metrics: MemoryMetrics = {
    usedJSHeapSize: 0,
    totalJSHeapSize: 0,
    jsHeapSizeLimit: 0
  }

  getMemoryMetrics(): MemoryMetrics {
    if ('memory' in performance) {
      const memory = performance.memory
      this.metrics = {
        usedJSHeapSize: memory.usedJSHeapSize || 0,
        totalJSHeapSize: memory.totalJSHeapSize || 0,
        jsHeapSizeLimit: memory.jsHeapSizeLimit || 0
      }
    }
    return this.metrics
  }

  optimizeMemory(): void {
    // Basic memory optimization
    if (typeof window !== 'undefined' && window.gc) {
      window.gc()
    }
  }

  isMemoryPressureHigh(): boolean {
    const metrics = this.getMemoryMetrics()
    if (metrics.jsHeapSizeLimit === 0) return false
    
    const usageRatio = metrics.usedJSHeapSize / metrics.jsHeapSizeLimit
    return usageRatio > 0.8 // 80% threshold
  }
}

export const memoryMonitor = new MemoryMonitor()
export default memoryMonitor
