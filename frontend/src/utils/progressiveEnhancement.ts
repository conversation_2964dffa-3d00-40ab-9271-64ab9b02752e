import React from 'react';
/**
 * Progressive Enhancement & Offline Support
 * Provides graceful degradation and offline functionality
 */

import { log } from './logger'

interface NetworkStatus {
  online: boolean
  effectiveType?: string
  downlink?: number
  rtt?: number
}

interface OfflineCapabilities {
  caching: boolean
  localStorage: boolean
  indexedDB: boolean
  serviceWorker: boolean
  webWorkers: boolean
}

class ProgressiveEnhancementManager {
  private networkStatus: NetworkStatus = { online: navigator.onLine }
  private capabilities: OfflineCapabilities
  private offlineQueue: Array<{ url: string; options: RequestInit; timestamp: number }> = []
  private listeners: Array<(status: NetworkStatus) => void> = []

  constructor() {
    this.capabilities = this.detectCapabilities()
    this.initializeNetworkMonitoring()
    this.initializeOfflineSupport()
  }

  /**
   * Detect browser capabilities
   */
  private detectCapabilities(): OfflineCapabilities {
    return {
      caching: 'caches' in window,
      localStorage: this.testLocalStorage(),
      indexedDB: 'indexedDB' in window,
      serviceWorker: 'serviceWorker' in navigator,
      webWorkers: 'Worker' in window
    }
  }

  private testLocalStorage(): boolean {
    try {
      const test = '__localStorage_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch {
      return false
    }
  }

  /**
   * Initialize network monitoring
   */
  private initializeNetworkMonitoring(): void {
    // Basic online/offline detection
    window.addEventListener('online', () => {
      this.updateNetworkStatus({ online: true })
      this.processOfflineQueue()
    })

    window.addEventListener('offline', () => {
      this.updateNetworkStatus({ online: false })
    })

    // Advanced network information (if available)
    if ('connection' in navigator) {
      const connection = navigator.connection
      
      const updateConnectionInfo = () => {
        this.updateNetworkStatus({
          online: navigator.onLine,
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        })
      }

      connection.addEventListener('change', updateConnectionInfo)
      updateConnectionInfo()
    }
  }

  private updateNetworkStatus(status: Partial<NetworkStatus>): void {
    this.networkStatus = { ...this.networkStatus, ...status }
    log.info('network', 'Network status updated', this.networkStatus)
    
    // Notify listeners
    this.listeners.forEach(listener => listener(this.networkStatus))
  }

  /**
   * Initialize offline support
   */
  private initializeOfflineSupport(): void {
    if (this.capabilities.serviceWorker) {
      this.registerServiceWorker()
    }

    // Intercept fetch requests for offline queueing
    this.interceptFetchRequests()
  }

  private async registerServiceWorker(): Promise<void> {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js')
      log.info('progressive', 'Service Worker registered', registration.scope)
      
      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              this.notifyAppUpdate()
            }
          })
        }
      })
    } catch (error) {
      log.error('progressive', 'Service Worker registration failed', error)
    }
  }

  private notifyAppUpdate(): void {
    // Show update notification
    const event = new CustomEvent('app-update-available')
    window.dispatchEvent(event)
  }

  /**
   * Intercept fetch requests for offline handling
   */
  private interceptFetchRequests(): void {
    const originalFetch = window.fetch
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      try {
        const response = await originalFetch(input, init)
        return response
      } catch (error) {
        // If offline and request failed, queue it
        if (!this.networkStatus.online && this.shouldQueueRequest(input, init)) {
          this.queueOfflineRequest(input, init)
          throw new Error('Request queued for when online')
        }
        throw error
      }
    }
  }

  private shouldQueueRequest(input: RequestInfo | URL, init?: RequestInit): boolean {
    const method = init?.method || 'GET'
    const url = typeof input === 'string' ? input : input.toString()
    
    // Only queue POST, PUT, PATCH, DELETE requests
    return ['POST', 'PUT', 'PATCH', 'DELETE'].includes(method.toUpperCase()) &&
           !url.includes('/auth/') // Don't queue auth requests
  }

  private queueOfflineRequest(input: RequestInfo | URL, init?: RequestInit): void {
    const url = typeof input === 'string' ? input : input.toString()
    
    this.offlineQueue.push({
      url,
      options: init || {},
      timestamp: Date.now()
    })

    log.info('offline', `Queued request: ${url}`)
    
    // Persist queue to localStorage
    if (this.capabilities.localStorage) {
      localStorage.setItem('offline_queue', JSON.stringify(this.offlineQueue))
    }
  }

  /**
   * Process queued offline requests when back online
   */
  private async processOfflineQueue(): Promise<void> {
    if (this.offlineQueue.length === 0) return

    log.info('offline', `Processing ${this.offlineQueue.length} queued requests`)

    const queue = [...this.offlineQueue]
    this.offlineQueue = []

    for (const request of queue) {
      try {
        await fetch(request.url, request.options)
        log.info('offline', `Successfully processed queued request: ${request.url}`)
      } catch (error) {
        log.error('offline', `Failed to process queued request: ${request.url}`, error)
        // Re-queue failed requests
        this.offlineQueue.push(request)
      }
    }

    // Update persisted queue
    if (this.capabilities.localStorage) {
      localStorage.setItem('offline_queue', JSON.stringify(this.offlineQueue))
    }
  }

  /**
   * Load queued requests from localStorage on app start
   */
  loadPersistedQueue(): void {
    if (!this.capabilities.localStorage) return

    try {
      const persistedQueue = localStorage.getItem('offline_queue')
      if (persistedQueue) {
        this.offlineQueue = JSON.parse(persistedQueue)
        log.info('offline', `Loaded ${this.offlineQueue.length} persisted requests`)
        
        // Process if online
        if (this.networkStatus.online) {
          this.processOfflineQueue()
        }
      }
    } catch (error) {
      log.error('offline', 'Failed to load persisted queue', error)
    }
  }

  /**
   * Cache critical resources
   */
  async cacheResources(urls: string[]): Promise<void> {
    if (!this.capabilities.caching) return

    try {
      const cache = await caches.open('app-critical-v1')
      await cache.addAll(urls)
      log.info('progressive', `Cached ${urls.length} critical resources`)
    } catch (error) {
      log.error('progressive', 'Failed to cache resources', error)
    }
  }

  /**
   * Get cached resource
   */
  async getCachedResource(url: string): Promise<Response | null> {
    if (!this.capabilities.caching) return null

    try {
      const cache = await caches.open('app-critical-v1')
      return await cache.match(url)
    } catch (error) {
      log.error('progressive', 'Failed to get cached resource', error)
      return null
    }
  }

  /**
   * Store data offline
   */
  storeOfflineData(key: string, data: any): boolean {
    if (!this.capabilities.localStorage) return false

    try {
      const offlineData = {
        data,
        timestamp: Date.now(),
        version: 1
      }
      localStorage.setItem(`offline_${key}`, JSON.stringify(offlineData))
      return true
    } catch (error) {
      log.error('offline', 'Failed to store offline data', error)
      return false
    }
  }

  /**
   * Get offline data
   */
  getOfflineData(key: string, maxAge = 24 * 60 * 60 * 1000): any | null {
    if (!this.capabilities.localStorage) return null

    try {
      const stored = localStorage.getItem(`offline_${key}`)
      if (!stored) return null

      const offlineData = JSON.parse(stored)
      const age = Date.now() - offlineData.timestamp

      if (age > maxAge) {
        localStorage.removeItem(`offline_${key}`)
        return null
      }

      return offlineData.data
    } catch (error) {
      log.error('offline', 'Failed to get offline data', error)
      return null
    }
  }

  /**
   * Subscribe to network status changes
   */
  onNetworkChange(callback: (status: NetworkStatus) => void): () => void {
    this.listeners.push(callback)
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  /**
   * Get current capabilities and status
   */
  getStatus() {
    return {
      network: this.networkStatus,
      capabilities: this.capabilities,
      queuedRequests: this.offlineQueue.length
    }
  }

  /**
   * Check if feature should be enabled based on capabilities
   */
  shouldEnableFeature(feature: keyof OfflineCapabilities): boolean {
    return this.capabilities[feature]
  }

  /**
   * Graceful degradation for features
   */
  withFallback<T>(
    primaryFunction: () => T,
    fallbackFunction: () => T,
    requiredCapability?: keyof OfflineCapabilities
  ): T {
    try {
      if (requiredCapability && !this.capabilities[requiredCapability]) {
        return fallbackFunction()
      }
      return primaryFunction()
    } catch (error) {
      log.warn('progressive', 'Primary function failed, using fallback', error)
      return fallbackFunction()
    }
  }
}

// Singleton instance
export const progressiveEnhancement = new ProgressiveEnhancementManager()

// Initialize on module load
if (typeof window !== 'undefined') {
  progressiveEnhancement.loadPersistedQueue()
  
  // Cache critical resources
  progressiveEnhancement.cacheResources([
    '/',
    '/assets/critical.css',
    '/assets/app.js'
  ])
}

export default progressiveEnhancement
