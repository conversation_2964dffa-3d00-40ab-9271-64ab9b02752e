import React from 'react';
/**
 * Push Notification Manager
 * Handles push notification subscription, management, and delivery
 */

import { log } from './logger'

interface NotificationPayload {
  title: string
  body: string
  icon?: string
  badge?: string
  image?: string
  data?: any
  actions?: Array<{
    action: string
    title: string
    icon?: string
  }>
  tag?: string
  requireInteraction?: boolean
  silent?: boolean
  vibrate?: number[]
  timestamp?: number
}

interface PushSubscriptionData {
  endpoint: string
  keys: {
    p256dh: string
    auth: string
  }
  userId?: string
  deviceInfo?: {
    userAgent: string
    platform: string
    language: string
  }
}

class PushNotificationManager {
  private registration: ServiceWorkerRegistration | null = null
  private subscription: PushSubscription | null = null
  private vapidPublicKey: string = ''
  private isSupported: boolean = false
  private permission: NotificationPermission = 'default'
  constructor() {
    this.checkSupport()
    this.initialize()
  }
  private checkSupport(): void {
    this.isSupported = 
      'serviceWorker' in navigator &&
      'PushManager' in window &&
      'Notification' in window

    if (!this.isSupported) {
      log.warn('push', 'Push notifications not supported in this browser')
    }
  }
  private async initialize(): Promise<void> {
    if (!this.isSupported) return

    try {
      // Get service worker registration
      this.registration = await navigator.serviceWorker.ready
      
      // Get current permission status
      this.permission = Notification.permission
      
      // Get VAPID public key from environment or API
      this.vapidPublicKey = import.meta.env.VITE_VAPID_PUBLIC_KEY || await this.getVapidKey()

      // Check for existing subscription
      this.subscription = await this.registration.pushManager.getSubscription()
      
      log.info('push', 'Push notification manager initialized', {
        supported: this.isSupported,
        permission: this.permission,
        hasSubscription: !!this.subscription
      })
    } catch (error) {
      log.error('push', 'Failed to initialize push notifications', error)
    }
  }
  private async getVapidKey(): Promise<string> {
    try {
      const response: any = await fetch('/api/push/vapid-key')
      const data = await response.json()
      return data.publicKey
    } catch (error) {
      log.error('push', 'Failed to get VAPID key', error)
      return ''
    }
  }

  /**
   * Request notification permission
   */
  public async requestPermission(): Promise<NotificationPermission> {
    if (!this.isSupported) {
      throw new Error('Push notifications not supported')
    }

    if (this.permission === 'granted') {
      return this.permission
    }

    try {
      this.permission = await Notification.requestPermission()
      
      log.info('push', 'Notification permission requested', {
        permission: this.permission
      })

      return this.permission
    } catch (error) {
      log.error('push', 'Failed to request notification permission', error)
      throw error
    }
  }

  /**
   * Subscribe to push notifications
   */
  public async subscribe(): Promise<PushSubscriptionData | null> {
    if (!this.isSupported || !this.registration) {
      throw new Error('Push notifications not supported or service worker not ready')
    }

    if (this.permission !== 'granted') {
      await this.requestPermission()
    }

    if (this.permission !== 'granted') {
      throw new Error('Notification permission denied')
    }

    try {
      // Unsubscribe from existing subscription if any
      if (this.subscription) {
        await this.subscription.unsubscribe()
      }

      // Create new subscription
      this.subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
      })

      const subscriptionData: PushSubscriptionData = {
        endpoint: this.subscription.endpoint,
        keys: {
          p256dh: this.arrayBufferToBase64(this.subscription.getKey('p256dh')!),
          auth: this.arrayBufferToBase64(this.subscription.getKey('auth')!)
        },
        deviceInfo: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          language: navigator.language
        }
      }

      // Send subscription to server
      await this.sendSubscriptionToServer(subscriptionData)

      log.info('push', 'Successfully subscribed to push notifications')
      return subscriptionData

    } catch (error) {
      log.error('push', 'Failed to subscribe to push notifications', error)
      throw error
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  public async unsubscribe(): Promise<boolean> {
    if (!this.subscription) {
      return true
    }

    try {
      const success = await this.subscription.unsubscribe()
      
      if (success) {
        // Remove subscription from server
        await this.removeSubscriptionFromServer()
        this.subscription = null
        log.info('push', 'Successfully unsubscribed from push notifications')
      }

      return success
    } catch (error) {
      log.error('push', 'Failed to unsubscribe from push notifications', error)
      return false
    }
  }

  /**
   * Send subscription data to server
   */
  private async sendSubscriptionToServer(subscriptionData: PushSubscriptionData): Promise<void> {
    try {
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(subscriptionData)
      })

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`)
      }
    } catch (error) {
      log.error('push', 'Failed to send subscription to server', error)
      throw error
    }
  }

  /**
   * Remove subscription from server
   */
  private async removeSubscriptionFromServer(): Promise<void> {
    if (!this.subscription) return

    try {
      await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          endpoint: this.subscription.endpoint
        })
      })
    } catch (error) {
      log.error('push', 'Failed to remove subscription from server', error)
    }
  }

  /**
   * Show local notification
   */
  public async showNotification(payload: NotificationPayload): Promise<void> {
    if (!this.isSupported || !this.registration) {
      throw new Error('Notifications not supported')
    }

    if (this.permission !== 'granted') {
      throw new Error('Notification permission not granted')
    }

    try {
      await this.registration.showNotification(payload.title, {
        body: payload.body,
        icon: payload.icon || '/assets/icons/icon-(192x192).png',
        badge: payload.badge || '/assets/icons/badge-(72x72).png',
        data: payload.data,
        tag: payload.tag,
        requireInteraction: payload.requireInteraction,
        silent: payload.silent,
        vibrate: payload.vibrate || [100, 50, 100],
        timestamp: payload.timestamp || Date.now(),
        dir: 'rtl',
        lang: 'ar'
      })

      log.debug('push', 'Local notification shown', payload.title)
    } catch (error) {
      log.error('push', 'Failed to show notification', error)
      throw error
    }
  }

  /**
   * Get notification history
   */
  public async getNotifications(): Promise<Notification[]> {
    if (!this.isSupported || !this.registration) {
      return []
    }

    try {
      return await this.registration.getNotifications()
    } catch (error) {
      log.error('push', 'Failed to get notifications', error)
      return []
    }
  }

  /**
   * Clear all notifications
   */
  public async clearNotifications(): Promise<void> {
    if (!this.isSupported || !this.registration) {
      return
    }

    try {
      const notifications = await this.registration.getNotifications()
      notifications.forEach(notification => notification.close())
      log.info('push', 'All notifications cleared')
    } catch (error) {
      log.error('push', 'Failed to clear notifications', error)
    }
  }

  /**
   * Utility functions
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes: any = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return window.btoa(binary)
  }

  /**
   * Get current status
   */
  public getStatus() {
    return {
      supported: this.isSupported,
      permission: this.permission,
      subscribed: !!this.subscription,
      endpoint: this.subscription?.endpoint
    }
  }

  /**
   * Check if subscribed
   */
  public isSubscribed(): boolean {
    return !!this.subscription
  }

  /**
   * Get subscription
   */
  public getSubscription(): PushSubscription | null {
    return this.subscription
  }
}

// Singleton instance
export const pushNotifications = new PushNotificationManager()

// Convenience functions
export const requestNotificationPermission = () => pushNotifications.requestPermission()
export const subscribeToPush = () => pushNotifications.subscribe()
export const unsubscribeFromPush = () => pushNotifications.unsubscribe()
export const showNotification = (payload: NotificationPayload => pushNotifications.showNotification(payload)
export const clearAllNotifications = () => pushNotifications.clearNotifications()
export const getNotificationStatus = () => pushNotifications.getStatus()

export default pushNotifications
