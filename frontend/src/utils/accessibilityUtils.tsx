/**
 * FIXED: Comprehensive Accessibility Utilities
 * Provides utilities for improving accessibility across the application
 */
import React from 'react'

import { useEffect, useRef, useCallback } from 'react'

// Note: Use React components with aria-live regions instead of DOM manipulation
export const announceToScreenReader: any = (message: string, priority: 'polite' | 'assertive' = 'polite'): void => {
  console.log(`Screen reader announcement (${priority}): ${message}`)
  // In React, use a dedicated announcement component with aria-live
}

// FIXED: Focus trap utility for modals and dropdowns
export const useFocusTrap = (isActive: boolean): void => {
  const containerRef = useRef<HTMLElement>(null)
  const trapFocus = useCallback((e: KeyboardEvent) => {
    if (!isActive || !containerRef.current) return
    
    const focusableElements = containerRef.current.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    const firstElement = focusableElements[0] as HTMLElement
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement
    
    if (e.key === 'Tab') {
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus()
          e.preventDefault()
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus()
          e.preventDefault()
        }
      }
    }
    
    // Close on Escape
    if (e.key === 'Escape') {
      const closeButton = containerRef.current.querySelector('[data-close-button]') as HTMLElement
      closeButton?.click()
    }
  }, [isActive])
  useEffect(() => {
    if (isActive) {
      document.addEventListener('keydown', trapFocus)
      
      // Focus first element when activated
      const firstFocusable = containerRef.current?.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement
      firstFocusable?.focus()
    }
    
    return () => {
      document.removeEventListener('keydown', trapFocus)
    }
  }, [isActive, trapFocus])
  
  return containerRef
}

// FIXED: Skip link utility for keyboard navigation
export const SkipLink: React.FC<{ href: string; children: React.ReactNode }> = ({ href, children }) => {
  return (
    <a
      href={href as React.RefObject<HTMLDivElement>}
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-blue-600 focus:text-white focus:rounded focus:shadow-lg"
      onFocus={() => announceToScreenReader('Skip link focused')}
    >
      {children}
    </a>
  )
}

// FIXED: Accessible form validation
export const useAccessibleValidation = (): void => {
  const announceError = useCallback((fieldName: string, errorMessage: string) => {
    announceToScreenReader(`${fieldName}: ${errorMessage}`, 'assertive')
  }, [])
  const announceSuccess = useCallback((message: string) => {
    announceToScreenReader(message, 'polite')
  }, [])
  const getErrorId = useCallback((fieldName: string) => {
    return `${fieldName}-error`
  }, [])
  const getDescriptionId = useCallback((fieldName: string) => {
    return `${fieldName}-description`
  }, [])
  
  return {
    announceError,
    announceSuccess,
    getErrorId,
    getDescriptionId
  }
}

// FIXED: Accessible button component with proper ARIA attributes
interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger'
  loading?: boolean
  loadingText?: string
  icon?: React.ReactNode
  children: React.ReactNode
}
export const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  variant = 'primary',
  loading = false,
  loadingText = 'Loading...',
  icon,
  children,
  disabled,
  className = '',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  }
  
  const isDisabled = disabled || loading
  
  return (<button
      {...props}
      disabled={isDisabled}
      className={`${baseClasses} ${variantClasses[variant]} ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
      aria-disabled={isDisabled}
      aria-describedby={loading ? `${props.id}-loading` : undefined}
    >
      {loading && (<svg
          className="animate-spin -ml-1 mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-(8V0C5).373 0 0 (5).373 0 12h4zm2 (5).291A7.962 (7).962 0 014 12H0c0 (3).042 1.135 (5).824 3 (7).938l3-(2).647z"
          />
        </svg>
      )}
      {icon && !loading && <span className="mr-2" aria-hidden="true">{icon}</span>}
      {loading ? loadingText : children}
      {loading && (<span id={`${props.id}-loading`} className="sr-only">
          {loadingText}
        </span>
      )}
    </button>
  )
}

// FIXED: Accessible form field component
interface AccessibleFormFieldProps {
  label: string
  name: string
  type?: string
  required?: boolean
  error?: string
  description?: string
  children?: React.ReactNode
  className?: string
}
export const AccessibleFormField: React.FC<AccessibleFormFieldProps> = ({
  label,
  name,
  type = 'text',
  required = false,
  error,
  description,
  children,
  className = ''
}) => {
  const { getErrorId, getDescriptionId } = useAccessibleValidation()
  
  const fieldId = `field-${name}`
  const errorId = getErrorId(name)
  const descriptionId = getDescriptionId(name)
  
  return (
    <div className={`space-y-2 ${className}`}>
      <label
        htmlFor={fieldId}
        className="block text-sm font-medium text-white"
      >
        {label}
        {required && (
          <span className="text-red-400 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      {description && (
        <p id={descriptionId} className="text-sm text-white/60">
          {description}
        </p>
      )}
      
      {children || (
        <input
          id={fieldId}
          name={name}
          type={type}
          required={required}
          className={`w-full px-3 py-2 border rounded-md bg-white/10 border-white/20 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            error ? 'border-red-500 focus:ring-red-500' : ''
          }`}
          aria-describedby={`${description ? descriptionId : ''} ${error ? errorId : ''}`.trim()}
          aria-invalid={error ? 'true' : 'false'}
        />
      )}
      
      {error && (
        <p
          id={errorId}
          className="text-sm text-red-400"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
    </div>
  )
}

// FIXED: Accessible modal component
interface AccessibleModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  className?: string
}

export const AccessibleModal: React.FC<AccessibleModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  className = ''
}) => {
  const modalRef = useFocusTrap(isOpen)
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
      announceToScreenReader(`${title} dialog opened`)
    } else {
      document.body.style.overflow = 'unset'
    }
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, title])
  
  if (!isOpen) return null
  
  return (<div
      className="fixed inset-0 z-50 flex items-center justify-center"
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
        aria-hidden="true"
      />
      
      <div
        ref={modalRef as React.RefObject<HTMLDivElement>}
        className={`relative bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 ${className}`}
      >
        <div className="p-6">
          <h2 id="modal-title" className="text-lg font-semibold text-white mb-4">
            {title}
          </h2>
          
          {children}
          
          <button
            data-close-button
            onClick={onClose}
            className="absolute top-4 right-4 text-white/60 hover:text-white"
            aria-label="Close dialog"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
}

// FIXED: Keyboard navigation utilities
export const useKeyboardNavigation = (items: HTMLElement[], loop = true): void => {
  const currentIndex = useRef(0)
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault()
        currentIndex.current = loop 
          ? (currentIndex.current + 1) % items.length
          : Math.min(currentIndex.current + 1, items.length - 1)
        items[currentIndex.current]?.focus()
        break
        
      case 'ArrowUp':
        e.preventDefault()
        currentIndex.current = loop
          ? (currentIndex.current - 1 + items.length) % items.length
          : Math.max(currentIndex.current - 1, 0)
        items[currentIndex.current]?.focus()
        break
        
      case 'Home':
        e.preventDefault()
        currentIndex.current = 0
        items[0]?.focus()
        break
        
      case 'End':
        e.preventDefault()
        currentIndex.current = items.length - 1
        items[items.length - 1]?.focus()
        break
    }
  }, [items, loop])
  return { handleKeyDown, currentIndex: currentIndex.current }
}

export default {
  announceToScreenReader,
  useFocusTrap,
  SkipLink,
  useAccessibleValidation,
  AccessibleButton,
  AccessibleFormField,
  AccessibleModal,
  useKeyboardNavigation
}
