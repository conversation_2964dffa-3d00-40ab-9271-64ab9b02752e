import React from 'react';
/**
 * ERROR HANDLING FIX: Comprehensive Error Handler
 * Provides unified error handling across the entire application
 */

import { toast } from 'react-hot-toast'
import { normalizeError, AppError, NetworkError } from './errorHandling'

interface useErrorHandlerProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


export interface ErrorContext {
  component?: string
  action?: string
  userId?: string
  timestamp?: string
  url?: string
  userAgent?: string
  additionalData?: Record<string, unknown>
}

export interface ErrorHandlingOptions {
  language?: 'ar' | 'en'
  showToast?: boolean
  logToConsole?: boolean
  sendToService?: boolean
  retryable?: boolean
  onRetry?: () => void
  fallbackMessage?: string
}

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical'

export interface ProcessedError extends AppError {
  severity: ErrorSeverity
  category: string
  userMessage: string
  userMessageAr: string
  actionable: boolean
  retryable: boolean
  context?: ErrorContext
}

class ComprehensiveErrorHandler {
  private errorQueue: ProcessedError[] = []
  private maxQueueSize = 100
  private isOnline = navigator.onLine
  private errorThrottle = new Map<string, number>()
  private readonly THROTTLE_DURATION = 5000 // 5 seconds

  constructor() {
    this.setupGlobalErrorHandlers()
    this.setupNetworkStatusListener()
  }

  // ERROR FIX: Setup global error handlers
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event: PromiseRejectionEvent => {
      // Safe console logging
      if (typeof console !== 'undefined' && console.error) {
        try {
          console.error('Unhandled promise rejection:', event.reason)
        } catch (logError) {
          // Fallback if console is broken
        }
      }
      this.handleError(event.reason, {
        component: 'Global',
        action: 'unhandledrejection'
      }, {
        language: 'en',
        showToast: true,
        logToConsole: true
      })
      event.preventDefault()
    })

    // Handle global JavaScript errors with throttling
    window.addEventListener('error', (event) => {
      const errorKey: any = `${event.filename}:${event.lineno}:${event.colno}`
      const now = Date.now()
      const lastTime = this.errorThrottle.get(errorKey) || 0

      // Only log if enough time has passed since last occurrence
      if (now - lastTime > this.THROTTLE_DURATION) {
        // Safe console logging
        if (typeof console !== 'undefined' && console.error) {
          try {
            console.error('Global JavaScript error:', event.error)
          } catch (logError) {
            // Fallback if console is broken
          }
        }
        this.errorThrottle.set(errorKey, now)

        this.handleError(event.error, {
          component: 'Global',
          action: 'javascript_error',
          additionalData: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          }
        }, {
          language: 'en',
          showToast: false, // Reduce toast spam
          logToConsole: false // Already logged above
        })
      }
    })

    // Handle network status changes
    window.addEventListener('online', () => {
      this.isOnline = true
      toast.success('اتصال الإنترنت متاح مرة أخرى', {
        duration: 3000,
        position: 'top-center'
      })
    })
    window.addEventListener('offline', () => {
      this.isOnline = false
      toast.error('انقطع اتصال الإنترنت', {
        duration: 5000,
        position: 'top-center'
      })
    })
  }

  private setupNetworkStatusListener(): void {
    // Monitor network status
    setInterval(() => {
      const currentStatus = navigator.onLine;
      if (currentStatus !== this.isOnline) {
        this.isOnline = currentStatus
      }
    }, 5000)
  }

  // ERROR FIX: Main error handling method
  handleError(
    error: unknown,
    context: ErrorContext = {},
    options: ErrorHandlingOptions = {}): ProcessedError {
    const {
      language = 'en',
      showToast = true,
      logToConsole = true,
      sendToService = false,
      retryable = false,
      onRetry,
      fallbackMessage
    } = options

    // Normalize the error
    const normalizedError = normalizeError(error)
    
    // Process the error
    const processedError = this.processError(normalizedError, context, language)
    
    // Add to error queue
    this.addToErrorQueue(processedError)

    // Handle based on severity
    this.handleBySeverity(processedError, {
      showToast,
      logToConsole,
      sendToService,
      retryable,
      onRetry,
      fallbackMessage,
      language
    })

    return processedError
  }

  // ERROR FIX: Process error with categorization and severity
  private processError(
    error: AppError,
    context: ErrorContext,
    language: 'ar' | 'en'
  ): ProcessedError {
    const category = this.categorizeError(error)
    const severity = this.determineSeverity(error, category)
    const { userMessage, userMessageAr } = this.generateUserMessage(error, category, language)
    const actionable = this.isActionable(error, category)
    const retryable = this.isRetryable(error, category)

    return {
      ...error,
      severity,
      category,
      userMessage,
      userMessageAr,
      actionable,
      retryable,
      context: {
        ...context,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent
      }
    }
  }

  // ERROR FIX: Categorize errors
  private categorizeError(error: AppError): string {
    const code = error.code?.toLowerCase() || ''
    const message = error.message?.toLowerCase() || ''

    if (code.includes('network') || message.includes('network') || message.includes('fetch')) {
      return 'network'
    }
    if (code.includes('auth') || message.includes('unauthorized') || message.includes('forbidden')) {
      return 'authentication'
    }
    if (code.includes('validation') || message.includes('validation') || message.includes('invalid')) {
      return 'validation'
    }
    if (code.includes('permission') || message.includes('permission') || message.includes('access')) {
      return 'permission'
    }
    if (code.includes('timeout') || message.includes('timeout')) {
      return 'timeout'
    }
    if (code.includes('server') || message.includes('500') || message.includes('internal')) {
      return 'server'
    }
    if (code.includes('not_found') || message.includes('404') || message.includes('not found')) {
      return 'not_found'
    }

    return 'unknown'
  }

  // ERROR FIX: Determine error severity
  private determineSeverity(error: AppError, category: string): ErrorSeverity {
    // Critical errors that break the app
    if (category === 'server' || category === 'authentication') {
      return 'critical'
    }
    
    // High priority errors that affect functionality
    if (category === 'network' || category === 'permission') {
      return 'high'
    }
    
    // Medium priority errors that affect UX
    if (category === 'validation' || category === 'timeout') {
      return 'medium'
    }
    
    // Low priority errors
    return 'low'
  }

  // ERROR FIX: Generate user-friendly messages
  private generateUserMessage(
    error: AppError,
    category: string,
    language: 'ar' | 'en': { userMessage: string; userMessageAr: string } {
    const messages = {
      network: {
        en: 'Network connection problem. Please check your internet connection and try again.',
        ar: 'مشكلة في الاتصال بالشبكة. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.'
      },
      authentication: {
        en: 'Authentication required. Please log in again.',
        ar: 'مطلوب تسجيل الدخول. يرجى تسجيل الدخول مرة أخرى.'
      },
      validation: {
        en: 'Please check your input and try again.',
        ar: 'يرجى التحقق من البيانات المدخلة والمحاولة مرة أخرى.'
      },
      permission: {
        en: 'You don\'t have permission to perform this action.',
        ar: 'ليس لديك صلاحية لتنفيذ هذا الإجراء.'
      },
      timeout: {
        en: 'The request took too long. Please try again.',
        ar: 'استغرق الطلب وقتاً طويلاً. يرجى المحاولة مرة أخرى.'
      },
      server: {
        en: 'Server error occurred. Please try again later.',
        ar: 'حدث خطأ في الخادم. يرجى المحاولة لاحقاً.'
      },
      not_found: {
        en: 'The requested resource was not found.',
        ar: 'المورد المطلوب غير موجود.'
      },
      unknown: {
        en: 'An unexpected error occurred. Please try again.',
        ar: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'
      }
    }

    const categoryMessages = messages[category as keyof typeof messages] || messages.unknown

    return {
      userMessage: categoryMessages.en,
      userMessageAr: categoryMessages.ar
    }
  }

  // ERROR FIX: Check if error is actionable
  private isActionable(error: AppError, category: string): boolean {
    return ['network', 'timeout', 'server', 'validation'].includes(category)
  }

  // ERROR FIX: Check if error is retryable
  private isRetryable(error: AppError, category: string): boolean {
    return ['network', 'timeout', 'server'].includes(category)
  }

  // ERROR FIX: Handle error based on severity
  private handleBySeverity(
    error: ProcessedError,
    options: ErrorHandlingOptions & { language: 'ar' | 'en' } {
    const { showToast, logToConsole, language, retryable, onRetry } = options

    if (logToConsole) {
      // Safe console logging
      if (typeof console !== 'undefined' && console.error) {
        try {
          console.error(`[${error.severity.toUpperCase()}] ${error.category}:`, error)
        } catch (logError) {
          // Fallback if console is broken
        }
      }
    }

    if (showToast) {
      const message = language === 'ar' ? error.userMessageAr : error.userMessage
      
      switch (error.severity) {
        case 'critical':
          toast.error(message, {
            duration: 8000,
            position: 'top-center',
            style: {
              background: 'rgba(239, 68, 68, 0.95)',
              color: 'white',
              fontWeight: '600'
            }
          })
          break
          
        case 'high':
          toast.error(message, {
            duration: 6000,
            position: 'top-right'
          })
          break
          
        case 'medium':
          toast.warning(message, {
            duration: 4000,
            position: 'top-right'
          })
          break
          
        case 'low':
          toast(message, {
            duration: 3000,
            position: 'bottom-right',
            icon: 'ℹ️'
          })
          break
      }

      // Add retry button for retryable errors
      if (retryable && onRetry && error.retryable) {
        setTimeout(() => {
          toast((t) => (
            <div className="flex items-center space-x-2">
              <span>{language === 'ar' ? 'هل تريد المحاولة مرة أخرى؟' : 'Want to try again?'}</span>
              <button
                onClick={() => {
                  onRetry()
                  toast.dismiss(t.id)
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm transition-colors"
              >
                {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
              </button>
            </div>
          ), {
            duration: 8000,
            position: 'top-center'
          })
        }, 1000)
      }
    }
  }

  // ERROR FIX: Add error to queue for analysis
  private addToErrorQueue(error: ProcessedError): void {
    this.errorQueue.push(error)

    // Keep queue size manageable
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }
  }

  // ERROR FIX: Get error statistics
  getErrorStats() {
    const bySeverity = this.errorQueue.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1
      return acc
    }, {} as Record<ErrorSeverity, number>)
    const byCategory = this.errorQueue.reduce((acc, error) => {
      acc[error.category] = (acc[error.category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const recent = this.errorQueue.slice(-10)

    return {
      total: this.errorQueue.length,
      bySeverity,
      byCategory,
      recent
    }
  }

  clearErrorQueue(): void {
    this.errorQueue = [];
  }

  isSystemHealthy(): boolean {
    return true;
  }
}

// Export singleton instance
export const comprehensiveErrorHandler = new ComprehensiveErrorHandler()

// ERROR FIX: React hook for error handling
export function useErrorHandler(language: 'ar' | 'en' = 'en'): void {
  const handleError = (
    error: unknown,
    context: ErrorContext = {},
    options: Partial<ErrorHandlingOptions> = {}
  ): void => {
    return comprehensiveErrorHandler.handleError(error, context, {
      language,
      ...options
    })
  }

  const handleAsyncError = async (
    asyncFn: () => Promise<any>,
    context: ErrorContext = {},
    options: Partial<ErrorHandlingOptions> = {}
  ) => {
    try {
      return await asyncFn()
    } catch (error) {
      handleError(error, context, options)
      throw error
    }
  }

  return {
    handleError,
    handleAsyncError,
    getErrorStats: () => comprehensiveErrorHandler.getErrorStats(),
    isSystemHealthy: () => comprehensiveErrorHandler.isSystemHealthy()
  }
}

export default comprehensiveErrorHandler
