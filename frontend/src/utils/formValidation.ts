import React from 'react';
/**
 * VALIDATION FIX: Comprehensive Form Validation System
 * Provides standardized validation with real-time feedback and better error messages
 */

import { ValidationSchema, validateField } from './validation'

export interface FormValidationOptions {
  language?: 'ar' | 'en'
  realTimeValidation?: boolean
  showFieldErrors?: boolean
  validateOnBlur?: boolean
  validateOnChange?: boolean
}

export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
  fieldErrors: Record<string, string[]>
  firstErrorField?: string
}

export interface FormFieldConfig {
  name: string
  label: string
  labelAr?: string
  type: 'text' | 'email' | 'number' | 'select' | 'textarea' | 'date' | 'tel'
  required?: boolean
  placeholder?: string
  placeholderAr?: string
  options?: Array<{ value: string | number; label: string; labelAr?: string }>
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: RegExp
    custom?: (value: unknown) => string | null
  }
}

class FormValidationManager {
  private validationSchemas: Map<string, ValidationSchema> = new Map()
  private formConfigs: Map<string, FormFieldConfig[]> = new Map()

  // Register validation schema for a form
  registerSchema(formName: string, schema: ValidationSchema): void {
    this.validationSchemas.set(formName, schema)
  }

  // Register form field configuration
  registerFormConfig(formName: string, config: FormFieldConfig[]): void {
    this.formConfigs.set(formName, config)
  }

  // VALIDATION FIX: Comprehensive form validation with better error messages
  validateForm(
    formName: string,
    data: Record<string, unknown>,
    options: FormValidationOptions = {}
  ): ValidationResult {
    const { language = 'en' } = options
    const schema = this.validationSchemas.get(formName)
    
    if (!schema) {
      console.warn(`No validation schema found for form: ${formName}`)
      return { isValid: true, errors: {}, fieldErrors: {} }
    }

    const errors: Record<string, string> = {}
    const fieldErrors: Record<string, string[]> = {}
    let firstErrorField: string | undefined

    // Validate each field
    Object.keysschema.forEach(fieldName => {
      const value = data[fieldName]
      const rules = schema[fieldName]
      const fieldConfig = this.getFieldConfig(formName, fieldName)
      
      // Get field display name
      const displayName = this.getFieldDisplayName(fieldConfig, fieldName, language)
      
      // Validate field
      const error = validateField(value, rules, displayName, data)
      
      if (error) {
        errors[fieldName] = error
        fieldErrors[fieldName] = [error]
        
        if (!firstErrorField) {
          firstErrorField = fieldName
        }
      }
    })

    return {
      isValid: Object.keyserrors.length === 0,
      errors,
      fieldErrors,
      firstErrorField
    }
  }

  // VALIDATION FIX: Real-time field validation
  validateSingleField(
    formName: string,
    fieldName: string,
    value: unknown,
    allData: Record<string, unknown> = {},
    language: 'ar' | 'en' = 'en'
  ): string | null {
    const schema = this.validationSchemas.get(formName)
    
    if (!schema || !schema[fieldName]) {
      return null
    }

    const rules = schema[fieldName]
    const fieldConfig = this.getFieldConfig(formName, fieldName)
    const displayName = this.getFieldDisplayName(fieldConfig, fieldName, language)
    
    return validateField(value, rules, displayName, allData)
  }

  // Get field configuration
  private getFieldConfig(formName: string, fieldName: string): FormFieldConfig | undefined {
    const config = this.formConfigs.get(formName)
    return config?.find(field => field.name === fieldName)
  }

  // Get localized field display name
  private getFieldDisplayName(
    fieldConfig: FormFieldConfig | undefined,
    fieldName: string,
    language: 'ar' | 'en'
  ): string {
    if (fieldConfig) {
      return language === 'ar' && fieldConfig.labelAr 
        ? fieldConfig.labelAr 
        : fieldConfig.label
    }
    
    // Fallback to field name with proper formatting
    return this.formatFieldName(fieldName, language)
  }

  // Format field name for display
  private formatFieldName(fieldName: string, language: 'ar' | 'en'): string {
    // Convert snake_case to readable format
    const formatted = fieldName
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .toLowerCase()
      .replace(/^./, str => str.toUpperCase())

    // Arabic translations for common field names
    if (language === 'ar') {
      const arabicTranslations: Record<string, string> = {
        'first name': 'الاسم الأول',
        'last name': 'اسم العائلة',
        'email': 'البريد الإلكتروني',
        'phone': 'رقم الهاتف',
        'address': 'العنوان',
        'department': 'القسم',
        'position': 'المنصب',
        'salary': 'الراتب',
        'hire date': 'تاريخ التوظيف',
        'emergency contact': 'جهة الاتصال الطارئة',
        'national id': 'رقم الهوية الوطنية',
        'employee id': 'رقم الموظف',
        'name': 'الاسم',
        'description': 'الوصف',
        'manager': 'المدير',
        'budget': 'الميزانية'
      }
      
      return arabicTranslations[formatted.toLowerCase()] || formatted
    }

    return formatted
  }

  // VALIDATION FIX: Get user-friendly error messages
  getErrorMessage(
    fieldName: string,
    errorType: string,
    _value: unknown, // Prefixed with underscore to indicate intentionally unused
    language: 'ar' | 'en' = 'en',
    fieldConfig?: FormFieldConfig
  ): string {
    const displayName = fieldConfig 
      ? this.getFieldDisplayName(fieldConfig, fieldName, language)
      : this.formatFieldName(fieldName, language)

    if (language === 'ar') {
      switch (errorType) {
        case 'required':
          return `${displayName} مطلوب`
        case 'minLength':
          return `${displayName} يجب أن يكون على الأقل {min} أحرف`
        case 'maxLength':
          return `${displayName} يجب أن لا يتجاوز {max} حرف`
        case 'email':
          return `يرجى إدخال بريد إلكتروني صحيح`
        case 'phone':
          return `يرجى إدخال رقم هاتف صحيح`
        case 'number':
          return `${displayName} يجب أن يكون رقماً صحيحاً`
        case 'date':
          return `يرجى إدخال تاريخ صحيح`
        default:
          return `${displayName} غير صحيح`
      }
    } else {
      switch (errorType) {
        case 'required':
          return `${displayName} is required`
        case 'minLength':
          return `${displayName} must be at least {min} characters`
        case 'maxLength':
          return `${displayName} cannot exceed {max} characters`
        case 'email':
          return `Please enter a valid email address`
        case 'phone':
          return `Please enter a valid phone number`
        case 'number':
          return `${displayName} must be a valid number`
        case 'date':
          return `Please enter a valid date`
        default:
          return `${displayName} is invalid`
      }
    }
  }

  // Clear validation for a form
  clearValidation(formName: string): void {
    // This would be used to clear any cached validation state
    console.log(`Cleared validation for form: ${formName}`)
  }

  // Get all registered forms
  getRegisteredForms(): string[] {
    return Array.from(this.validationSchemas.keys())
  }
}

// Export singleton instance
export const formValidationManager = new FormValidationManager()

// VALIDATION FIX: Enhanced validation hook for React components
export function useFormValidation(
  formName: string,
  options: FormValidationOptions = {}
) {
  const validateForm = (data: Record<string, unknown>) => {
    return formValidationManager.validateForm(formName, data, options)
  }

  const validateField = (
    fieldName: string,
    value: unknown,
    allData: Record<string, unknown> = {}
  ) => {
    return formValidationManager.validateSingleField(
      formName,
      fieldName,
      value,
      allData,
      options.language
    )
  }

  const getErrorMessage = (
    fieldName: string,
    errorType: string,
    value: unknown,
    fieldConfig?: FormFieldConfig
  ) => {
    return formValidationManager.getErrorMessage(
      fieldName,
      errorType,
      value,
      options.language,
      fieldConfig
    )
  }

  return {
    validateForm,
    validateField,
    getErrorMessage,
    formValidationManager
  }
}

export default formValidationManager
