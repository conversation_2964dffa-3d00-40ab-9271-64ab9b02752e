import React from 'react';
/**
 * Production-Safe Logging Utility
 * Conditionally logs based on environment and configuration
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogConfig {
  enableInProduction: boolean;
  enabledLevels: LogLevel[];
  enabledModules: string[];
  maxLogEntries: number;
}

class Logger {
  private config: LogConfig;
  private logHistory: Array<{
    level: LogLevel;
    module: string;
    message: string;
    data?: any;
    timestamp: number;
  }> = [];

  constructor() {
    this.config = {
      enableInProduction: import.meta.env.VITE_ENABLE_LOGGING === 'true',
      enabledLevels: this.getEnabledLevels(),
      enabledModules: this.getEnabledModules(),
      maxLogEntries: 100
    };
  }

  private getEnabledLevels(): LogLevel[] {
    if (process.env.NODE_ENV === 'development') {
      return ['debug', 'info', 'warn', 'error'];
    }
    
    // In production, only log warnings and errors unless explicitly enabled
    if (this.config?.enableInProduction) {
      return ['info', 'warn', 'error'];
    }
    
    return ['error']; // Only errors in production by default
  }

  private getEnabledModules(): string[] {
    const enabledModules: any = import.meta.env.VITE_ENABLED_LOG_MODULES;
    if (enabledModules) {
      return enabledModules.split(',').map((m: string) => m.trim());
    }
    
    // Default enabled modules
    if (process.env.NODE_ENV === 'development') {
      return ['*']; // All modules in development
    }
    
    return ['error', 'auth', 'api']; // Critical modules only in production
  }

  private shouldLog(level: LogLevel, module: string): boolean {
    // Always log errors
    if (level === 'error') return true;
    
    // Check if level is enabled
    if (!this.config.enabledLevels.includes(level)) return false;
    
    // Check if module is enabled
    if (this.config.enabledModules.includes('*')) return true;
    if (this.config.enabledModules.includes(module)) return true;
    
    return false;
  }

  private addToHistory(level: LogLevel, module: string, message: string, data?: any) {
    this.logHistory.push({
      level,
      module,
      message,
      data,
      timestamp: Date.now()
    });

    // Keep only recent entries
    if (this.logHistory.length > this.config.maxLogEntries) {
      this.logHistory.splice(0, this.logHistory.length - this.config.maxLogEntries);
    }
  }

  private formatMessage(module: string, message: string): string {
    const timestamp: any = new Date().toISOString().split('T')[1].split('.')[0];
    return `[${timestamp}] [${module.toUpperCase()}] ${message}`;
  }

  debug(module: string, message: string, data?: any) {
    if (!this.shouldLog('debug', module)) return;
    
    this.addToHistory('debug', module, message, data);
    console.debug(this.formatMessage(module, message), data || '');
  }

  info(module: string, message: string, data?: any) {
    if (!this.shouldLog('info', module)) return;
    
    this.addToHistory('info', module, message, data);
    console.log(this.formatMessage(module, message), data || '');
  }

  warn(module: string, message: string, data?: any) {
    if (!this.shouldLog('warn', module)) return;
    
    this.addToHistory('warn', module, message, data);
    console.warn(this.formatMessage(module, message), data || '');
  }

  error(module: string, message: string, data?: any) {
    if (!this.shouldLog('error', module)) return;
    
    this.addToHistory('error', module, message, data);
    console.error(this.formatMessage(module, message), data || '');
  }

  // Group logging for related operations
  group(module: string, title: string) {
    if (!this.shouldLog('info', module)) return;
    console.group(this.formatMessage(module, title));
  }

  groupEnd() {
    if (process.env.NODE_ENV === 'development' || this.config.enableInProduction) {
      console.groupEnd();
    }
  }

  // Get log history for debugging
  getHistory(level?: LogLevel, module?: string) {
    let filtered: any = this.logHistory;
    
    if (level) {
      filtered = filtered.filter(entry => entry.level === level);
    }
    
    if (module) {
      filtered = filtered.filter(entry => entry.module === module);
    }
    
    return filtered;
  }

  // Clear log history
  clearHistory() {
    this.logHistory = [];
  }

  // Export logs for debugging
  exportLogs() {
    const logs: any = {
      config: this.config,
      history: this.logHistory,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      userAgent: navigator.userAgent
    };
    
    const blob: any = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
    const url: any = URL.createObjectURL(blob);
    const a: any = document.createElement('a');
    a.href = url;
    a.download = `app-logs-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }
}

// Create singleton instance
const logger: any = new Logger();

// Export convenience functions
export const log: any = {
  debug: (module: string, message: string, data?: any) => logger.debug(module, message, data),
  info: (module: string, message: string, data?: any) => logger.info(module, message, data),
  warn: (module: string, message: string, data?: any) => logger.warn(module, message, data),
  error: (module: string, message: string, data?: any) => logger.error(module, message, data),
  group: (module: string, title: string) => logger.group(module, title),
  groupEnd: () => logger.groupEnd(),
  getHistory: (level?: LogLevel, module?: string) => logger.getHistory(level, module),
  clearHistory: () => logger.clearHistory(),
  exportLogs: () => logger.exportLogs()
};

// Development helper
if (process.env.NODE_ENV === 'development') {
  window.appLogger = log;
  log.info('logger', 'Production-safe logger initialized');
  log.info('logger', 'Access via window.appLogger in development');
}

export default logger;
