import React from 'react';
/**
 * Authentication State Synchronization Utilities
 * Provides functions to manually sync Redux state with authentication tokens
 */

import { store } from '../store'
import { syncAuthState, syncTokenState } from '../store/slices/authSlice'
import { authService } from '../services/authService'

/**
 * Manually sync Redux authentication state with current tokens
 * Useful for emergency login, testing, or when tokens are set manually
 *
 * @returns Promise<boolean> - true if sync successful, false otherwise
 */
export async function syncReduxAuthState(): Promise<boolean> {
  try {
    console.log('🔄 Syncing Redux auth state via secure httpOnly cookies...')

    // Check current Redux state
    const currentState = store.getState().auth
    console.log('🔍 Current Redux auth state:', {
      isAuthenticated: currentState.isAuthenticated,
      hasUser: !!currentState.user,
      username: currentState.user?.username
    })

    // SECURITY MIGRATION: Use httpOnly cookies exclusively
    // We can't directly check httpOnly cookies, but we can verify via API call
    console.log('🔍 Verifying authentication via secure API call...')

    // Dispatch the sync action
    const result = await store.dispatch(syncAuthState())

    if (syncAuthState.fulfilled.match(result)) {
      console.log('✅ Redux auth state synced successfully via httpOnly cookies!')
      console.log('👤 User:', result.payload.username)
      console.log('🔐 Authentication method: Secure httpOnly cookies')
      return true
    } else {
      console.log('❌ Redux auth state sync failed:', result.payload)
      return false
    }
  } catch (error) {
    console.error('❌ Error syncing Redux auth state:', error)
    return false
  }
}

/**
 * Check if user is authenticated (either via cookies or localStorage)
 * @returns Promise<boolean>
 */
export async function checkAuthStatus(): Promise<boolean> {
  try {
    const result = await syncReduxAuthState()
    return result
  } catch (error) {
    console.error('Error checking auth status:', error)
    return false
  }
}

/**
 * Force sync Redux state when tokens are manually set
 * This is specifically for cases where tokens are set outside the normal login flow
 *
 * @param token - Optional token to verify before syncing
 * @returns Promise<boolean> - true if sync successful, false otherwise
 */
export async function forceTokenSync(token?: string): Promise<boolean> {
  try {
    console.log('🔧 Force syncing manually set tokens...')

    // If token provided, verify it first
    if (token) {
      console.log('🔍 Verifying provided token...')
      // Set the token in localStorage for verification
      localStorage.setItem('access_token', token)
    }

    // SECURITY MIGRATION: Use httpOnly cookies exclusively
    // We can't directly check httpOnly cookies, but we can attempt sync via API
    console.log('🔍 Attempting force sync via secure httpOnly cookies...')

    // Perform the sync
    const result = await syncReduxAuthState()

    if (result) {
      console.log('✅ Force token sync successful!')
    } else {
      console.log('❌ Force token sync failed')
    }

    return result
  } catch (error) {
    console.error('❌ Error in force token sync:', error)
    return false
  }
}

/**
 * Global function for browser console access
 * Makes it easy to sync auth state from browser console
 */
if (typeof window !== 'undefined') {
  // Make functions available globally for debugging
  window.syncAuth = syncReduxAuthState
  window.checkAuth = checkAuthStatus
  window.forceSync = forceTokenSync
  window.manualAuth = manualAuthenticate
  window.syncUser = syncUserToRedux

  console.log('🔧 Auth sync utilities available:')
  console.log('  - window.syncAuth() - Sync Redux state with current tokens')
  console.log('  - window.checkAuth() - Check current authentication status')
  console.log('  - window.forceSync(token?) - Force sync manually set tokens')
  console.log('  - window.manualAuth(token, refresh?) - Complete manual auth flow')
  console.log('  - window.syncUser(user) - Directly sync user object to Redux')
}

/**
 * Directly sync user data to Redux state
 * Use this when you have a valid user object and want to bypass API calls
 *
 * @param user - User object to sync to Redux
 */
export function syncUserToRedux(user: any): void {
  try {
    console.log('🔧 Directly syncing user to Redux state:', user.username)

    store.dispatch(syncTokenState({ user }))

    console.log('✅ User synced directly to Redux state')
  } catch (error) {
    console.error('❌ Error syncing user to Redux:', error)
  }
}

/**
 * Complete manual authentication flow
 * DEPRECATED: Manual token setting is no longer supported with httpOnly cookies
 * Use proper login flow instead for security
 *
 * @deprecated Use authService.login() instead for secure authentication
 * @returns Promise<boolean> - always false (deprecated)
 */
export async function manualAuthenticate(accessToken: string, refreshToken?: string): Promise<boolean> {
  console.warn('⚠️ manualAuthenticate is deprecated with httpOnly cookies')
  console.warn('🔒 Use authService.login() for secure authentication instead')
  console.warn('📚 Manual token setting is not supported for security reasons')

  // SECURITY MIGRATION: Manual token setting is not allowed with httpOnly cookies
  // This maintains API compatibility but prevents insecure token handling
  return false
}

export default {
  syncReduxAuthState,
  checkAuthStatus,
  forceTokenSync,
  syncUserToRedux,
  manualAuthenticate
}
