import React from 'react';
/**
 * UX FIX: Comprehensive UX Feedback Manager
 * Provides consistent user feedback across the application
 */

import { toast } from 'react-hot-toast'

interface useUXFeedbackProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


export interface FeedbackOptions {
  language?: 'ar' | 'en'
  duration?: number
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  showProgress?: boolean
  persistent?: boolean
}

export interface LoadingFeedback {
  id: string
  message: string
  startTime: number
  expectedDuration?: number
}

class UXFeedbackManager {
  private activeLoadings: Map<string, LoadingFeedback> = new Map()
  private defaultOptions: FeedbackOptions = {
    language: 'en',
    duration: 4000,
    position: 'top-center',
    showProgress: false,
    persistent: false
  }

  // UX FIX: Success feedback with better messaging
  success(message: string, options: FeedbackOptions = {}) {
    const opts: any = { ...this.defaultOptions, ...options }
    const { language } = opts

    const messages = {
      ar: {
        saved: 'تم الحفظ بنجاح',
        updated: 'تم التحديث بنجاح',
        deleted: 'تم الحذف بنجاح',
        created: 'تم الإنشاء بنجاح',
        sent: 'تم الإرسال بنجاح',
        uploaded: 'تم الرفع بنجاح',
        downloaded: 'تم التحميل بنجاح',
        copied: 'تم النسخ بنجاح',
        completed: 'تم الإكمال بنجاح'
      },
      en: {
        saved: 'Successfully saved',
        updated: 'Successfully updated',
        deleted: 'Successfully deleted',
        created: 'Successfully created',
        sent: 'Successfully sent',
        uploaded: 'Successfully uploaded',
        downloaded: 'Successfully downloaded',
        copied: 'Successfully copied',
        completed: 'Successfully completed'
      }
    }

    // Auto-translate common success messages
    const translatedMessage = this.translateMessage(message, messages[language!])

    toast.success(translatedMessage, {
      duration: opts.duration,
      position: opts.position,
      style: {
        background: 'rgba(34, 197, 94, 0.9)',
        color: 'white',
        border: '1px solid rgba(34, 197, 94, 0.3)',
        backdropFilter: 'blur(10px)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      },
      iconTheme: {
        primary: 'white',
        secondary: 'rgba(34, 197, 94, 0.9)'
      }
    })
  }

  // UX FIX: Error feedback with actionable messages
  error(message: string, options: FeedbackOptions & { action?: () => void; actionLabel?: string } = {}) {
    const opts: any = { ...this.defaultOptions, ...options }
    const { language, action, actionLabel } = opts

    const messages = {
      ar: {
        'network error': 'خطأ في الشبكة - تحقق من الاتصال',
        'server error': 'خطأ في الخادم - حاول مرة أخرى',
        'validation error': 'خطأ في التحقق - تحقق من البيانات',
        'permission denied': 'ليس لديك صلاحية للوصول',
        'not found': 'العنصر غير موجود',
        'timeout': 'انتهت مهلة الطلب - حاول مرة أخرى',
        'unauthorized': 'يجب تسجيل الدخول أولاً',
        'forbidden': 'غير مسموح بهذا الإجراء',
        'conflict': 'تعارض في البيانات',
        'too many requests': 'طلبات كثيرة - انتظر قليلاً'
      },
      en: {
        'network error': 'Network error - check your connection',
        'server error': 'Server error - please try again',
        'validation error': 'Validation error - check your data',
        'permission denied': 'You don\'t have permission to access this',
        'not found': 'Item not found',
        'timeout': 'Request timed out - please try again',
        'unauthorized': 'Please log in first',
        'forbidden': 'This action is not allowed',
        'conflict': 'Data conflict occurred',
        'too many requests': 'Too many requests - please wait'
      }
    }

    const translatedMessage = this.translateMessage(message, messages[language!])

    toast.error(translatedMessage, {
      duration: opts.persistent ? Infinity : opts.duration,
      position: opts.position,
      style: {
        background: 'rgba(239, 68, 68, 0.9)',
        color: 'white',
        border: '1px solid rgba(239, 68, 68, 0.3)',
        backdropFilter: 'blur(10px)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      },
      iconTheme: {
        primary: 'white',
        secondary: 'rgba(239, 68, 68, 0.9)'
      }
    })

    // Add action button if provided
    if (action && actionLabel) {
      setTimeout(() => {
        toast((t) => (
          <div className="flex items-center space-x-2">
            <span>{actionLabel}</span>
            <button
              onClick={() => {
                action()
                toast.dismiss(t.id)
              }}
              className="bg-white/20 hover:bg-white/30 px-2 py-1 rounded text-xs transition-colors"
            >
              {language === 'ar' ? 'حاول مرة أخرى' : 'Retry'}
            </button>
          </div>
        ), {
          duration: 8000,
          position: opts.position
        })
      }, 1000)
    }
  }

  // UX FIX: Loading feedback with progress tracking
  loading(message: string, options: FeedbackOptions & { expectedDuration?: number } = {}): string {
    const opts = { ...this.defaultOptions, ...options }
    const { language, expectedDuration } = opts

    const messages = {
      ar: {
        'loading': 'جاري التحميل...',
        'saving': 'جاري الحفظ...',
        'updating': 'جاري التحديث...',
        'deleting': 'جاري الحذف...',
        'creating': 'جاري الإنشاء...',
        'uploading': 'جاري الرفع...',
        'downloading': 'جاري التحميل...',
        'processing': 'جاري المعالجة...',
        'validating': 'جاري التحقق...',
        'connecting': 'جاري الاتصال...'
      },
      en: {
        'loading': 'Loading...',
        'saving': 'Saving...',
        'updating': 'Updating...',
        'deleting': 'Deleting...',
        'creating': 'Creating...',
        'uploading': 'Uploading...',
        'downloading': 'Downloading...',
        'processing': 'Processing...',
        'validating': 'Validating...',
        'connecting': 'Connecting...'
      }
    }

    const translatedMessage = this.translateMessage(message, messages[language!])
    const loadingId = `loading_${Date.now()}_${Math.random()}`

    // Store loading state
    this.activeLoadings.set(loadingId, {
      id: loadingId,
      message: translatedMessage,
      startTime: Date.now(),
      expectedDuration
    })

    const toastId = toast.loading(translatedMessage, {
      duration: Infinity,
      position: opts.position,
      style: {
        background: 'rgba(59, 130, 246, 0.9)',
        color: 'white',
        border: '1px solid rgba(59, 130, 246, 0.3)',
        backdropFilter: 'blur(10px)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      }
    })

    // Update progress if expected duration is provided
    if (expectedDuration && opts.showProgress) {
      this.updateLoadingProgress(loadingId, toastId, expectedDuration, translatedMessage)
    }

    return loadingId
  }

  // UX FIX: Update loading progress
  private updateLoadingProgress(loadingId: string, toastId: string, expectedDuration: number, baseMessage: string) {
    const interval = setInterval(() => {
      const loading = this.activeLoadings.get(loadingId)
      if (!loading) {
        clearInterval(interval)
        return
      }
      const elapsed = Date.now() - loading.startTime
      const progress = Math.min((elapsed / expectedDuration) * 100, 95) // Cap at 95%

      toast.loading(`${baseMessage} (${Math.round(progress)}%)`, {
        id: toastId
      })

      if (progress >= 95) {
        clearInterval(interval)
      }
    }, 500)
  }

  // UX FIX: Dismiss loading feedback
  dismissLoading(loadingId: string, result?: 'success' | 'error', message?: string) {
    const loading = this.activeLoadings.get(loadingId)
    if (!loading) return

    this.activeLoadings.delete(loadingId)
    toast.dismiss()

    if (result && message) {
      if (result === 'success') {
        this.success(message)
      } else {
        this.error(message)
      }
    }
  }

  // UX FIX: Warning feedback
  warning(message: string, options: FeedbackOptions = {}) {
    const opts = { ...this.defaultOptions, ...options }
    const { language } = opts

    const messages = {
      ar: {
        'unsaved changes': 'لديك تغييرات غير محفوظة',
        'slow connection': 'الاتصال بطيء',
        'large file': 'الملف كبير الحجم',
        'quota exceeded': 'تم تجاوز الحد المسموح',
        'deprecated': 'هذه الميزة قديمة',
        'beta feature': 'هذه ميزة تجريبية'
      },
      en: {
        'unsaved changes': 'You have unsaved changes',
        'slow connection': 'Slow connection detected',
        'large file': 'Large file detected',
        'quota exceeded': 'Quota exceeded',
        'deprecated': 'This feature is deprecated',
        'beta feature': 'This is a beta feature'
      }
    }

    const translatedMessage = this.translateMessage(message, messages[language!])

    toast(translatedMessage, {
      duration: opts.duration,
      position: opts.position,
      icon: '⚠️',
      style: {
        background: 'rgba(245, 158, 11, 0.9)',
        color: 'white',
        border: '1px solid rgba(245, 158, 11, 0.3)',
        backdropFilter: 'blur(10px)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      }
    })
  }

  // UX FIX: Info feedback
  info(message: string, options: FeedbackOptions = {}) {
    const opts = { ...this.defaultOptions, ...options }
    
    toast(message, {
      duration: opts.duration,
      position: opts.position,
      icon: 'ℹ️',
      style: {
        background: 'rgba(99, 102, 241, 0.9)',
        color: 'white',
        border: '1px solid rgba(99, 102, 241, 0.3)',
        backdropFilter: 'blur(10px)',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500'
      }
    })
  }

  // Helper method to translate common messages
  private translateMessage(message: string, translations: Record<string, string>): string {
    const lowerMessage = message.toLowerCase()
    
    for (const [key, translation] of Object.entries(translations)) {
      if (lowerMessage.includes(key)) {
        return translation
      }
    }
    
    return message
  }

  // UX FIX: Clear all feedback
  clearAll() {
    toast.dismiss()
    this.activeLoadings.clear()
  }

  // UX FIX: Get active loading states
  getActiveLoadings(): LoadingFeedback[] {
    return Array.from(this.activeLoadings.values())
  }

  // UX FIX: Set default options
  setDefaults(options: Partial<FeedbackOptions>) {
    this.defaultOptions = { ...this.defaultOptions, ...options }
  }
}

// Export singleton instance
export const uxFeedback = new UXFeedbackManager()

// UX FIX: React hook for UX feedback
export function useUXFeedback(language: 'ar' | 'en' = 'en'): void {
  const feedback = {
    success: (message: string, options?: FeedbackOptions) => 
      uxFeedback.success(message, { ...options, language }),
    
    error: (message: string, options?: FeedbackOptions & { action?: () => void; actionLabel?: string }) => 
      uxFeedback.error(message, { ...options, language }),
    
    loading: (message: string, options?: FeedbackOptions & { expectedDuration?: number }) => 
      uxFeedback.loading(message, { ...options, language }),
    
    warning: (message: string, options?: FeedbackOptions) => 
      uxFeedback.warning(message, { ...options, language }),
    
    info: (message: string, options?: FeedbackOptions) => 
      uxFeedback.info(message, { ...options, language }),
    
    dismissLoading: (loadingId: string, result?: 'success' | 'error', message?: string) =>
      uxFeedback.dismissLoading(loadingId, result, message),
    clearAll: () => uxFeedback.clearAll()
  }

  return feedback
}

export default uxFeedback
