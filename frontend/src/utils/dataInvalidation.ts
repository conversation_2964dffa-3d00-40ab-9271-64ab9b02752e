import React from 'react';
/**
 * Data Invalidation System
 * Manages cache invalidation and data synchronization across components
 */

import { store } from '../store'
import { AppDispatch } from '../store'
import { invalidateEmployeeCache, invalidateDepartmentCache, invalidateDashboardCache } from './apiCache'

// Event types for data invalidation
export type DataInvalidationEvent =
  | 'employee.created'
  | 'employee.updated'
  | 'employee.deleted'
  | 'department.created'
  | 'department.updated'
  | 'department.deleted'
  | 'user.created'
  | 'user.updated'
  | 'user.deleted'
  | 'message.created'
  | 'message.updated'
  | 'message.deleted'
  | 'risk.created'
  | 'risk.updated'
  | 'risk.deleted'
  | 'quality.created'
  | 'quality.updated'
  | 'quality.deleted'
  | 'dashboard.refresh'
  | 'search.invalidate'
  | 'notifications.refresh'

// Data invalidation listeners
type InvalidationListener = (event: DataInvalidationEvent, data?: any) => void

class DataInvalidationManager {
  private listeners: Map<DataInvalidationEvent, Set<InvalidationListener>> = new Map()
  private dispatch: AppDispatch

  constructor() {
    this.dispatch = store.dispatch
  }

  // Subscribe to data invalidation events
  subscribe(event: DataInvalidationEvent, listener: InvalidationListener): () => void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    
    this.listeners.get(event)!.add(listener)
    
    // Return unsubscribe function
    return () => {
      this.listeners.get(event)?.delete(listener)
    }
  }

  // Emit data invalidation event
  emit(event: DataInvalidationEvent, data?: any): void {
    const listeners = this.listeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event, data)
        } catch (error) {
          console.error(`Error in data invalidation listener for ${event}:`, error)
        }
      })
    }

    // Handle global invalidations
    this.handleGlobalInvalidations(event, data)
  }

  // Handle global data invalidations that affect multiple components
  private handleGlobalInvalidations(event: DataInvalidationEvent, data?: any): void {
    switch (event) {
      case 'employee.created':
      case 'employee.updated':
      case 'employee.deleted':
        // Invalidate API caches
        this.invalidateEmployeeCache(data?.id)
        // Invalidate dashboard employee metrics
        this.emit('dashboard.refresh', { section: 'employees' })
        // Invalidate search cache
        this.emit('search.invalidate', { type: 'employee' })
        break

      case 'department.created':
      case 'department.updated':
      case 'department.deleted':
        // Invalidate API caches
        this.invalidateDepartmentCache(data?.id)
        // Invalidate dashboard department metrics
        this.emit('dashboard.refresh', { section: 'departments' })
        // Invalidate search cache
        this.emit('search.invalidate', { type: 'department' })
        break

      case 'user.created':
      case 'user.updated':
      case 'user.deleted':
        // Invalidate dashboard user metrics
        this.emit('dashboard.refresh', { section: 'users' })
        // Invalidate search cache
        this.emit('search.invalidate', { type: 'user' })
        break

      case 'message.created':
        // Trigger notification refresh
        this.emit('notifications.refresh')
        // Update dashboard communication metrics
        this.emit('dashboard.refresh', { section: 'messages' })
        break

      case 'risk.created':
      case 'risk.updated':
      case 'risk.deleted':
        // Update dashboard risk metrics
        this.emit('dashboard.refresh', { section: 'risks' })
        break

      case 'quality.created':
      case 'quality.updated':
      case 'quality.deleted':
        // Update dashboard quality metrics
        this.emit('dashboard.refresh', { section: 'quality' })
        break
    }
  }

  // Clear all listeners (for cleanup)
  clear(): void {
    this.listeners.clear()
  }

  // Get active listeners count (for debugging)
  getListenerCount(): number {
    let count = 0
    this.listeners.forEach(listeners => {
      count += listeners.size
    })
    return count
  }

  // Cache invalidation methods
  private invalidateEmployeeCache(employeeId?: string): void {
    try {
      invalidateEmployeeCache(employeeId)
      console.log('🗑️ Employee cache invalidated:', employeeId || 'all')
    } catch (error) {
      console.error('Failed to invalidate employee cache:', error)
    }
  }

  private invalidateDepartmentCache(departmentId?: string): void {
    try {
      invalidateDepartmentCache(departmentId)
      console.log('🗑️ Department cache invalidated:', departmentId || 'all')
    } catch (error) {
      console.error('Failed to invalidate department cache:', error)
    }
  }
}

// Global instance
export const dataInvalidationManager = new DataInvalidationManager()

// React hook for data invalidation
export function useDataInvalidation() {
  const subscribe = (event: DataInvalidationEvent, listener: InvalidationListener => {
    return dataInvalidationManager.subscribe(event, listener)
  }

  const emit = (event: DataInvalidationEvent, data?: any) => {
    dataInvalidationManager.emit(event, data)
  }

  return { subscribe, emit }
}

// Enhanced CRUD operations with data invalidation
export class InvalidatingCrudService {
  private baseService: any
  private entityType: string

  constructor(baseService: any, entityType: string) {
    this.baseService = baseService
    this.entityType = entityType
  }

  async create(data: any) {
    const result = await this.baseService.create(data)
    
    // Emit invalidation event
    dataInvalidationManager.emit(`${this.entityType}.created` as DataInvalidationEvent, result)
    
    return result
  }

  async update(id: string | number, data: any) {
    const result = await this.baseService.update(id, data)
    
    // Emit invalidation event
    dataInvalidationManager.emit(`${this.entityType}.updated` as DataInvalidationEvent, { id, data: result })
    
    return result
  }

  async delete(id: string | number) {
    const result = await this.baseService.delete(id)
    
    // Emit invalidation event
    dataInvalidationManager.emit(`${this.entityType}.deleted` as DataInvalidationEvent, { id })
    
    return result
  }

  // Proxy other methods to base service
  async getAll(params?: any) {
    return this.baseService.getAll(params)
  }

  async getById(id: string | number) {
    return this.baseService.getById(id)
  }

  async export(format: string) {
    return this.baseService.export(format)
  }

  async import(file: File) {
    const result = await this.baseService.import(file)
    
    // Emit invalidation event for bulk import
    dataInvalidationManager.emit(`${this.entityType}.created` as DataInvalidationEvent, { bulk: true })
    
    return result
  }
}

// Cache invalidation utilities
export class CacheInvalidator {
  private static caches: Map<string, any> = new Map()

  static set(key: string, value: any, ttl: number = 300000): void { // 5 minutes default
    const expiry = Date.now() + ttl
    this.caches.set(key, { value, expiry })
  }

  static get(key: string): any | null {
    const cached = this.caches.get(key)
    if (!cached) return null

    if (Date.now() > cached.expiry) {
      this.caches.delete(key)
      return null
    }

    return cached.value
  }

  static invalidate(pattern: string): void {
    const keysToDelete: string[] = []
    
    this.caches.forEach((_, key) => {
      if (key.includes(pattern)) {
        keysToDelete.push(key)
      }
    })

    keysToDelete.forEach(key => this.caches.delete(key))
  }

  static clear(): void {
    this.caches.clear()
  }

  static size(): number {
    return this.caches.size
  }
}

// Optimistic update utilities
export class OptimisticUpdater {
  static async performOptimisticUpdate<T>(
    optimisticUpdate: () => void,
    actualUpdate: () => Promise<T>,
    rollback: () => void
  ): Promise<T> {
    // Apply optimistic update immediately
    optimisticUpdate()

    try {
      // Perform actual update
      const result = await actualUpdate()
      return result
    } catch (error) {
      // Rollback on error
      rollback()
      throw error
    }
  }
}

// Data synchronization utilities
export class DataSynchronizer {
  private static syncQueue: Array<() => Promise<void>> = []
  private static isProcessing = false

  static async queueSync(syncFn: () => Promise<void>): Promise<void> {
    this.syncQueue.push(syncFn)
    
    if (!this.isProcessing) {
      await this.processSyncQueue()
    }
  }

  private static async processSyncQueue(): Promise<void> {
    this.isProcessing = true

    while (this.syncQueue.length > 0) {
      const syncFn = this.syncQueue.shift()
      if (syncFn) {
        try {
          await syncFn()
        } catch (error) {
          console.error('Data sync error:', error)
        }
      }
    }

    this.isProcessing = false
  }

  static clearQueue(): void {
    this.syncQueue = []
  }

  static getQueueSize(): number {
    return this.syncQueue.length
  }
}

export default {
  dataInvalidationManager,
  useDataInvalidation,
  InvalidatingCrudService,
  CacheInvalidator,
  OptimisticUpdater,
  DataSynchronizer
}
