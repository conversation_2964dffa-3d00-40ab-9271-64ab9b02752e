/**
 * Test Helpers for Development and Debugging
 * Provides utilities to test functionality without full authentication
 */

// Mock user data for testing
export const mockSuperAdminUser: any = {
  id: 1,
  fullName: 'Test Super Admin',
  fullNameAr: 'مدير النظام التجريبي',
  email: '<EMAIL>',
  phone: '+1234567890',
  role: {
    id: 1,
    name: 'SUPERADMIN',
    nameAr: 'مدير النظام',
    permissions: ['all']
  },
  department: 'IT',
  departmentAr: 'تقنية المعلومات',
  position: 'System Administrator',
  positionAr: 'مدير النظام',
  status: 'active',
  joinDate: '2024-01-01',
  lastLogin: '2024-12-23T10:00:00Z',
  address: '123 Test Street',
  addressAr: '123 شارع التجربة',
  emergencyContact: '+1234567891',
  emergencyContactAr: '+1234567891',
  permissions: ['all'],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-12-23T10:00:00Z'
}

// Mock users data for testing
export const mockUsers = [
  {
    id: 1,
    fullName: 'John Doe',
    fullNameAr: 'جون دو',
    email: '<EMAIL>',
    phone: '+1234567890',
    role: 'ADMIN',
    roleAr: 'مدير',
    department: 'IT',
    departmentAr: 'تقنية المعلومات',
    position: 'System Admin',
    positionAr: 'مدير النظام',
    status: 'active',
    joinDate: '2024-01-15',
    lastLogin: '2024-12-23T09:30:00Z',
    address: '123 Admin Street',
    addressAr: '123 شارع الإدارة',
    emergencyContact: '+1234567891',
    emergencyContactAr: '+1234567891',
    permissions: ['user_management', 'system_settings'],
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-12-23T09:30:00Z'
  },
  {
    id: 2,
    fullName: 'Jane Smith',
    fullNameAr: 'جين سميث',
    email: '<EMAIL>',
    phone: '+1234567892',
    role: 'HR_MANAGER',
    roleAr: 'مدير الموارد البشرية',
    department: 'HR',
    departmentAr: 'الموارد البشرية',
    position: 'HR Manager',
    positionAr: 'مدير الموارد البشرية',
    status: 'active',
    joinDate: '2024-02-01',
    lastLogin: '2024-12-23T08:45:00Z',
    address: '456 HR Avenue',
    addressAr: '456 شارع الموارد البشرية',
    emergencyContact: '+1234567893',
    emergencyContactAr: '+1234567893',
    permissions: ['employee_management', 'payroll'],
    createdAt: '2024-02-01T00:00:00Z',
    updatedAt: '2024-12-23T08:45:00Z'
  },
  {
    id: 3,
    fullName: 'Ahmed Ali',
    fullNameAr: 'أحمد علي',
    email: '<EMAIL>',
    phone: '+1234567894',
    role: 'EMPLOYEE',
    roleAr: 'موظف',
    department: 'Sales',
    departmentAr: 'المبيعات',
    position: 'Sales Representative',
    positionAr: 'مندوب مبيعات',
    status: 'active',
    joinDate: '2024-03-01',
    lastLogin: '2024-12-22T16:20:00Z',
    address: '789 Sales Street',
    addressAr: '789 شارع المبيعات',
    emergencyContact: '+1234567895',
    emergencyContactAr: '+1234567895',
    permissions: ['basic_access'],
    createdAt: '2024-03-01T00:00:00Z',
    updatedAt: '2024-12-22T16:20:00Z'
  },
  {
    id: 4,
    fullName: 'Sarah Johnson',
    fullNameAr: 'سارة جونسون',
    email: '<EMAIL>',
    phone: '+1234567896',
    role: 'FINANCE_MANAGER',
    roleAr: 'مدير المالية',
    department: 'Finance',
    departmentAr: 'المالية',
    position: 'Finance Manager',
    positionAr: 'مدير المالية',
    status: 'inactive',
    joinDate: '2024-01-20',
    lastLogin: '2024-12-20T14:30:00Z',
    address: '321 Finance Road',
    addressAr: '321 طريق المالية',
    emergencyContact: '+1234567897',
    emergencyContactAr: '+1234567897',
    permissions: ['financial_reports', 'budget_management'],
    createdAt: '2024-01-20T00:00:00Z',
    updatedAt: '2024-12-20T14:30:00Z'
  }
]

// Test authentication state
export const mockAuthState = {
  user: mockSuperAdminUser,
  isAuthenticated: true,
  isLoading: false,
  error: null,
  loginAttempts: 0,
  lastLoginAttempt: null,
  csrfToken: 'mock-csrf-token',
  csrfTokenExpiry: Date.now() + (60 * 60 * 1000), // 1 hour from now
  isFetchingCsrf: false
}

// Mock API responses
export const mockApiResponses = {
  getUsers: () => Promise.resolve({ data: mockUsers, total: mockUsers.length }),
  createUser: (userData: any) => Promise.resolve({ ...userData, id: Date.now() }),
  updateUser: (id: number, userData: any) => Promise.resolve({ ...userData, id }),
  deleteUser: (id: number) => Promise.resolve({ success: true, id }),
  resetPassword: (id: number) => Promise.resolve({ success: true, message: 'Password reset email sent' })
}

// Development mode checker
export const isDevelopmentMode = () => {
  return process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost'
}

// Console debugging helpers
export const debugLog = (category: string, message: string, data?: any) => {
  if (isDevelopmentMode()) {
    console.log(`🐛 [${category.toUpperCase()}] ${message}`, data || '')
  }
}

export const debugError = (category: string, message: string, error?: any) => {
  if (isDevelopmentMode()) {
    console.error(`❌ [${category.toUpperCase()}] ${message}`, error || '')
  }
}

export const debugSuccess = (category: string, message: string, data?: any) => {
  if (isDevelopmentMode()) {
    console.log(`✅ [${category.toUpperCase()}] ${message}`, data || '')
  }
}

// Test mode enabler
export const enableTestMode = () => {
  if (isDevelopmentMode()) {
    // Store original console methods
    window.__originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn
    }
    
    // Enable enhanced logging
    console.log('🧪 Test mode enabled - Enhanced debugging active')
    
    // Add test data to window for debugging
    ;window.__testData = {
      mockUsers,
      mockSuperAdminUser,
      mockAuthState,
      mockApiResponses
    }
    
    return true
  }
  return false
}

// Disable test mode
export const disableTestMode = () => {
  if (isDevelopmentMode() && window.__originalConsole) {
    console.log('🧪 Test mode disabled')
    
    // Restore original console methods
    const original = window.__originalConsole
    console.log = original.log
    console.error = original.error
    console.warn = original.warn
    
    // Clean up test data
    delete window.__testData
    delete window.__originalConsole
    
    return true
  }
  return false
}

export default {
  mockSuperAdminUser,
  mockUsers,
  mockAuthState,
  mockApiResponses,
  isDevelopmentMode,
  debugLog,
  debugError,
  debugSuccess,
  enableTestMode,
  disableTestMode
}
