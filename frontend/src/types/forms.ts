import React from 'react';
import { ValidationRule } from './common'

export interface FormFieldConfig {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox' | 'file'
  required?: boolean
  placeholder?: string
  options?: Array<{ value: string | number; label: string }>
  validation?: ValidationRule[]
  disabled?: boolean
  multiple?: boolean
  accept?: string // for file inputs
}

export interface FormConfig {
  fields: FormFieldConfig[]
  submitLabel?: string
  cancelLabel?: string
  layout?: 'vertical' | 'horizontal' | 'grid'
  columns?: number
}

export interface FormData {
  [key: string]: any
}

export interface FormErrors {
  [key: string]: string
}

export interface FormState {
  data: FormData
  errors: FormErrors
  isSubmitting: boolean
  isDirty: boolean
  isValid: boolean
}

export interface FormProps {
  config: FormConfig
  initialData?: FormData
  onSubmit: (data: FormData => Promise<void> | void
  onCancel?: () => void
  loading?: boolean
  disabled?: boolean
}

// Specific form data types
export interface LoginFormData {
  username: string
  password: string
  remember_me?: boolean
}

export interface EmployeeFormData {
  first_name: string
  last_name: string
  first_name_ar?: string
  last_name_ar?: string
  email: string
  phone: string
  department: string
  position: string
  position_ar?: string
  salary: number
  hire_date: string
  address?: string
  emergency_contact?: string
  emergency_phone?: string
  national_id?: string
  gender?: 'M' | 'F'
  is_active?: boolean
}

export interface DepartmentFormData {
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  manager?: string
  budget?: number
  is_active?: boolean
}

export interface ProjectFormData {
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  start_date: string
  end_date?: string
  budget: number
  status: 'PLANNING' | 'ACTIVE' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  manager: string
  team_members?: string[]
}

export interface TaskFormData {
  title: string
  title_ar?: string
  description?: string
  description_ar?: string
  project: string
  assigned_to: string
  due_date?: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'DONE'
  estimated_hours?: number
}

export interface CustomerFormData {
  name: string
  name_ar?: string
  email?: string
  phone?: string
  address?: string
  address_ar?: string
  type: 'INDIVIDUAL' | 'COMPANY'
  status: 'ACTIVE' | 'INACTIVE'
  notes?: string
}

export interface ProductFormData {
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  category: string
  sku: string
  price: number
  cost: number
  stock_quantity: number
  min_stock_level: number
  unit: string
  unit_ar?: string
  is_active?: boolean
}
