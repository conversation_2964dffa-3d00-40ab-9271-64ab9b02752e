import React from 'react';
import { ReactNode } from 'react'

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  children: ReactNode
  onClick?: () => void
  type?: 'button' | 'submit' | 'reset'
  className?: string
}

export interface InputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'date' | 'tel' | 'url'
  placeholder?: string
  value?: string | number
  defaultValue?: string | number
  onChange?: (value: string) => void
  onBlur?: () => void
  onFocus?: () => void
  disabled?: boolean
  required?: boolean
  className?: string
  error?: string
  label?: string
  id?: string
  name?: string
}

export interface SelectProps {
  options: Array<{ value: string | number; label: string; disabled?: boolean }>
  value?: string | number
  defaultValue?: string | number
  onChange?: (value: string) => void
  placeholder?: string
  disabled?: boolean
  required?: boolean
  className?: string
  error?: string
  label?: string
  multiple?: boolean
  searchable?: boolean
}

export interface TextareaProps {
  placeholder?: string
  value?: string
  defaultValue?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  onFocus?: () => void
  disabled?: boolean
  required?: boolean
  className?: string
  error?: string
  label?: string
  rows?: number
  maxLength?: number
  resize?: 'none' | 'vertical' | 'horizontal' | 'both'
}

export interface CheckboxProps {
  checked?: boolean
  defaultChecked?: boolean
  onChange?: (checked: boolean) => void
  disabled?: boolean
  required?: boolean
  className?: string
  label?: string
  id?: string
  name?: string
}

export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closeOnOverlayClick?: boolean
  closeOnEscape?: boolean
  showCloseButton?: boolean
}

export interface ToastProps {
  type: 'success' | 'error' | 'warning' | 'info'
  title?: string
  message: string
  duration?: number
  onClose?: () => void
}

export interface LoaderProps {
  size?: 'sm' | 'md' | 'lg'
  color?: string
  className?: string
}

export interface BadgeProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
  children: ReactNode
  className?: string
}

export interface CardProps {
  children: ReactNode
  className?: string
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: 'none' | 'sm' | 'md' | 'lg'
  border?: boolean
}

export interface TabsProps {
  tabs: Array<{ id: string; label: string; content: ReactNode; disabled?: boolean }>
  activeTab?: string
  onChange?: (tabId: string) => void
  className?: string
  variant?: 'default' | 'pills' | 'underline'
}

export interface AccordionProps {
  items: Array<{
    id: string
    title: string
    content: ReactNode
    disabled?: boolean
    defaultOpen?: boolean
  }>
  allowMultiple?: boolean
  className?: string
}

export interface DropdownProps {
  trigger: ReactNode
  items: Array<{
    id: string
    label: string
    icon?: ReactNode
    onClick?: () => void
    disabled?: boolean
    separator?: boolean
  }>
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end'
  className?: string
}

export interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  showFirstLast?: boolean
  showPrevNext?: boolean
  maxVisiblePages?: number
  className?: string
}

export interface DataTableProps<T = any> {
  data: T[]
  columns: Array<{
    key: string
    label: string
    sortable?: boolean
    render?: (item: T => ReactNode
    width?: string
    align?: 'left' | 'center' | 'right'
  }>
  loading?: boolean
  pagination?: PaginationProps
  selection?: {
    enabled: boolean
    selectedItems: T[]
    onSelectionChange: (items: T[]) => void
  }
  actions?: Array<{
    label: string
    icon?: ReactNode
    onClick: (item: T => void
    disabled?: (item: T => boolean
  }>
  className?: string
}
