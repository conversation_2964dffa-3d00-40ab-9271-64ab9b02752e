import { useSelector } from 'react-redux'
import type { RootState } from '../store'

// Role-specific route components
import SuperAdminRoutes from './SuperAdminRoutes'
import HRManagerRoutes from './HRManagerRoutes'
import FinanceManagerRoutes from './FinanceManagerRoutes'
import DepartmentManagerRoutes from './DepartmentManagerRoutes'
import SalesManagerRoutes from './SalesManagerRoutes'
import EmployeeRoutes from './EmployeeRoutes'

interface RoleBasedRouterProps {
  language: 'ar' | 'en'
}

export default function RoleBasedRouter({ language }: RoleBasedRouterProps) {
  const { user } = useSelector((state: RootState => state.auth)

  if (!user) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-xl">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  // Route to appropriate component based on user role with proper prefixes
  switch (user.role.id) {
    case 'super_admin':
      return <SuperAdminRoutes language={language} />

    case 'hr_manager':
      return <HRManagerRoutes language={language} />

    case 'finance_manager':
      return <FinanceManagerRoutes language={language} />

    case 'department_manager':
      return <DepartmentManagerRoutes language={language} />

    case 'sales_manager':
      return <SalesManagerRoutes language={language} />

    case 'employee':
      return <EmployeeRoutes language={language} />

    default:
      // Default to employee routes for unknown roles
      return <EmployeeRoutes language={language} />
  }
}
