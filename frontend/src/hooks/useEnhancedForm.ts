import React from 'react';
/**
 * Enhanced Form Hook with Validation
 * Provides comprehensive form management with validation and API integration
 */

import { useState, useCallback, useEffect } from 'react'
import { useForm, UseFormReturn, FieldValues, DefaultValues, Path } from 'react-hook-form'
import { toast } from 'react-hot-toast'
import { ValidationSchema, validateForm } from '@/utils/validation'
import { loadingManager } from '@/services/enhancedAPI'

export interface UseEnhancedFormOptions<T extends FieldValues> {
  defaultValues?: DefaultValues<T>
  validationSchema?: ValidationSchema
  onSubmit?: (data: T => Promise<void>
  onSuccess?: (data: T => void
  onError?: (error: Error => void
  language?: 'ar' | 'en'
  resetOnSuccess?: boolean
}

export interface UseEnhancedFormReturn<T extends FieldValues> extends UseFormReturn<T> {
  isSubmitting: boolean
  submitError: string | null
  handleSubmit: (onSubmit: (data: T => Promise<void>) => (e?: React.BaseSyntheticEvent) => Promise<void>
  validateField: (fieldName: keyof T, value: unknown) => string | null
  validateAllFields: () => boolean
  resetForm: () => void
  setFieldError: (fieldName: keyof T, error: string) => void
  clearFieldError: (fieldName: keyof T) => void
  isFieldValid: (fieldName: keyof T) => boolean
  getFieldError: (fieldName: keyof T) => string | undefined
  loadingStates: Record<string, boolean>
}

export function useEnhancedForm<T extends FieldValues = FieldValues>({
  defaultValues,
  validationSchema,
  onSubmit,
  onSuccess,
  onError,
  language = 'en',
  resetOnSuccess = true
}: UseEnhancedFormOptions<T> = {}): UseEnhancedFormReturn<T> {
  
  const form = useForm<T>({
    defaultValues,
    mode: 'onChange'
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})

  // Subscribe to loading states
  useEffect(() => {
    const unsubscribe = loadingManager.subscribe(setLoadingStates)
    return unsubscribe as () => void
  }, [])

  // Validate individual field
  const validateField = useCallback((fieldName: keyof T, value: unknown): string | null => {
    if (!validationSchema || !validationSchema[fieldName as string]) {
      return null
    }

    const rules = validationSchema[fieldName as string]
    const fieldDisplayName = String(fieldName)

    // Required validation
    if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return language === 'ar'
        ? `${fieldDisplayName} مطلوب`
        : `${fieldDisplayName} is required`
    }

    // Skip other validations if field is empty and not required
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return null
    }

    // String validations
    if (typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        return language === 'ar'
          ? `${fieldDisplayName} يجب أن يكون على الأقل ${rules.minLength} أحرف`
          : `${fieldDisplayName} must be at least ${rules.minLength} characters`
      }

      if (rules.maxLength && value.length > rules.maxLength) {
        return language === 'ar'
          ? `${fieldDisplayName} يجب ألا يتجاوز ${rules.maxLength} أحرف`
          : `${fieldDisplayName} must not exceed ${rules.maxLength} characters`
      }

      if (rules.pattern && !rules.pattern.test(value)) {
        return language === 'ar'
          ? `تنسيق ${fieldDisplayName} غير صحيح`
          : `${fieldDisplayName} format is invalid`
      }
    }

    // Custom validation
    if (rules.custom) {
      return rules.custom(value)
    }

    return null
  }, [validationSchema, language])

  // Validate all fields
  const validateAllFields = useCallback((): boolean => {
    if (!validationSchema) return true
    const formData = form.getValues()
    const errors = validateForm(formData, validationSchema)
    
    // Set errors in react-hook-form
    Object.keyserrors.forEach(fieldName => {
      form.setError(fieldName as Path<T>, {
        type: 'validation',
        message: errors[fieldName]
      })
    })

    return Object.keyserrors.length === 0
  }, [form, validationSchema])

  // Enhanced submit handler
  const handleSubmit = useCallback((submitFn: (data: T => Promise<void>) => {
    return async (e?: React.BaseSyntheticEvent) => {
      if (e) {
        e.preventDefault()
        e.stopPropagation()
      }

      try {
        setIsSubmitting(true)
        setSubmitError(null)

        // Validate form
        const isValid = await form.trigger()
        if (!isValid) {
          const firstError = Object.keys(form.formState.errors)[0]
          if (firstError) {
            const errorMessage = form.formState.errors[firstError]?.message
            if (errorMessage) {
              toast.error(String(errorMessage))
            }
          }
          return
        }

        // Additional custom validation
        if (!validateAllFields()) {
          return
        }
        const formData = form.getValues()
        
        // Execute submit function
        await submitFn(formData)
        
        // Success handling
        if (onSuccess) {
          onSuccess(formData)
        }

        if (resetOnSuccess) {
          form.reset()
        }

        toast.success(language === 'ar' 
            ? 'تم الحفظ بنجاح' 
            : 'Saved successfully'} catch (error: unknown) {
        console.error('Form submission error:', error)
        
        const errorMessage = (error)?.message || (
          language === 'ar'
            ? 'حدث خطأ أثناء الحفظ'
            : 'An error occurred while saving'
        )

        setSubmitError(errorMessage)
        toast.error(errorMessage)

        // Handle validation errors from server
        if ((error)?.details?.errors) {
          Object.keys(error.details.errors).forEach(fieldName => {
            const fieldErrors = error.details.errors[fieldName]
            if (Array.isArray(fieldErrors) && fieldErrors.length > 0) {
              form.setError(fieldName, {
                type: 'server',
                message: fieldErrors[0]
              })
            }
          })
        }

        if (onError) {
          onError(error as Error)
        }
      } finally {
        setIsSubmitting(false)
      }
    }
  }, [form, validateAllFields, onSuccess, onError, language, resetOnSuccess])

  // Reset form
  const resetForm = useCallback(() => {
    form.reset(defaultValues)
    setSubmitError(null)
  }, [form, defaultValues])

  // Set field error
  const setFieldError = useCallback((fieldName: keyof T, error: string) => {
    form.setError(fieldName, {
      type: 'manual',
      message: error
    })
  }, [form])

  // Clear field error
  const clearFieldError = useCallback((fieldName: keyof T) => {
    form.clearErrors(fieldName)
  }, [form])

  // Check if field is valid
  const isFieldValid = useCallback((fieldName: keyof T): boolean => {
    return !form.formState.errors[fieldName]
  }, [form.formState.errors])

  // Get field error
  const getFieldError = useCallback((fieldName: keyof T): string | undefined => {
    return form.formState.errors[fieldName]?.message as string | undefined
  }, [form.formState.errors])

  // Watch for field changes and validate - TEMPORARILY DISABLED TO FIX INFINITE LOOP
  // useEffect(() => {
  //   if (!validationSchema) return

  //   const subscription = form.watch((value, { name }) => {
  //     if (name && validationSchema[name]) {
  //       const error = validateField(name as keyof T, value[name])
  //       if (error) {
  //         form.setError(name, {
  //           type: 'validation',
  //           message: error
  //         })
  //       } else {
  //         form.clearErrors(name)
  //       }
  //     }
  //   })

  //   return () => subscription.unsubscribe()
  // }, [form, validationSchema])

  return {
    ...form,
    isSubmitting,
    submitError,
    handleSubmit,
    validateField,
    validateAllFields,
    resetForm,
    setFieldError,
    clearFieldError,
    isFieldValid,
    getFieldError,
    loadingStates
  }
}

export default useEnhancedForm
