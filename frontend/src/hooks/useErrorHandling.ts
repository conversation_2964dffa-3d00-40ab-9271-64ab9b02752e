import React from 'react';
/**
 * ERROR HANDLING FIX: Comprehensive Error Handling Hook
 * Provides unified error handling for React components
 */

import { useState, useCallback, useEffect, useRef } from 'react'
import { useErrorHandler } from '@/utils/comprehensiveErrorHandler'
import { useUXFeedback } from '@/utils/uxFeedbackManager'

export interface ErrorState {
  hasError: boolean
  error: Error | null
  errorId: string | null
  retryCount: number
  isRetrying: boolean
}

export interface ErrorHandlingOptions {
  language?: 'ar' | 'en'
  maxRetries?: number
  retryDelay?: number
  showToast?: boolean
  logErrors?: boolean
  component?: string
}

export interface AsyncErrorOptions extends ErrorHandlingOptions {
  loadingMessage?: string
  successMessage?: string
  errorMessage?: string
}

export function useErrorHandling(options: ErrorHandlingOptions = {}) {
  const {
    language = 'en',
    maxRetries = 3,
    retryDelay = 1000,
    showToast = true,
    logErrors = true,
    component = 'Unknown'
  } = options

  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    error: null,
    errorId: null,
    retryCount: 0,
    isRetrying: false
  })

  const { handleError: handleErrorComprehensive } = useErrorHandler(language)
  const uxFeedback = useUXFeedback(language)
  const retryTimeoutRef = useRef<NodeJS.Timeout>()

  // ERROR FIX: Clear error state
  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
      errorId: null,
      retryCount: 0,
      isRetrying: false
    })

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
    }
  }, [])

  // ERROR FIX: Handle error with comprehensive processing
  const handleError = useCallback((
    error: unknown,
    context: string = 'unknown',
    customOptions: Partial<ErrorHandlingOptions> = {}) => {
    const mergedOptions = { ...options, ...customOptions }
    
    const processedError = handleErrorComprehensive(error, {
      component,
      action: context,
      additionalData: {
        retryCount: errorState.retryCount,
        timestamp: new Date().toISOString()
      }
    }, {
      language: mergedOptions.language,
      showToast: mergedOptions.showToast,
      logToConsole: mergedOptions.logErrors
    })

    setErrorState(prev => ({
      hasError: true,
      error: error instanceof Error ? error : new Error(String(error)),
      errorId: processedError.code,
      retryCount: prev.retryCount,
      isRetrying: false
    }))

    return processedError
  }, [component, errorState.retryCount, handleErrorComprehensive, options])

  // ERROR FIX: Retry with exponential backoff
  const retry = useCallback(async (
    retryFn: () => Promise<any> | any,
    context: string = 'retry'
  ) => {
    if (errorState.retryCount >= maxRetries) {
      uxFeedback.error(language === 'ar' 
          ? 'تم تجاوز الحد الأقصى لعدد المحاولات' 
          : 'Maximum retry attempts exceeded'
      return
    }

    setErrorState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1
    }))

    const delay = retryDelay * Math.pow(2, errorState.retryCount) // Exponential backoff
    retryTimeoutRef.current = setTimeout(async () => {
      try {
        const result = await retryFn()
        clearError()
        
        uxFeedback.success(language === 'ar' 
            ? 'تم حل المشكلة بنجاح' 
            : 'Issue resolved successfully'
        
        return result
      } catch (retryError) {
        handleError(retryError, `${context}_retry_${errorState.retryCount + 1}`)
      }
    }, delay)
  }, [errorState.retryCount, maxRetries, retryDelay, language, uxFeedback, clearError, handleError])

  // ERROR FIX: Async operation wrapper with error handling
  const withErrorHandling = useCallback(<T>(
    asyncFn: () => Promise<T>,
    context: string = 'async_operation',
    asyncOptions: AsyncErrorOptions = {}
  ) => {
    return async (): Promise<T | null> => {
      const {
        loadingMessage,
        successMessage,
        errorMessage
      } = asyncOptions

      let loadingId: string | undefined

      try {
        // Clear previous errors
        clearError()

        // Show loading feedback
        if (loadingMessage) {
          loadingId = uxFeedback.loading(loadingMessage)
        }
        const result = await asyncFn()

        // Show success feedback
        if (successMessage) {
          if (loadingId) {
            uxFeedback.dismissLoading(loadingId, 'success', successMessage)
          } else {
            uxFeedback.success(successMessage)
          }
        } else if (loadingId) {
          uxFeedback.dismissLoading(loadingId)
        }

        return result
      } catch (error) {
        // Handle error
        const processedError = handleError(error, context, asyncOptions)

        // Show error feedback
        const finalErrorMessage = errorMessage || processedError.userMessage
        if (loadingId) {
          uxFeedback.dismissLoading(loadingId, 'error', finalErrorMessage)
        }

        return null
      }
    }
  }, [clearError, uxFeedback, handleError])

  // ERROR FIX: Safe async execution with automatic retry
  const safeAsync = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    context: string = 'safe_async',
    autoRetry: boolean = false
  ): Promise<T | null> => {
    try {
      clearError()
      return await asyncFn()
    } catch (error) {
      const processedError = handleError(error, context)
      
      // Auto retry for retryable errors
      if (autoRetry && processedError.retryable && errorState.retryCount < maxRetries) {
        return retry(asyncFn, context)
      }
      
      return null
    }
  }, [clearError, handleError, errorState.retryCount, maxRetries, retry])

  // ERROR FIX: Form submission with error handling
  const handleFormSubmit = useCallback(<T>(
    submitFn: () => Promise<T>,
    options: {
      successMessage?: string
      errorMessage?: string
      onSuccess?: (result: T => void
      onError?: (error: any) => void
    } = {}
  ) => {
    return withErrorHandling(
      async () => {
        const result = await submitFn()
        options.onSuccess?.(result)
        return result
      },
      'form_submit',
      {
        loadingMessage: language === 'ar' ? 'جاري الحفظ...' : 'Saving...',
        successMessage: options.successMessage || (language === 'ar' ? 'تم الحفظ بنجاح' : 'Saved successfully'),
        errorMessage: options.errorMessage
      }
    )
  }, [withErrorHandling, language])

  // ERROR FIX: Data fetching with error handling
  const handleDataFetch = useCallback(<T>(
    fetchFn: () => Promise<T>,
    options: {
      dataType?: string
      silent?: boolean
      onSuccess?: (data: T => void
      onError?: (error: any) => void
    } = {}
  ) => {
    const { dataType = 'data', silent = false } = options

    return withErrorHandling(
      async () => {
        const data = await fetchFn()
        options.onSuccess?.(data)
        return data
      },
      'data_fetch',
      {
        loadingMessage: silent ? undefined : (
          language === 'ar' ? `جاري تحميل ${dataType}...` : `Loading ${dataType}...`
        ),
        errorMessage: language === 'ar' 
          ? `فشل في تحميل ${dataType}` 
          : `Failed to load ${dataType}`
      }
    )
  }, [withErrorHandling, language])

  // ERROR FIX: Cleanup on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [])

  return {
    // State
    errorState,
    hasError: errorState.hasError,
    error: errorState.error,
    errorId: errorState.errorId,
    retryCount: errorState.retryCount,
    isRetrying: errorState.isRetrying,

    // Actions
    handleError,
    clearError,
    retry,

    // Wrappers
    withErrorHandling,
    safeAsync,
    handleFormSubmit,
    handleDataFetch,

    // Utilities
    canRetry: errorState.retryCount < maxRetries,
    isRetryable: (error: any) => {
      // Check if error is retryable based on type
      if (error?.code?.includes('network') || error?.code?.includes('timeout')) {
        return true
      }
      if (error?.status >= 500) {
        return true
      }
      return false
    }
  }
}

// ERROR FIX: Specialized hooks for common use cases
export function useFormErrorHandling(language: 'ar' | 'en' = 'en') {
  return useErrorHandling({
    language,
    component: 'Form',
    maxRetries: 2,
    showToast: true
  })
}

export function useApiErrorHandling(language: 'ar' | 'en' = 'en') {
  return useErrorHandling({
    language,
    component: 'API',
    maxRetries: 3,
    retryDelay: 2000,
    showToast: true
  })
}

export function useDataErrorHandling(language: 'ar' | 'en' = 'en') {
  return useErrorHandling({
    language,
    component: 'Data',
    maxRetries: 2,
    showToast: false, // Usually handled by loading states
    logErrors: true
  })
}

export default useErrorHandling
