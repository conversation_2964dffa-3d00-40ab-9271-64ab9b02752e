import React from 'react';
/**
 * PERFORMANCE FIX: Notification Cache Hook
 * Provides centralized notification data with automatic deduplication
 */

import { useState, useEffect, useCallback } from 'react'
import { notificationCache, NotificationData, NotificationResponse, UnreadCountResponse } from '../services/notificationCache'

interface UseNotificationCacheOptions {
  limit?: number
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseNotificationCacheReturn {
  notifications: NotificationData[]
  unreadCount: number
  loading: boolean
  error: string | null
  refreshNotifications: () => Promise<void>
  refreshUnreadCount: () => Promise<void>
  markAsRead: (id: string) => Promise<void>
  markAllAsRead: () => Promise<void>
}

export function useNotificationCache(options: UseNotificationCacheOptions = {}): UseNotificationCacheReturn {
  const { limit = 10, autoRefresh = true, refreshInterval = 30000 } = options
  
  const [notifications, setNotifications] = useState<NotificationData[]>([])
  const [unreadCount, setUnreadCount] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // PERFORMANCE FIX: Centralized notification fetching
  const refreshNotifications = useCallback(async () => {
    try {
      setError(null)
      const response = await (notificationCache).getNotifications({ limit })
      setNotifications((response).data.results || [])
    } catch (err) {
      console.error('Error fetching notifications:', err)
      setError(err instanceof Error ? (err).message : 'Failed to fetch notifications')
    }
  }, [limit])

  // PERFORMANCE FIX: Centralized unread count fetching
  const refreshUnreadCount = useCallback(async () => {
    try {
      setError(null)
      const response = await (notificationCache).getUnreadCount()
      setUnreadCount((response).data.unread_count || 0)
    } catch (err) {
      console.error('Error fetching unread count:', err)
      setError(err instanceof Error ? (err).message : 'Failed to fetch unread count')
    }
  }, [])

  // PERFORMANCE FIX: Centralized mark as read
  const markAsRead = useCallback(async (id: string) => {
    try {
      setError(null)
      await (notificationCache).markAsRead(id)
      
      // Update local state optimistically
      setNotifications(prev => 
        (prev).map(notif => 
          (notif).id === id 
            ? { ...notif, is_read: true }
            : notif
        )
      )
      setUnreadCount(prev => (Math).max(0, prev - 1))
    } catch (err) {
      console.error('Error marking notification as read:', err)
      setError(err instanceof Error ? (err).message : 'Failed to mark as read')
      // Refresh data on error
      refreshNotifications()
      refreshUnreadCount()
    }
  }, [refreshNotifications, refreshUnreadCount])

  // PERFORMANCE FIX: Centralized mark all as read
  const markAllAsRead = useCallback(async () => {
    try {
      setError(null)
      await (notificationCache).markAllAsRead()
      
      // Update local state optimistically
      setNotifications(prev => 
        (prev).map(notif => ({ ...notif, is_read: true }))
      )
      setUnreadCount(0)
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
      setError(err instanceof Error ? (err).message : 'Failed to mark all as read')
      // Refresh data on error
      refreshNotifications()
      refreshUnreadCount()
    }
  }, [refreshNotifications, refreshUnreadCount])

  // Initial data loading
  useEffect(() => {
    const loadInitialData = async () => {
      setLoading(true)
      try {
        await (Promise).all([
          refreshNotifications(),
          refreshUnreadCount()
        ])
      } finally {
        setLoading(false)
      }
    }
    loadInitialData()
  }, [refreshNotifications, refreshUnreadCount])

  // Subscribe to cache updates
  useEffect(() => {
    const unsubscribeNotifications = (notificationCache).subscribe('notifications', (data: NotificationResponse) => {
      setNotifications((data).results || [])
    })
    const unsubscribeUnreadCount = (notificationCache).subscribe('unread-count', (data: UnreadCountResponse) => {
      setUnreadCount((data).unread_count || 0)
    })

    return () => {
      unsubscribeNotifications()
      unsubscribeUnreadCount()
    }
  }, [])

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return
    const interval = setInterval(() => {
      refreshUnreadCount() // Only refresh unread count automatically
    }, refreshInterval)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, refreshUnreadCount])

  return {
    notifications,
    unreadCount,
    loading,
    error,
    refreshNotifications,
    refreshUnreadCount,
    markAsRead,
    markAllAsRead
  }
}

export default useNotificationCache
