import React from 'react';
/**
 * React hook for CSRF token management via Redux
 * Prevents conflicts between CSRF fetching and Redux state
 */

import { useSelector, useDispatch } from 'react-redux'
import { useCallback, useEffect } from 'react'
import type { RootState, AppDispatch } from '../store'
import { fetchCSRFToken, clearCSRFToken } from '../store/slices/authSlice'

export function useCSRF() {
  const dispatch = useDispatch<AppDispatch>()

  const {
    csrfToken,
    csrfTokenExpiry,
    isFetchingCsrf,
    isAuthenticated
  } = useSelector((state: RootState => state.auth)

  // Check if token is valid
  const isTokenValid = useCallback(() => {
    return csrfToken && Date.now() < csrfTokenExpiry
  }, [csrfToken, csrfTokenExpiry])

  // Ensure CSRF token is available
  const ensureToken = useCallback(async () => {
    if (!isAuthenticated) {
      return null
    }

    if (isTokenValid()) {
      return csrfToken
    }

    if (!isFetchingCsrf) {
      const result = await dispatch(fetchCSRFToken())
      if (fetchCSRFToken.fulfilled.match(result)) {
        return result.payload
      }
    }

    return csrfToken
  }, [dispatch, csrfToken, isTokenValid, isFetchingCsrf, isAuthenticated])

  // Clear token
  const clearToken = useCallback(() => {
    dispatch(clearCSRFToken())
  }, [dispatch])

  // Get headers with CSRF token
  const getHeaders = useCallback((): HeadersInit => {
    if (!isAuthenticated || !isTokenValid()) {
      return {}
    }

    return {
      'X-CSRFToken': csrfToken!
    }
  }, [csrfToken, isTokenValid, isAuthenticated])

  return {
    csrfToken,
    isTokenValid: isTokenValid(),
    isFetching: isFetchingCsrf,
    ensureToken,
    clearToken,
    getHeaders
  }
}
