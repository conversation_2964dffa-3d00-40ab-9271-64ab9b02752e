import React from 'react';
/**
 * Notification Synchronization Hook
 * Keeps notifications in sync between WebSocket updates and Redux state
 */

import { useEffect, useCallback, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { RootState, AppDispatch } from '../store'
import { addNotification, markAsRead, removeNotification } from '../store/slices/notificationSlice'
import { useDataInvalidation } from '../utils/dataInvalidation'

interface NotificationToast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
}

export function useNotificationSync() {
  const dispatch = useDispatch<AppDispatch>()
  const { subscribe } = useDataInvalidation()
  const notifications = useSelector((state: RootState => state.notifications)
  const [toasts, setToasts] = useState<NotificationToast[]>([])

  // Show toast notification
  const showToast = useCallback((toast: NotificationToast => {
    setToasts(prev => [...prev, toast])
    
    // Auto-remove toast after duration
    const duration = toast.duration || 5000
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t.id !== toast.id))
    }, duration)
  }, [])

  // Remove toast
  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id))
  }, [])

  // Handle data change notifications
  const handleDataChangeNotification = useCallback((event: string, data: any) => {
    let title = ''
    let message = ''
    let type: 'success' | 'info' = 'success'

    switch (event) {
      case 'employee.created':
        title = 'Employee Created'
        message = 'New employee has been added successfully'
        break
      case 'employee.updated':
        title = 'Employee Updated'
        message = 'Employee information has been updated'
        break
      case 'employee.deleted':
        title = 'Employee Deleted'
        message = 'Employee has been removed'
        break
      case 'user.created':
        title = 'User Created'
        message = 'New user account has been created'
        break
      case 'user.updated':
        title = 'User Updated'
        message = 'User information has been updated'
        break
      case 'user.deleted':
        title = 'User Deleted'
        message = 'User account has been removed'
        break
      case 'message.created':
        title = 'New Message'
        message = 'A new message has been sent'
        type = 'info'
        break
      case 'risk.created':
        title = 'Risk Identified'
        message = 'New risk has been registered'
        type = 'info'
        break
      case 'quality.created':
        title = 'Quality Record Added'
        message = 'New quality record has been created'
        break
      default:
        return // Don't show notification for unknown events
    }

    // Create notification for Redux store
    const notification = {
      id: `${event}-${Date.now()}`,
      type: type,
      title,
      titleAr: title, // TODO: Add Arabic translations
      message,
      messageAr: message, // TODO: Add Arabic translations
      timestamp: new Date().toISOString(),
      isRead: false,
      isStarred: false,
      actionRequired: false,
      category: 'system' as const,
      priority: 'medium' as const,
      metadata: data
    }

    // Add to Redux store
    dispatch(addNotification(notification))

    // Show toast
    showToast({
      id: notification.id,
      type,
      title,
      message,
      duration: 3000
    })
  }, [dispatch, showToast])

  // Subscribe to data invalidation events for notifications
  useEffect(() => {
    const unsubscribers = [
      subscribe('employee.created', (event, data) => handleDataChangeNotification('employee.created', data)),
      subscribe('employee.updated', (event, data) => handleDataChangeNotification('employee.updated', data)),
      subscribe('employee.deleted', (event, data) => handleDataChangeNotification('employee.deleted', data)),
      subscribe('user.created', (event, data) => handleDataChangeNotification('user.created', data)),
      subscribe('user.updated', (event, data) => handleDataChangeNotification('user.updated', data)),
      subscribe('user.deleted', (event, data) => handleDataChangeNotification('user.deleted', data)),
      subscribe('message.created', (event, data) => handleDataChangeNotification('message.created', data)),
      subscribe('risk.created', (event, data) => handleDataChangeNotification('risk.created', data)),
      subscribe('quality.created', (event, data) => handleDataChangeNotification('quality.created', data)),
    ]

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe())
    }
  }, [subscribe, handleDataChangeNotification])

  // Mark notification as read
  const markNotificationAsRead = useCallback((id: string) => {
    dispatch(markAsRead(id))
  }, [dispatch])

  // Remove notification
  const removeNotificationById = useCallback((id: string) => {
    dispatch(removeNotification(id))
  }, [dispatch])

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    notifications.notifications.forEach(notification => {
      if (!notification.isRead) {
        dispatch(markAsRead(notification.id))
      }
    })
  }, [dispatch, notifications.notifications])

  return {
    notifications: notifications.notifications,
    unreadCount: notifications.unreadCount,
    toasts,
    showToast,
    removeToast,
    markNotificationAsRead,
    removeNotificationById,
    markAllAsRead
  }
}

export default useNotificationSync
