import React from 'react';
/**
 * Dashboard Data Synchronization Hook
 * Keeps dashboard widgets in sync with data changes across the application
 */

import { useEffect, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { RootState, AppDispatch } from '../store'
import { useDataInvalidation } from '../utils/dataInvalidation'

export function useDashboardSync() {
  const dispatch = useDispatch<AppDispatch>()
  const { subscribe } = useDataInvalidation()
  const dashboardState = useSelector((state: RootState => state.dashboard)

  // Refresh dashboard metrics when data changes
  const refreshDashboardMetrics = useCallback(async (section?: string) => {
    console.log(`🔄 Refreshing dashboard metrics${section ? ` for ${section}` : ''}`)
    
    // Here you would dispatch actions to refresh specific dashboard widgets
    // For now, we'll just log the refresh event
    
    // Example of what this would look like:
    // if (section === 'employees') {
    //   dispatch(refreshEmployeeMetrics())
    // } else if (section === 'users') {
    //   dispatch(refreshUserMetrics())
    // } else {
    //   dispatch(refreshAllMetrics())
    // }
  }, [dispatch])

  // Subscribe to data invalidation events
  useEffect(() => {
    const unsubscribers = [
      // Employee data changes
      subscribe('employee.created', () => refreshDashboardMetrics('employees')),
      subscribe('employee.updated', () => refreshDashboardMetrics('employees')),
      subscribe('employee.deleted', () => refreshDashboardMetrics('employees')),

      // User data changes
      subscribe('user.created', () => refreshDashboardMetrics('users')),
      subscribe('user.updated', () => refreshDashboardMetrics('users')),
      subscribe('user.deleted', () => refreshDashboardMetrics('users')),

      // Message data changes
      subscribe('message.created', () => refreshDashboardMetrics('messages')),
      subscribe('message.updated', () => refreshDashboardMetrics('messages')),
      subscribe('message.deleted', () => refreshDashboardMetrics('messages')),

      // Risk data changes
      subscribe('risk.created', () => refreshDashboardMetrics('risks')),
      subscribe('risk.updated', () => refreshDashboardMetrics('risks')),
      subscribe('risk.deleted', () => refreshDashboardMetrics('risks')),

      // Quality data changes
      subscribe('quality.created', () => refreshDashboardMetrics('quality')),
      subscribe('quality.updated', () => refreshDashboardMetrics('quality')),
      subscribe('quality.deleted', () => refreshDashboardMetrics('quality')),

      // General dashboard refresh
      subscribe('dashboard.refresh', (event, data) => {
        refreshDashboardMetrics(data?.section)
      })
    ]

    // Cleanup subscriptions
    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe())
    }
  }, [subscribe, refreshDashboardMetrics])

  return {
    refreshDashboardMetrics,
    dashboardState
  }
}

export default useDashboardSync
