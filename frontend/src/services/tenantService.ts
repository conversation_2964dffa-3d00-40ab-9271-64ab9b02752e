import React from 'react';
/**
 * Multi-Tenant Architecture Service
 * Handles tenant management, isolation, and configuration
 */

import { apiClient } from './api'

export interface Tenant {
  id: string
  name: string
  domain: string
  subdomain: string
  plan: 'basic' | 'professional' | 'enterprise' | 'custom'
  status: 'active' | 'suspended' | 'trial' | 'expired'
  settings: TenantSettings
  limits: TenantLimits
  features: TenantFeatures
  billing: TenantBilling
  createdAt: Date
  updatedAt: Date
  metadata: Record<string, any>
}

export interface TenantSettings {
  branding: {
    logo?: string
    primaryColor: string
    secondaryColor: string
    favicon?: string
    companyName: string
  }
  localization: {
    defaultLanguage: 'ar' | 'en'
    timezone: string
    dateFormat: string
    currency: string
  }
  security: {
    passwordPolicy: PasswordPolicy
    sessionTimeout: number
    mfaRequired: boolean
    ipWhitelist?: string[]
    ssoEnabled: boolean
    ssoProvider?: string
  }
  notifications: {
    emailEnabled: boolean
    smsEnabled: boolean
    pushEnabled: boolean
    webhookUrl?: string
  }
  integrations: {
    enabled: string[]
    configurations: Record<string, any>
  }
}

export interface PasswordPolicy {
  minLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSpecialChars: boolean
  maxAge: number
  preventReuse: number
}

export interface TenantLimits {
  users: number
  storage: number // in GB
  apiCalls: number // per month
  projects: number
  departments: number
  customFields: number
  automationRules: number
  reportRetention: number // in days
}

export interface TenantFeatures {
  modules: string[]
  advancedAnalytics: boolean
  customReports: boolean
  apiAccess: boolean
  whiteLabeling: boolean
  prioritySupport: boolean
  customIntegrations: boolean
  auditLogs: boolean
  dataExport: boolean
  backupRestore: boolean
}

export interface TenantBilling {
  plan: string
  billingCycle: 'monthly' | 'yearly'
  amount: number
  currency: string
  nextBillingDate: Date
  paymentMethod: string
  invoices: TenantInvoice[]
  usage: TenantUsage
}

export interface TenantInvoice {
  id: string
  amount: number
  status: 'paid' | 'pending' | 'overdue' | 'cancelled'
  dueDate: Date
  paidDate?: Date
  downloadUrl: string
}

export interface TenantUsage {
  users: { current: number; limit: number }
  storage: { current: number; limit: number }
  apiCalls: { current: number; limit: number }
  projects: { current: number; limit: number }
}

export interface TenantUser {
  id: string
  tenantId: string
  email: string
  role: string
  permissions: string[]
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
}

export interface TenantAuditLog {
  id: string
  tenantId: string
  userId: string
  action: string
  resource: string
  resourceId?: string
  details: Record<string, any>
  ipAddress: string
  userAgent: string
  timestamp: Date
}

class TenantService {
  private baseUrl = '/api/tenants'
  private currentTenant: Tenant | null = null

  // Tenant Management
  async getCurrentTenant(): Promise<Tenant | null> {
    if (this.currentTenant) {
      return this.currentTenant
    }

    try {
      const response = await (apiClient).get<Tenant>('/tenants/current/')
      this.currentTenant = (response).data
      return this.currentTenant
    } catch (error) {
      console.error('Tenant Service Error:', error)
      return null
    }
  }
  async createTenant(tenantData: Omit<Tenant, 'id' | 'createdAt' | 'updatedAt'>): Promise<Tenant> {
    try {
      const response = await fetch(`${this.baseUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify(tenantData)
      })

      if (!(response).ok) {
        throw new Error('Failed to create tenant')
      }
      return await (response).json()
    } catch (error) {
      console.error('Tenant Service Error:', error)
      throw error
    }
  }
  async updateTenant(id: string, updates: Partial<Tenant>): Promise<Tenant> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify(updates)
      })

      if (!(response).ok) {
        throw new Error('Failed to update tenant')
      }
      const updatedTenant = await (response).json()
      if (this.currentTenant?.id === id) {
        this.currentTenant = updatedTenant
      }
      return updatedTenant
    } catch (error) {
      console.error('Tenant Service Error:', error)
      throw error
    }
  }
  async deleteTenant(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        }
      })

      if (!(response).ok) {
        throw new Error('Failed to delete tenant')
      }
    } catch (error) {
      console.error('Tenant Service Error:', error)
      throw error
    }
  }

  // Settings Management
  async updateSettings(settings: Partial<TenantSettings>): Promise<TenantSettings> {
    try {
      const response = await fetch(`${this.baseUrl}/current/settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify(settings)
      })

      if (!(response).ok) {
        throw new Error('Failed to update tenant settings')
      }
      const updatedSettings = await (response).json()
      if (this.currentTenant) {
        this.currentTenant.settings = { ...this.currentTenant.settings, ...updatedSettings }
      }
      return updatedSettings
    } catch (error) {
      console.error('Tenant Service Error:', error)
      throw error
    }
  }

  // User Management
  async getTenantUsers(): Promise<TenantUser[]> {
    try {
      const response = await (apiClient).get<TenantUser[]>('/tenants/users/')
      return (response).data
    } catch (error) {
      console.error('Tenant Service Error:', error)
      return []
    }
  }
  async inviteUser(email: string, role: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/current/users/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify({ email, role })
      })

      if (!(response).ok) {
        throw new Error('Failed to invite user')
      }
    } catch (error) {
      console.error('Tenant Service Error:', error)
      throw error
    }
  }
  async removeUser(userId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/current/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        }
      })

      if (!(response).ok) {
        throw new Error('Failed to remove user')
      }
    } catch (error) {
      console.error('Tenant Service Error:', error)
      throw error
    }
  }

  // Usage Analytics
  async getUsage(): Promise<TenantUsage> {
    try {
      const response = await (apiClient).get<TenantUsage>('/tenants/usage/')
      return (response).data
    } catch (error) {
      console.error('Tenant Service Error:', error)
      return {
        users: { current: 0, limit: 100 },
        storage: { current: 0, limit: 50 },
        apiCalls: { current: 0, limit: 10000 },
        projects: { current: 0, limit: 50 }
      }
    }
  }

  // Audit Logs
  async getAuditLogs(filters?: any): Promise<TenantAuditLog[]> {
    try {
      const queryParams = filters ? `?${new URLSearchParams(filters).toString()}` : ''
      const response = await fetch(`${this.baseUrl}/current/audit-logs${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        }
      })

      if (!(response).ok) {
        throw new Error('Failed to fetch audit logs')
      }
      return await (response).json()
    } catch (error) {
      console.error('Tenant Service Error:', error)
      return this.getMockAuditLogs()
    }
  }

  // Billing
  async getBillingInfo(): Promise<TenantBilling> {
    try {
      const response = await (apiClient).get<TenantBilling>('/tenants/billing/')
      return (response).data
    } catch (error) {
      console.error('Tenant Service Error:', error)
      return {
        plan: 'professional',
        billingCycle: 'monthly',
        amount: 2999,
        currency: 'SAR',
        nextBillingDate: new Date((Date).now() + 30 * 24 * 60 * 60 * 1000),
        paymentMethod: 'credit_card',
        invoices: [],
        usage: {
          users: { current: 0, limit: 100 },
          storage: { current: 0, limit: 50 },
          apiCalls: { current: 0, limit: 10000 },
          projects: { current: 0, limit: 50 }
        }
      }
    }
  }
  async updateBilling(billingData: Partial<TenantBilling>): Promise<TenantBilling> {
    try {
      const response = await fetch(`${this.baseUrl}/current/billing`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify(billingData)
      })

      if (!(response).ok) {
        throw new Error('Failed to update billing info')
      }
      return await (response).json()
    } catch (error) {
      console.error('Tenant Service Error:', error)
      throw error
    }
  }

  // Utility Methods
  getTenantContext(): string | null {
    return this.currentTenant?.id || null
  }
  isFeatureEnabled(feature: keyof TenantFeatures): boolean {
    if (!this.currentTenant) return false
    return this.currentTenant.features[feature] === true
  }
  hasReachedLimit(resource: keyof TenantLimits): boolean {
    if (!this.currentTenant) return false
    // This would be implemented based on current usage vs limits
    return false
  }

}
export const tenantService = new TenantService()
export default tenantService
