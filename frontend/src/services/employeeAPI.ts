import React from 'react';
/**
 * Employee Management API Services
 */

import { apiClient } from './api'
import {
  Employee as EmployeeType,
  EmployeeFormData as EmployeeFormDataType,
  EmployeeFilters as EmployeeFiltersType,
  EmployeeStats
} from '../types/employee'
import {
  ApiResponse,
  PaginatedApiResponse,
  ListQueryParams,
  ImportResponse,
  ExportResponse
} from '../types/api'

// Backend API Response Types
export interface EmployeeAPIResponse {
  id: number
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
    is_active: boolean
    date_joined: string
  }
  employee_id: string
  department: number
  department_name: string
  department_name_ar: string
  position: string
  position_ar: string
  first_name_ar: string
  last_name_ar: string
  phone: string
  gender: 'M' | 'F'
  hire_date: string
  salary?: number
  employment_status: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERN' | 'CONSULTANT'
  is_active: boolean
  created_at: string
  updated_at: string
}

// Frontend Employee Interface (unified)
export interface Employee {
  id: number
  firstName: string
  firstNameAr: string
  lastName: string
  lastNameAr: string
  name: string
  nameAr: string
  email: string
  employeeId: string
  position: string
  positionAr: string
  department: string
  departmentAr: string
  departmentId: number
  salary: number
  hireDate: string
  joinDate: string
  phone: string
  gender: 'M' | 'F'
  isActive: boolean
  status: 'active' | 'inactive' | 'terminated' | 'on-leave'
  username: string
  userId: number
  createdAt: string
  updatedAt: string
  // Activation status
  isAccountActivated: boolean
  activationEmailSent: boolean
  activationEmailSentAt?: string
}

// Data transformer function - Fixed to match frontend Employee interface
export function transformEmployeeData(apiData: EmployeeAPIResponse): any {
  // Create a unified employee object that matches the frontend Employee interface
  const transformedEmployee = {
    // Basic identification
    id: apiData.id,
    employeeId: apiData.employee_id,
    userId: apiData.user.id,

    // Names (both English and Arabic)
    firstName: apiData.user.first_name || '',
    lastName: apiData.user.last_name || '',
    firstNameAr: apiData.first_name_ar || apiData.user.first_name || '',
    lastNameAr: apiData.last_name_ar || apiData.user.last_name || '',
    name: `${apiData.user.first_name || ''} ${apiData.user.last_name || ''}`.trim() || 'N/A',
    nameAr: `${apiData.first_name_ar || apiData.user.first_name || ''} ${apiData.last_name_ar || apiData.user.last_name || ''}`.trim() || 'غير محدد',

    // Contact information
    email: apiData.user.email || '',
    phone: apiData.phone || '',

    // Job information
    position: apiData.position || '',
    positionAr: apiData.position_ar || apiData.position || '',
    department: apiData.department_name || '',
    departmentAr: apiData.department_name_ar || apiData.department_name || '',
    departmentId: apiData.department,

    // Employment details
    hireDate: apiData.hire_date || '',
    joinDate: apiData.hire_date || '', // Alias for hireDate
    salary: Number(apiData.salary) || 0,
    gender: apiData.gender || 'M',

    // Status mapping - convert employment_status to simple status
    status: apiData.is_active
      ? (apiData.employment_status === 'FULL_TIME' ? 'active' : 'active')
      : 'inactive',
    isActive: apiData.is_active,

    // Additional fields for compatibility - get from real data when available
    username: apiData.user.username || apiData.user.email?.split('@')[0] || '',
    createdAt: apiData.created_at,
    updatedAt: apiData.updated_at,

    // Activation status (default values for now - will be populated from backend)
    isAccountActivated: apiData.user.is_active || false,
    activationEmailSent: false,
    activationEmailSentAt: undefined,

    // Backend compatibility fields (for EmployeeType)
    first_name: apiData.user.first_name,
    first_name_ar: apiData.first_name_ar || apiData.user.first_name,
    last_name: apiData.user.last_name,
    last_name_ar: apiData.last_name_ar || apiData.user.last_name,
    employee_id: apiData.employee_id,
    position_ar: apiData.position_ar,
    department_id: apiData.department,
    hire_date: apiData.hire_date,
    is_active: apiData.is_active,
    user_id: apiData.user.id,
    created_at: apiData.created_at,
    updated_at: apiData.updated_at,
    created_by: apiData.user.id,
    updated_by: apiData.user.id,
    full_name: `${apiData.user.first_name} ${apiData.user.last_name}`.trim(),
    employment_status: apiData.employment_status || 'FULL_TIME',
    work_location: 'Office',
    currency: 'USD',
    manager_id: undefined,
    emergency_contact: { name: '', phone: '', relationship: '' },
    notes: '',
    address: '', // Fixed: Address should be a string, not an object
    skills: [],
    education: [],
    certifications: [],
    languages: [],
    benefits: [],
    timezone: 'UTC',
    full_name_ar: `${apiData.first_name_ar || apiData.user.first_name} ${apiData.last_name_ar || apiData.user.last_name}`.trim()
  }

  console.log('🔄 Transformed employee:', {
    id: transformedEmployee.id,
    name: transformedEmployee.name,
    firstName: transformedEmployee.firstName,
    lastName: transformedEmployee.lastName,
    position: transformedEmployee.position,
    department: transformedEmployee.department,
    status: transformedEmployee.status,
    hireDate: transformedEmployee.hireDate
  })

  return transformedEmployee
}

// Transform frontend form data to backend API format
export function transformFormDataToAPI(formData: EmployeeFormDataType): CreateEmployeeData {
  return {
    user: {
      username: formData.username || formData.employee_id || formData.email?.split('@')[0] || '',
      email: formData.email,
      first_name: formData.first_name,
      last_name: formData.last_name,
      password: formData.password || 'defaultPassword123!' // Generate a default password
    },
    employee_id: formData.employee_id,
    department: Number(formData.department_id),
    position: formData.position,
    position_ar: formData.position_ar || formData.positionAr || '',
    first_name_ar: formData.firstNameAr || '',
    last_name_ar: formData.lastNameAr || '',
    phone: formData.phone,
    gender: formData.gender as 'M' | 'F',
    hire_date: formData.hire_date,
    salary: formData.salary ? Number(formData.salary) : undefined,
    employment_status: (formData.employment_status || 'FULL_TIME') as 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERN' | 'CONSULTANT'
  }
}

export interface CreateEmployeeData {
  user: {
    username: string
    email: string
    first_name: string
    last_name: string
    password: string
  }
  employee_id: string
  department: number
  position: string
  position_ar: string
  first_name_ar?: string
  last_name_ar?: string
  phone?: string
  gender: 'M' | 'F'
  hire_date: string
  salary?: number
  employment_status: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERN' | 'CONSULTANT'
}

export interface UpdateEmployeeData {
  department?: number
  position?: string
  position_ar?: string
  phone?: string
  salary?: number
  employment_status?: 'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'INTERN' | 'CONSULTANT'
  is_active?: boolean
}

// Using imported EmployeeFiltersType instead

// Department Types
export interface Department {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar: string
  employee_count: number
  created_at: string
  updated_at: string
}

export interface CreateDepartmentData {
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  budget_amount?: number
  location?: string
  phone?: string
  email?: string
}

// Employee API
export const employeeAPI = {
  // Get all employees
  getAll: async (filters?: EmployeeFiltersType): Promise<{
    data: EmployeeType[]
    total: number
    page: number
    pageSize: number
  }> => {
    // Extract search from filters and flatten the parameters
    const { search, ...otherFilters } = filters || {}
    const apiParams = {
      ...otherFilters,
      ...(search && { search: search })
    }

    const response = await apiClient.get<{
      count: number
      next: string | null
      previous: string | null
      results: EmployeeAPIResponse[]
    }>('/employees/', { params: apiParams })

    // Handle paginated response
    const employees = response.data.results || response.data
    let transformedEmployees = Array.isArray(employees)
      ? employees.map(transformEmployeeData)
      : []

    console.log('🔄 Employee API - Transformed employees:', transformedEmployees.length)

    // Client-side search fallback - always apply if search term exists
    const searchTerm = filters?.search
    if (searchTerm && searchTerm.trim().length > 0 && transformedEmployees.length > 0) {
      const normalizedSearchTerm = searchTerm.toLowerCase().trim()
      console.log('🔍 Client-side search for:', normalizedSearchTerm)

      const filteredEmployees = transformedEmployees.filter(employee => {
        const searchableFields = [
          // Use the new unified field names from our transformation
          employee.firstName,
          employee.lastName,
          employee.firstNameAr,
          employee.lastNameAr,
          employee.name,
          employee.nameAr,
          `${employee.firstName} ${employee.lastName}`.trim(),
          employee.email,
          employee.employeeId,
          employee.position,
          employee.positionAr,
          employee.department,
          employee.departmentAr,
          employee.phone,
          // Also check legacy field names for compatibility
          employee.first_name,
          employee.first_name_ar,
          employee.last_name,
          employee.last_name_ar,
          employee.employee_id,
          employee.position_ar
        ].filter(Boolean) // Remove null/undefined values

        const matchFound = searchableFields.some(field =>
          field && typeof field === 'string' && field.toLowerCase().includes(normalizedSearchTerm)
        )

        if (matchFound) {
          console.log(`✅ Match found for "${normalizedSearchTerm}" in employee:`, employee.name || employee.firstName)
        }

        return matchFound
      })

      console.log('✅ Client-side search results:', filteredEmployees.length, 'out of', transformedEmployees.length)
      transformedEmployees = filteredEmployees
    }

    const result = {
      data: transformedEmployees,
      total: response.data.count || transformedEmployees.length,
      page: 1, // Default page
      pageSize: transformedEmployees.length
    }

    // Return the result without excessive logging

    return result
  },

  // Get employee by ID
  getById: async (id: number): Promise<EmployeeType> => {
    const response = await apiClient.get<EmployeeAPIResponse>(`/employees/${id}/`)
    return transformEmployeeData(response.data)
  },

  // Create new employee
  create: async (employeeData: EmployeeFormDataType): Promise<EmployeeType> => {
    // Transform frontend form data to backend API format
    const transformedData = transformFormDataToAPI(employeeData)
    const response = await apiClient.post<EmployeeAPIResponse>('/employees/', transformedData)
    return transformEmployeeData(response.data)
  },

  // Update employee
  update: async (id: number, employeeData: UpdateEmployeeData): Promise<EmployeeType> => {
    const response = await apiClient.patch<EmployeeAPIResponse>(`/employees/${id}/`, employeeData)
    return transformEmployeeData(response.data)
  },

  // Delete employee
  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/employees/${id}/`)
  },

  // Get employee statistics
  getStats: async (): Promise<{
    total: number
    active: number
    by_department: Record<string, number>
    by_status: Record<string, number>
  }> => {
    const response = await apiClient.get('/employees/stats/')
    return response.data as {
      total: number
      active: number
      by_department: Record<string, number>
      by_status: Record<string, number>
    }
  },
}

// Department API
export const departmentAPI = {
  // Get all departments
  getAll: async (): Promise<Department[]> => {
    const response = await apiClient.get<Department[]>('/departments/')
    return response.data
  },

  // Get department by ID
  getById: async (id: number): Promise<Department> => {
    const response = await apiClient.get<Department>(`/departments/${id}/`)
    return response.data
  },

  // Create new department
  create: async (departmentData: CreateDepartmentData): Promise<Department> => {
    const response = await apiClient.post<Department>('/departments/', departmentData)
    return response.data
  },

  // Update department
  update: async (id: number, departmentData: Partial<CreateDepartmentData>): Promise<Department> => {
    const response = await apiClient.patch<Department>(`/departments/${id}/`, departmentData)
    return response.data
  },

  // Delete department
  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/departments/${id}/`)
  },

  // Get department employees
  getEmployees: async (id: number): Promise<Employee[]> => {
    const response = await apiClient.get<Employee[]>(`/departments/${id}/employees/`)
    return response.data
  },
}

// Activity Types
export interface Activity {
  id: number
  user: number
  user_name: string
  activity_type: string
  description: string
  description_ar: string
  timestamp: string
  ip_address?: string
}

// Activity API
export const activityAPI = {
  // Get all activities
  getAll: async (limit?: number): Promise<Activity[]> => {
    const params = limit ? { limit } : {}
    const response = await apiClient.get<Activity[]>('/activities/', { params })
    return response.data
  },

  // Get activities by user
  getByUser: async (userId: number): Promise<Activity[]> => {
    const response = await apiClient.get<Activity[]>('/activities/', { 
      params: { user: userId } 
    })
    return response.data
  },

  // Get recent activities
  getRecent: async (limit: number = 10): Promise<Activity[]> => {
    const response = await apiClient.get<Activity[]>('/activities/', { 
      params: { limit, ordering: '-timestamp' } 
    })
    return response.data
  },
}

export default {
  employeeAPI,
  departmentAPI,
  activityAPI,
}
