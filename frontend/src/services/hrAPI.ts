import React from 'react';
/**
 * HR Management API Services
 */

import { apiClient } from './api'

// Leave Types
export interface LeaveType {
  id: number
  name: string
  name_ar: string
  days_allowed: number
  is_paid: boolean
  requires_approval: boolean
  carry_forward: boolean
  description: string
  description_ar: string
  is_active: boolean
  created_at: string
}

export interface CreateLeaveTypeData {
  name: string
  name_ar: string
  days_allowed: number
  is_paid?: boolean
  requires_approval?: boolean
  carry_forward?: boolean
  description?: string
  description_ar?: string
}

// Leave Request Types
export interface LeaveRequest {
  id: number
  employee: number
  employee_name: string
  leave_type: number
  leave_type_name: string
  start_date: string
  end_date: string
  days_requested: number
  reason: string
  reason_ar?: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED'
  approved_by?: number
  approved_by_name?: string
  approval_date?: string
  rejection_reason?: string
  rejection_reason_ar?: string
  created_at: string
  updated_at: string
}

export interface CreateLeaveRequestData {
  leave_type: number
  start_date: string
  end_date: string
  days_requested: number
  reason: string
  reason_ar?: string
}

export interface ApproveLeaveRequestData {
  status: 'APPROVED' | 'REJECTED'
  rejection_reason?: string
  rejection_reason_ar?: string
}

// Attendance Types
export interface Attendance {
  id: number
  employee: number
  employee_name: string
  date: string
  check_in?: string
  check_out?: string
  break_start?: string
  break_end?: string
  total_hours?: number
  overtime_hours: number
  is_present: boolean
  is_late: boolean
  notes?: string
  created_at: string
  updated_at: string
}

export interface CreateAttendanceData {
  employee: number
  date: string
  check_in?: string
  check_out?: string
  break_start?: string
  break_end?: string
  total_hours?: number
  overtime_hours?: number
  is_present?: boolean
  is_late?: boolean
  notes?: string
}

export interface AttendanceFilters {
  employee?: number
  date?: string
  date_from?: string
  date_to?: string
  is_present?: boolean
  is_late?: boolean
}

// Role Types
export interface Role {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar: string
  permissions: Record<string, any>
  created_at: string
}

export interface CreateRoleData {
  name: string
  name_ar: string
  description?: string
  description_ar?: string
  permissions?: Record<string, any>
}

// User Profile Types
export interface UserProfile {
  id: number
  user: {
    id: number
    username: string
    email: string
    first_name: string
    last_name: string
  }
  role?: Role
  avatar?: string
  phone?: string
  address?: string
  date_of_birth?: string
  emergency_contact_name?: string
  emergency_contact_phone?: string
  preferred_language: 'ar' | 'en'
  timezone: string
  created_at: string
  updated_at: string
}

// Leave Type API
export const leaveTypeAPI: any = {
  getAll: async (): Promise<LeaveType[]> => {
    const response = await apiClient.get<LeaveType[]>('/leave-types/')
    return response.data
  },

  getById: async (id: number): Promise<LeaveType> => {
    const response = await apiClient.get<LeaveType>(`/leave-types/${id}/`)
    return response.data
  },

  create: async (data: CreateLeaveTypeData): Promise<LeaveType> => {
    const response = await apiClient.post<LeaveType>('/leave-types/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateLeaveTypeData>): Promise<LeaveType> => {
    const response = await apiClient.patch<LeaveType>(`/leave-types/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/leave-types/${id}/`)
  },
}

// Leave Request API
export const leaveRequestAPI = {
  getAll: async (filters?: { employee?: number; status?: string }): Promise<LeaveRequest[]> => {
    const response: any = await apiClient.get<LeaveRequest[]>('/leave-requests/', { params: filters })
    return response.data
  },

  getById: async (id: number): Promise<LeaveRequest> => {
    const response = await apiClient.get<LeaveRequest>(`/leave-requests/${id}/`)
    return response.data
  },

  create: async (data: CreateLeaveRequestData): Promise<LeaveRequest> => {
    const response = await apiClient.post<LeaveRequest>('/leave-requests/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateLeaveRequestData>): Promise<LeaveRequest> => {
    const response = await apiClient.patch<LeaveRequest>(`/leave-requests/${id}/`, data)
    return response.data
  },

  approve: async (id: number, data: ApproveLeaveRequestData): Promise<LeaveRequest> => {
    const response = await apiClient.patch<LeaveRequest>(`/leave-requests/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/leave-requests/${id}/`)
  },

  getMyRequests: async (): Promise<LeaveRequest[]> => {
    const response = await apiClient.get<LeaveRequest[]>('/leave-requests/my-requests/')
    return response.data
  },
}

// Attendance API
export const attendanceAPI = {
  getAll: async (filters?: AttendanceFilters): Promise<Attendance[]> => {
    const response = await apiClient.get<Attendance[]>('/attendance/', { params: filters })
    return response.data
  },

  getById: async (id: number): Promise<Attendance> => {
    const response = await apiClient.get<Attendance>(`/attendance/${id}/`)
    return response.data
  },

  create: async (data: CreateAttendanceData): Promise<Attendance> => {
    const response = await apiClient.post<Attendance>('/attendance/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateAttendanceData>): Promise<Attendance> => {
    const response = await apiClient.patch<Attendance>(`/attendance/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/attendance/${id}/`)
  },

  checkIn: async (employeeId: number): Promise<Attendance> => {
    const response = await apiClient.post<Attendance>('/attendance/check-in/', { employee: employeeId })
    return response.data
  },

  checkOut: async (attendanceId: number): Promise<Attendance> => {
    const response = await apiClient.patch<Attendance>(`/attendance/${attendanceId}/check-out/`)
    return response.data
  },

  getMyAttendance: async (filters?: { date_from?: string; date_to?: string }): Promise<Attendance[]> => {
    const response = await apiClient.get<Attendance[]>('/attendance/my-attendance/', { params: filters })
    return response.data
  },
}

// Role API
export const roleAPI = {
  getAll: async (): Promise<Role[]> => {
    const response = await apiClient.get<Role[]>('/roles/')
    return response.data
  },

  getById: async (id: number): Promise<Role> => {
    const response = await apiClient.get<Role>(`/roles/${id}/`)
    return response.data
  },

  create: async (data: CreateRoleData): Promise<Role> => {
    const response = await apiClient.post<Role>('/roles/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateRoleData>): Promise<Role> => {
    const response = await apiClient.patch<Role>(`/roles/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/roles/${id}/`)
  },
}

// User Profile API
export const userProfileAPI = {
  getAll: async (): Promise<UserProfile[]> => {
    const response = await apiClient.get<UserProfile[]>('/user-profiles/')
    return response.data
  },

  getById: async (id: number): Promise<UserProfile> => {
    const response = await apiClient.get<UserProfile>(`/user-profiles/${id}/`)
    return response.data
  },

  update: async (id: number, data: Partial<UserProfile>): Promise<UserProfile> => {
    const response = await apiClient.patch<UserProfile>(`/user-profiles/${id}/`, data)
    return response.data
  },
}

export default {
  leaveTypeAPI,
  leaveRequestAPI,
  attendanceAPI,
  roleAPI,
  userProfileAPI,
}
