import React from 'react';
/**
 * Employee Approval Service
 * Handles employee approval workflow for admin users
 */

import { apiClient } from './api'

export interface PendingEmployee {
  id: number
  employee_id: string
  name: string
  name_ar: string
  email: string
  position: string
  position_ar: string
  department: string | null
  department_ar: string | null
  created_by: string | null
  created_at: string
  activation_id: number
}

export interface ApprovalResponse {
  message: string
}

class EmployeeApprovalService {
  private baseUrl = '/employees'

  /**
   * Get all employees pending admin approval
   */
  async getPendingApprovals(): Promise<PendingEmployee[]> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/pending-approvals/`)
      return response.data
    } catch (error) {
      console.error('Error fetching pending approvals:', error)
      throw error
    }
  }

  /**
   * Approve an employee activation
   */
  async approveEmployee(employeeId: number): Promise<ApprovalResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${employeeId}/approve/`)
      return response.data
    } catch (error) {
      console.error('Error approving employee:', error)
      throw error
    }
  }

  /**
   * Reject an employee activation
   */
  async rejectEmployee(employeeId: number, reason: string = ''): Promise<ApprovalResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${employeeId}/reject/`, {
        reason
      })
      return response.data
    } catch (error) {
      console.error('Error rejecting employee:', error)
      throw error
    }
  }
}
export const employeeApprovalService = new EmployeeApprovalService()
export default employeeApprovalService
