import React from 'react';
/**
 * Centralized Navigation Service
 * Replaces all window.location.href and window.location.reload() calls
 * Uses React Router for proper SPA navigation
 */

import { NavigateFunction } from 'react-router-dom'

interface NavigationOptions {
  replace?: boolean
  state?: any
}

class NavigationService {
  private navigate: NavigateFunction | null = null
  private eventListeners: Set<(path: string) => void> = new Set()

  /**
   * Initialize the navigation service with React Router's navigate function
   */
  setNavigate(navigate: NavigateFunction): void {
    this.navigate = navigate
  }

  /**
   * Navigate to a path using React Router
   */
  navigateTo(path: string, options: NavigationOptions = {}): void {
    if (this.navigate) {
      this.navigate(path, options)
    } else {
      // Fallback: emit navigation event for components to handle
      this.emitNavigationEvent(path)
    }
  }

  /**
   * Navigate to login page
   */
  navigateToLogin(): void {
    this.navigateTo('/login', { replace: true })
  }

  /**
   * Navigate to home page
   */
  navigateToHome(): void {
    this.navigateTo('/', { replace: true })
  }

  /**
   * Navigate back in history
   */
  goBack(): void {
    if (window.history.length > 1) {
      window.history.back()
    } else {
      this.navigateToHome()
    }
  }

  /**
   * Navigate forward in history
   */
  goForward(): void {
    window.history.forward()
  }

  /**
   * Refresh current page data without page reload
   */
  refreshPage(): void {
    // Emit refresh event instead of reloading page
    window.dispatchEvent(new CustomEvent('app:refresh', {
      detail: { timestamp: Date.now() }
    }))
  }

  /**
   * Handle external URLs
   */
  openExternal(url: string, newTab: boolean = true): void {
    if (newTab) {
      window.open(url, '_blank', 'noopener,noreferrer')
    } else {
      // For external URLs, we still need to use window.location
      // but only for actual external sites
      if (this.isExternalUrl(url)) {
        window.location.href = url
      } else {
        // Internal URL - use React Router
        const path = url.replace(window.location.origin, '')
        this.navigateTo(path)
      }
    }
  }

  /**
   * Check if URL is external
   */
  private isExternalUrl(url: string): boolean {
    try {
      const urlObj = new URL(url, window.location.origin)
      return urlObj.origin !== window.location.origin
    } catch {
      return false
    }
  }

  /**
   * Emit navigation event for components that can't access navigate directly
   */
  private emitNavigationEvent(path: string): void {
    window.dispatchEvent(new CustomEvent('app:navigate', {
      detail: { path }
    }))
    
    // Notify listeners
    this.eventListeners.forEach(listener => listener(path))
  }

  /**
   * Add navigation event listener
   */
  addNavigationListener(listener: (path: string) => void): void {
    this.eventListeners.add(listener)
  }

  /**
   * Remove navigation event listener
   */
  removeNavigationListener(listener: (path: string) => void): void {
    this.eventListeners.delete(listener)
  }

  /**
   * Handle authentication redirects
   */
  handleAuthRedirect(reason: string = 'Authentication required'): void {
    console.log(`Navigation: Redirecting to login - ${reason}`)
    
    // Clear any auth-related storage
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    
    // Navigate to login
    this.navigateToLogin()
  }

  /**
   * Handle error navigation
   */
  handleError(error: any, fallbackPath: string = '/'): void {
    console.error('Navigation: Handling error', error)
    
    // For critical errors, go to home
    if (error.severity === 'critical') {
      this.navigateToHome()
    } else {
      // For other errors, try the fallback path
      this.navigateTo(fallbackPath)
    }
  }

  /**
   * Force refresh application state (alternative to page reload)
   */
  forceRefresh(): void {
    // Clear caches
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => caches.delete(name))
      })
    }
    
    // Clear session storage (keep localStorage for user preferences)
    sessionStorage.clear()
    
    // Emit global refresh event
    window.dispatchEvent(new CustomEvent('app:force-refresh', {
      detail: { timestamp: Date.now() }
    }))
    
    // Navigate to current path to trigger re-render
    const currentPath = window.location.pathname
    this.navigateTo(currentPath, { replace: true })
  }

  /**
   * Get current path
   */
  getCurrentPath(): string {
    return window.location.pathname
  }

  /**
   * Check if we're on a specific path
   */
  isCurrentPath(path: string): boolean {
    return window.location.pathname === path
  }
}

// Export singleton instance
export const navigationService = new NavigationService()

// Global navigation functions for backward compatibility
if (typeof window !== 'undefined') {
  // Make navigation service available globally for debugging
  window.navigationService = navigationService
  
  console.log('🧭 Navigation Service initialized')
  console.log('  - Use navigationService.navigateTo(path) instead of window.location.href')
  console.log('  - Use navigationService.refreshPage() instead of window.location.reload()')
}

export default navigationService
