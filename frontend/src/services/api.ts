/**
 * API Service Layer for EMS Application
 * Handles all HTTP requests to the Django backend
 */

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'

// API Response Types
export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
}

export interface ApiErrorInterface {
  message: string
  status: number
  details?: any
}

// Authentication Types
export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResponse {
  access: string
  refresh: string
  user: User
}

export interface User {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  role: UserRole
  profile: UserProfile
}

export interface UserRole {
  id: string
  name: string
  nameAr: string
  permissions: Permission[]
  dashboardConfig: DashboardConfig
}

export interface Permission {
  module: string
  actions: string[]
}

export interface DashboardConfig {
  allowedRoutes: string[]
  defaultWidgets: string[]
  customizations: any
}

export interface UserProfile {
  avatar?: string
  phone?: string
  department?: string
  position?: string
  preferred_language: 'ar' | 'en'
  timezone: string
}

// HTTP Client Class
class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL
    this.token = localStorage.getItem('access_token')
  }

  // Set authentication token
  setToken(token: string | null) {
    this.token = token
    if (token) {
      localStorage.setItem('access_token', token)
    } else {
      localStorage.removeItem('access_token')
    }
  }

  // Get authentication headers
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return headers
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new ApiError({
          message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
          details: errorData,
        })
      }

      const data = await response.json()
      
      return {
        data,
        status: response.status,
        message: data.message,
      }
    } catch (error) {
      if (error instanceof ApiError) {
        throw error
      }
      
      throw new ApiError({
        message: error instanceof Error ? error.message : 'Network error occurred',
        status: 0,
        details: error,
      })
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  // File upload method
  async uploadFile<T>(endpoint: string, file: File, additionalData?: any): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key])
      })
    }

    const headers: HeadersInit = {}
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers,
    })
  }
}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL)

// Dashboard Stats Types
export interface DashboardStats {
  total_employees: number
  active_employees: number
  total_departments: number
  active_projects: number
  pending_tasks: number
  pending_leave_requests: number
  monthly_revenue: number
  monthly_expenses: number
  system_health: {
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    uptime: string
  }
}

export interface SystemStats {
  uptime: string
  activeUsers: number
  systemLoad: number
  storage: number
  serverLoad: number
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  apiRequests: number
  responseTime: number
  errorRate: number
}

export interface EmployeeOverview {
  totalEmployees: number
  presentToday: number
  onLeave: number
  newHires: number
  activeEmployees: number
  by_department: Record<string, number>
  by_status: Record<string, number>
}

export interface FinancialOverview {
  revenue: number
  expenses: number
  profit: number
  growth: number
  monthly_revenue: number
  monthly_expenses: number
  budget_utilization: number
}

// Dashboard API Service
export const dashboardAPI = {
  // Get general dashboard stats
  getStats: async (): Promise<DashboardStats> => {
    const response = await apiClient.get<DashboardStats>('/dashboard-stats/')
    return response.data
  },

  // Get system overview stats for super admin
  getSystemStats: async (): Promise<SystemStats> => {
    const response = await apiClient.get<SystemStats>('/superadmin/system-stats/')
    return response.data
  },

  // Get employee overview stats
  getEmployeeStats: async (): Promise<EmployeeOverview> => {
    const response = await apiClient.get<EmployeeOverview>('/employees/stats/')
    return response.data
  },

  // Get financial overview stats
  getFinancialStats: async (): Promise<FinancialOverview> => {
    const response = await apiClient.get<FinancialOverview>('/financial-analytics/')
    return response.data
  },

  // Get widget-specific data
  getWidgetData: async (widgetId: string): Promise<any> => {
    switch (widgetId) {
      case 'system_overview':
        return dashboardAPI.getSystemStats()
      case 'employee_overview':
        return dashboardAPI.getEmployeeStats()
      case 'financial_overview':
        return dashboardAPI.getFinancialStats()
      default:
        return dashboardAPI.getStats()
    }
  }
}

// Custom error class
class ApiError extends Error {
  status: number
  details?: any

  constructor({ message, status, details }: { message: string; status: number; details?: any }) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.details = details
  }
}

export { ApiError }

// Utility functions
export const isApiError = (error: any): error is ApiError => {
  return error instanceof ApiError
}

export const handleApiError = (error: unknown): string => {
  if (isApiError(error)) {
    return error.message
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  return 'An unexpected error occurred'
}
