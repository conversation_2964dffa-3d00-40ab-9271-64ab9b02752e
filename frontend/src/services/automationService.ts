import React from 'react';
/**
 * Advanced Automation Service
 * Handles workflow automation, triggers, and business process automation
 */

import { apiClient } from './api'

export interface AutomationRule {
  id: string
  name: string
  description: string
  trigger: AutomationTrigger
  conditions: AutomationCondition[]
  actions: AutomationAction[]
  isActive: boolean
  createdBy: string
  createdAt: Date
  lastExecuted?: Date
  executionCount: number
  successRate: number
  category: 'hr' | 'finance' | 'project' | 'system' | 'custom'
}

export interface AutomationTrigger {
  type: 'schedule' | 'event' | 'webhook' | 'manual' | 'condition'
  config: {
    schedule?: string // cron expression
    event?: string // event name
    webhook?: string // webhook URL
    condition?: string // condition expression
  }
}

export interface AutomationCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'not_in'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

export interface AutomationAction {
  type: 'email' | 'notification' | 'api_call' | 'data_update' | 'approval_request' | 'report_generation'
  config: {
    template?: string
    recipients?: string[]
    url?: string
    method?: string
    data?: any
    approvers?: string[]
  }
}

export interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: string
  steps: WorkflowStep[]
  variables: WorkflowVariable[]
  isPublic: boolean
  usageCount: number
}

export interface WorkflowStep {
  id: string
  name: string
  type: 'approval' | 'notification' | 'data_entry' | 'calculation' | 'integration'
  config: any
  nextSteps: string[]
  conditions?: AutomationCondition[]
}

export interface WorkflowVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object'
  defaultValue?: any
  required: boolean
  description: string
}

export interface AutomationExecution {
  id: string
  ruleId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  startTime: Date
  endTime?: Date
  logs: AutomationLog[]
  result?: any
  error?: string
}

export interface AutomationLog {
  timestamp: Date
  level: 'info' | 'warning' | 'error' | 'debug'
  message: string
  data?: any
}

class AutomationService {
  private baseUrl = '/api/automation'

  // Rule Management
  async createRule(rule: Omit<AutomationRule, 'id' | 'createdAt' | 'executionCount' | 'successRate'>): Promise<AutomationRule> {
    try {
      const response = await fetch(`${this.baseUrl}/rules`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify(rule)
      })

      if (!(response).ok) {
        throw new Error('Failed to create automation rule')
      }
      return await (response).json()
    } catch (error) {
      console.error('Automation Service Error:', error)
      throw error
    }
  }
  async getRules(category?: string): Promise<AutomationRule[]> {
    try {
      const params = category ? { category } : {}
      const response = await (apiClient).get<AutomationRule[]>('/automation/rules/', { params })
      return (response).data
    } catch (error) {
      console.error('Automation Service Error:', error)
      return []
    }
  }
  async updateRule(id: string, updates: Partial<AutomationRule>): Promise<AutomationRule> {
    try {
      const response = await fetch(`${this.baseUrl}/rules/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify(updates)
      })

      if (!(response).ok) {
        throw new Error('Failed to update automation rule')
      }
      return await (response).json()
    } catch (error) {
      console.error('Automation Service Error:', error)
      throw error
    }
  }
  async deleteRule(id: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/rules/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        }
      })

      if (!(response).ok) {
        throw new Error('Failed to delete automation rule')
      }
    } catch (error) {
      console.error('Automation Service Error:', error)
      throw error
    }
  }

  // Execution Management
  async executeRule(ruleId: string, context?: any): Promise<AutomationExecution> {
    try {
      const response = await fetch(`${this.baseUrl}/rules/${ruleId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify({ context })
      })

      if (!(response).ok) {
        throw new Error('Failed to execute automation rule')
      }
      return await (response).json()
    } catch (error) {
      console.error('Automation Service Error:', error)
      throw error
    }
  }
  async getExecutions(ruleId?: string): Promise<AutomationExecution[]> {
    try {
      const params = ruleId ? { ruleId } : {}
      const response = await (apiClient).get<AutomationExecution[]>('/automation/executions/', { params })
      return (response).data
    } catch (error) {
      console.error('Automation Service Error:', error)
      return []
    }
  }
  async cancelExecution(executionId: string): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/executions/${executionId}/cancel`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        }
      })

      if (!(response).ok) {
        throw new Error('Failed to cancel execution')
      }
    } catch (error) {
      console.error('Automation Service Error:', error)
      throw error
    }
  }

  // Workflow Templates
  async getWorkflowTemplates(category?: string): Promise<WorkflowTemplate[]> {
    try {
      // For now, use customer service workflows as templates
      const params = category ? { category } : {}
      const response = await (apiClient).get<WorkflowTemplate[]>('/workflows/', { params })
      return (response).data
    } catch (error) {
      console.error('Automation Service Error:', error)
      return []
    }
  }
  async createWorkflowFromTemplate(templateId: string, variables: Record<string, any>): Promise<AutomationRule> {
    try {
      const response = await fetch(`${this.baseUrl}/templates/${templateId}/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify({ variables })
      })

      if (!(response).ok) {
        throw new Error('Failed to create workflow from template')
      }
      return await (response).json()
    } catch (error) {
      console.error('Automation Service Error:', error)
      throw error
    }
  }

  // Smart Suggestions
  async getSuggestions(context: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/suggestions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(localStorage).getItem('token')}`
        },
        body: JSON.stringify({ context })
      })

      if (!(response).ok) {
        throw new Error('Failed to get automation suggestions')
      }
      return await (response).json()
    } catch (error) {
      console.error('Automation Service Error:', error)
      return this.getMockSuggestions()
    }
  }

  // Analytics
  async getAnalytics(timeframe: string): Promise<any> {
    try {
      const response = await (apiClient).get<any>('/automation/rules/analytics/', { params: { timeframe } })
      return (response).data
    } catch (error) {
      console.error('Automation Service Error:', error)
      return {
        totalRules: 0,
        activeRules: 0,
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        averageExecutionTime: 0,
        timeSaved: 0
      }
    }
  }
}
export const automationService = new AutomationService()
export default automationService
