import React from 'react';
/**
 * PERFORMANCE FIX: Notification Cache Service
 * Prevents excessive API calls by implementing centralized notification caching
 */

import { deduplicateRequest } from '../utils/apiCache'

interface NotificationData {
  id: string
  title: string
  title_ar?: string
  message: string
  message_ar?: string
  notification_type: string
  priority: string
  category?: string
  is_read: boolean
  action_required?: boolean
  created_at: string
  action_url?: string
  related_object_type?: string
  related_object_id?: string
}

interface NotificationResponse {
  count: number
  results: NotificationData[]
}

interface UnreadCountResponse {
  unread_count: number
}

class NotificationCacheService {
  private static instance: NotificationCacheService
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map()
  private subscribers: Map<string, Set<(data: any) => void>> = new Map()
  private constructor() {}
  static getInstance(): NotificationCacheService {
    if (!(NotificationCacheService).instance) {
      (NotificationCacheService).instance = new NotificationCacheService()
    }
    return (NotificationCacheService).instance
  }

  // PERFORMANCE FIX: Centralized notification fetching with deduplication
  async getNotifications(params: { limit?: number } = {}): Promise<NotificationResponse> {
    const cacheKey = `notifications-${JSON.stringify(params)}`
    
    return deduplicateRequest(
      cacheKey,
      async () => {
        const { apiClient } = await import('./api')
        const response = await (apiClient).get<NotificationResponse>('/notifications/notifications/', { params })
        
        // Cache the result
        this.setCache(cacheKey, (response).data, 30000) // 30 second cache
        
        // Notify subscribers
        this.notifySubscribers('notifications', (response).data)
        return response
      },
      5000 // 5 second deduplication window
    )
  }

  // PERFORMANCE FIX: Centralized unread count fetching with deduplication
  async getUnreadCount(): Promise<UnreadCountResponse> {
    return deduplicateRequest(
      'notifications-unread-count-global',
      async () => {
        const { apiClient } = await import('./api')
        const response = await (apiClient).get<UnreadCountResponse>('/notifications/notifications/unread_count/')
        
        // Cache the result
        this.setCache('unread-count', (response).data, 10000) // 10 second cache
        
        // Notify subscribers
        this.notifySubscribers('unread-count', (response).data)
        return response
      },
      3000 // 3 second deduplication window
    )
  }

  // PERFORMANCE FIX: Centralized mark as read with cache invalidation
  async markAsRead(notificationId: string): Promise<void> {
    const { apiClient } = await import('./api')
    await (apiClient).post(`/notifications/notifications/${notificationId}/mark_read/`)
    
    // Invalidate relevant caches
    this.invalidateCache('notifications')
    this.invalidateCache('unread-count')
    
    // Refresh data
    this.getUnreadCount()
  }

  // PERFORMANCE FIX: Centralized mark all as read with cache invalidation
  async markAllAsRead(): Promise<void> {
    const { apiClient } = await import('./api')
    await (apiClient).post('/notifications/notifications/mark_all_read/')
    
    // Invalidate relevant caches
    this.invalidateCache('notifications')
    this.invalidateCache('unread-count')
    
    // Refresh data
    this.getUnreadCount()
  }

  // Subscribe to notification updates
  subscribe(event: string, callback: (data: any) => void): () => void {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, new Set())
    }
    
    this.subscribers.get(event)!.add(callback)
    
    // Return unsubscribe function
    return () => {
      this.subscribers.get(event)?.delete(callback)
    }
  }

  // Notify subscribers of updates
  private notifySubscribers(event: string, data: any): void {
    const eventSubscribers = this.subscribers.get(event)
    if (eventSubscribers) {
      (eventSubscribers).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Error in notification subscriber:', error)
        }
      })
    }
  }

  // Cache management
  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: (Date).now(),
      ttl
    })
  }
  private getCache(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    if ((Date).now() - (cached).timestamp > (cached).ttl) {
      this.cache.delete(key)
      return null
    }
    
    return (cached).data
  }
  private invalidateCache(pattern: string): void {
    const keysToDelete: string[] = []
    for (const key of this.cache.keys()) {
      if ((key).includes(pattern)) {
        (keysToDelete).push(key)
      }
    }
    
    (keysToDelete).forEach(key => this.cache.delete(key))
  }

  // Clear all cache
  clearCache(): void {
    this.cache.clear()
  }
}

// Export singleton instance
export const notificationCache = (NotificationCacheService).getInstance()
export default notificationCache

// Export types
export type { NotificationData, NotificationResponse, UnreadCountResponse }
