import React from 'react';
/**
 * Inventory & Asset Management API Services
 */

import { apiClient } from './api'

// Product Types
export interface Product {
  id: number
  name: string
  name_ar?: string
  sku: string
  description?: string
  description_ar?: string
  category: number
  category_name: string
  category_name_ar?: string
  brand?: string
  brand_ar?: string
  unit_price: number
  cost_price?: number
  quantity_in_stock: number
  minimum_stock_level: number
  maximum_stock_level?: number
  reorder_point: number
  unit_of_measure: string
  unit_of_measure_ar?: string
  barcode?: string
  weight?: number
  dimensions?: string
  status: 'ACTIVE' | 'INACTIVE' | 'DISCONTINUED'
  supplier?: number
  supplier_name?: string
  location?: string
  location_ar?: string
  expiry_date?: string
  batch_number?: string
  created_at: string
  updated_at: string
}

export interface CreateProductData {
  name: string
  name_ar?: string
  sku: string
  description?: string
  description_ar?: string
  category: number
  brand?: string
  brand_ar?: string
  unit_price: number
  cost_price?: number
  quantity_in_stock?: number
  minimum_stock_level: number
  maximum_stock_level?: number
  reorder_point: number
  unit_of_measure: string
  unit_of_measure_ar?: string
  barcode?: string
  weight?: number
  dimensions?: string
  status?: 'ACTIVE' | 'INACTIVE' | 'DISCONTINUED'
  supplier?: number
  location?: string
  location_ar?: string
  expiry_date?: string
  batch_number?: string
}

// Category Types
export interface Category {
  id: number
  name: string
  name_ar?: string
  description?: string
  description_ar?: string
  parent_category?: number
  parent_category_name?: string
  product_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CreateCategoryData {
  name: string
  name_ar?: string
  description?: string
  description_ar?: string
  parent_category?: number
}

// Stock Movement Types
export interface StockMovement {
  id: number
  product: number
  product_name: string
  product_sku: string
  movement_type: 'IN' | 'OUT' | 'ADJUSTMENT' | 'TRANSFER'
  quantity: number
  unit_cost?: number
  total_cost?: number
  reference_number?: string
  reason: string
  reason_ar?: string
  location_from?: string
  location_to?: string
  performed_by: number
  performed_by_name: string
  date: string
  notes?: string
  notes_ar?: string
  created_at: string
}

export interface CreateStockMovementData {
  product: number
  movement_type: 'IN' | 'OUT' | 'ADJUSTMENT' | 'TRANSFER'
  quantity: number
  unit_cost?: number
  reference_number?: string
  reason: string
  reason_ar?: string
  location_from?: string
  location_to?: string
  notes?: string
  notes_ar?: string
}

// Asset Types
export interface Asset {
  id: number
  name: string
  name_ar?: string
  asset_tag: string
  category: string
  category_ar?: string
  brand?: string
  model?: string
  serial_number?: string
  purchase_date: string
  purchase_cost: number
  current_value?: number
  depreciation_rate?: number
  warranty_expiry?: string
  location: string
  location_ar?: string
  assigned_to?: number
  assigned_to_name?: string
  status: 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'DISPOSED'
  condition: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
  notes?: string
  notes_ar?: string
  created_at: string
  updated_at: string
}

export interface CreateAssetData {
  name: string
  name_ar?: string
  asset_tag: string
  category: string
  category_ar?: string
  brand?: string
  model?: string
  serial_number?: string
  purchase_date: string
  purchase_cost: number
  current_value?: number
  depreciation_rate?: number
  warranty_expiry?: string
  location: string
  location_ar?: string
  assigned_to?: number
  status?: 'ACTIVE' | 'INACTIVE' | 'MAINTENANCE' | 'DISPOSED'
  condition?: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR'
  notes?: string
  notes_ar?: string
}

export interface ProductFilters {
  category?: number
  status?: string
  low_stock?: boolean
  supplier?: number
  search?: string
}

export interface StockMovementFilters {
  product?: number
  movement_type?: string
  date_from?: string
  date_to?: string
  performed_by?: number
}

export interface AssetFilters {
  category?: string
  status?: string
  condition?: string
  assigned_to?: number
  location?: string
  search?: string
}

// Product API
export const productAPI = {
  getAll: async (filters?: ProductFilters): Promise<Product[]> => {
    const response = await apiClient.get<Product[]>('/products/', { params: filters })
    return response.data
  },

  getById: async (id: number): Promise<Product> => {
    const response = await apiClient.get<Product>(`/products/${id}/`)
    return response.data
  },

  create: async (data: CreateProductData): Promise<Product> => {
    const response = await apiClient.post<Product>('/products/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateProductData>): Promise<Product> => {
    const response = await apiClient.patch<Product>(`/products/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/products/${id}/`)
  },

  getLowStock: async (): Promise<Product[]> => {
    const response = await apiClient.get<Product[]>('/products/low-stock/')
    return response.data
  },

  getStats: async (): Promise<{
    total_products: number
    low_stock_count: number
    total_value: number
    categories_count: number
    out_of_stock: number
  }> => {
    const response = await apiClient.get('/products/stats/')
    return response.data as {
      total_products: number
      low_stock_count: number
      total_value: number
      categories_count: number
      out_of_stock: number
    }
  },
}

// Category API
export const categoryAPI = {
  getAll: async (): Promise<Category[]> => {
    const response = await apiClient.get<Category[]>('/categories/')
    return response.data
  },

  getById: async (id: number): Promise<Category> => {
    const response = await apiClient.get<Category>(`/categories/${id}/`)
    return response.data
  },

  create: async (data: CreateCategoryData): Promise<Category> => {
    const response = await apiClient.post<Category>('/categories/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateCategoryData>): Promise<Category> => {
    const response = await apiClient.patch<Category>(`/categories/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/categories/${id}/`)
  },
}

// Stock Movement API
export const stockMovementAPI = {
  getAll: async (filters?: StockMovementFilters): Promise<StockMovement[]> => {
    const response = await apiClient.get<StockMovement[]>('/stock-movements/', { params: filters })
    return response.data
  },

  getById: async (id: number): Promise<StockMovement> => {
    const response = await apiClient.get<StockMovement>(`/stock-movements/${id}/`)
    return response.data
  },

  create: async (data: CreateStockMovementData): Promise<StockMovement> => {
    const response = await apiClient.post<StockMovement>('/stock-movements/', data)
    return response.data
  },

  getByProduct: async (productId: number): Promise<StockMovement[]> => {
    const response = await apiClient.get<StockMovement[]>(`/stock-movements/`, { 
      params: { product: productId } 
    })
    return response.data
  },
}

// Asset API
export const assetAPI = {
  getAll: async (filters?: AssetFilters): Promise<Asset[]> => {
    const response = await apiClient.get<Asset[]>('/assets/', { params: filters })
    return response.data
  },

  getById: async (id: number): Promise<Asset> => {
    const response = await apiClient.get<Asset>(`/assets/${id}/`)
    return response.data
  },

  create: async (data: CreateAssetData): Promise<Asset> => {
    const response = await apiClient.post<Asset>('/assets/', data)
    return response.data
  },

  update: async (id: number, data: Partial<CreateAssetData>): Promise<Asset> => {
    const response = await apiClient.patch<Asset>(`/assets/${id}/`, data)
    return response.data
  },

  delete: async (id: number): Promise<void> => {
    await apiClient.delete(`/assets/${id}/`)
  },

  assign: async (id: number, employeeId: number): Promise<Asset> => {
    const response = await apiClient.patch<Asset>(`/assets/${id}/`, { assigned_to: employeeId })
    return response.data
  },

  getMyAssets: async (): Promise<Asset[]> => {
    const response = await apiClient.get<Asset[]>('/assets/my-assets/')
    return response.data
  },

  getStats: async (): Promise<{
    total_assets: number
    by_status: Record<string, number>
    by_condition: Record<string, number>
    total_value: number
    maintenance_due: number
  }> => {
    const response = await apiClient.get('/assets/stats/')
    return response.data as {
      total_assets: number
      by_status: Record<string, number>
      by_condition: Record<string, number>
      total_value: number
      maintenance_due: number
    }
  },
}

export default {
  productAPI,
  categoryAPI,
  stockMovementAPI,
  assetAPI,
}
