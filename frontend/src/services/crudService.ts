import React from 'react';
/**
 * Generic CRUD Service
 * Provides standardized Create, Read, Update, Delete operations for all entities
 * ENHANCED: Added comprehensive error handling, validation, and API integration
 */
const API_BASE_URL: any = (import.meta).env.VITE_API_BASE_URL || 'http://localhost:8000/api'

// CRUD Operation Result Types
export interface CrudOperationResult<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Enhanced Error Handling
export class CrudError extends Error {
  constructor(
    message: string,
    public operation: string,
    public endpoint: string,
    public statusCode?: number,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'CrudError'
  }
}

export interface CrudResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
}

export interface CrudFilters {
  search?: string
  category?: string
  status?: string
  department?: string
  employee?: string
  startDate?: string
  endDate?: string
  [key: string]: string | number | boolean | undefined
}

export interface DjangoResponse<T = unknown> {
  results?: T[]
  count?: number
  next?: string | null
  previous?: string | null
}

export interface CrudOptions {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: CrudFilters
}

class CrudService {
  private baseUrl: string
  public endpoint: string

  constructor(endpoint: string) {
    this.endpoint = endpoint
    this.baseUrl = `${API_BASE_URL}/${endpoint}`
  }

  // Generic request method with CSRF protection
  private async request<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    // SECURITY FIX: Remove localStorage token access - use httpOnly cookies
    // Authentication handled automatically via cookies

    // SECURITY FIX: Include CSRF token for non-GET requests
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    // TEMPORARILY DISABLE CSRF PROTECTION TO FIX INFINITE LOOP
    // TODO: Re-implement CSRF protection with proper safeguards
    // const method = options.method?.toUpperCase() || 'GET'
    // if (!['GET', 'HEAD', 'OPTIONS', 'TRACE'].includes(method)) {
    //   try {
    //     const { getCSRFHeadersRedux } = await import('../utils/reduxCSRF')
    //     const csrfHeaders = getCSRFHeadersRedux()
    //     Object.assign(headers, csrfHeaders)
    //   } catch (error) {
    //     console.warn('Failed to get CSRF headers via Redux:', error)
    //   }
    // }

    const config: RequestInit = {
      headers,
      credentials: 'include', // SECURITY FIX: Include httpOnly cookies
      ...options,
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        // Handle 401 Unauthorized - token might be expired
        if (response.status === 401) {
          console.warn('401 Unauthorized - token might be expired. Attempting token refresh...')

          // SECURITY FIX: Don't clear localStorage - we use httpOnly cookies now
          // Try to refresh token first before redirecting
          try {
            // Emit auth error event for centralized handling
            window.dispatchEvent(new CustomEvent('auth:error', {
              detail: { status: 401, message: 'Authentication required' }
            }))
          } catch (refreshError) {
            console.error('Token refresh failed, navigating to login')
            // FIXED: Use navigation service instead of window.location
            window.dispatchEvent(new CustomEvent('app:navigate', {
              detail: { path: '/login' }
            }))
          }

          throw new Error('Authentication required. Please log in again.')
        }

        // Try to get error details from response
        let errorMessage = `HTTP error! status: ${response.status}`
        try {
          const errorData = await response.json()
          console.error('Server error response:', errorData)
          if (errorData.detail) {
            errorMessage = errorData.detail
          } else if (errorData.message) {
            errorMessage = errorData.message
          } else if (typeof errorData === 'object') {
            errorMessage = JSON.stringify(errorData)
          }
        } catch (e) {
          // If response is not JSON, try to get text
          try {
            const errorText = await response.text()
            console.error('Server error text:', errorText)
            if (errorText) {
              errorMessage = errorText
            }
          } catch (e2) {
            // Keep original error message
          }
        }
        throw new Error(errorMessage)
      }

      return await response.json()
    } catch (error) {
      console.error(`API request failed: ${url}`, error)
      throw error
    }
  }

  // ENHANCED: Create (POST) with validation and error handling
  async create<T>(data: Partial<T>): Promise<CrudOperationResult<T>> {
    try {
      // Validate required data
      if (!data || typeof data !== 'object') {
        throw new CrudError('Invalid data provided for creation',
          'create',
          this.endpoint
        )
      }

      console.log(`🔧 Creating ${this.endpoint} with data:`, data)
      console.log(`📡 Endpoint: ${this.baseUrl}/`)

      const result = await this.request<T>(`${this.baseUrl}/`, {
        method: 'POST',
        body: JSON.stringify(data),
      }, 'create')

      console.log(`✅ Successfully created ${this.endpoint}:`, result)

      return {
        success: true,
        data: result,
        message: `${this.endpoint} created successfully`
      }
    } catch (error) {
      console.error(`❌ Failed to create ${this.endpoint}:`, error)

      const errorMessage = error instanceof CrudError
        ? error.message
        : `Failed to create ${this.endpoint}`

      return {
        success: false,
        error: errorMessage,
        message: errorMessage
      }
    }
  }

  // Read (GET) - List with pagination and filters
  async getAll<T>(options: CrudOptions = {}): Promise<CrudResponse<T>> {
    console.log('🔍 CrudService.getAll - Options received:', options)

    const params = new URLSearchParams()

    if (options.page) params.append('page', options.page.toString())
    if (options.pageSize) params.append('page_size', options.pageSize.toString())
    if (options.sortBy) params.append('ordering', options.sortOrder === 'desc' ? `-${options.sortBy}` : options.sortBy)

    // Add filters
    if (options.filters) {
      console.log('🔍 CrudService - Processing filters:', options.filters)
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          console.log(`🔍 CrudService - Adding filter: ${key} = ${value}`)
          params.append(key, value.toString())
        }
      })
    }

    const url = `${this.baseUrl}/${params.toString() ? `?${params.toString()}` : ''}`
    console.log('🔍 CrudService - Final URL:', url)

    const response = await this.request<DjangoResponse>(url)
    console.log('📡 CrudService - Backend response:', response)

    // Transform Django REST Framework response to our CrudResponse format
    if (response.results && Array.isArray(response.results)) {
      return {
        data: response.results as T[],
        total: response.count || response.results.length,
        page: options.page || 1,
        pageSize: options.pageSize || 20 // Always use requested pageSize, not results length
      }
    }

    // Fallback for non-paginated responses
    if (Array.isArray(response)) {
      return {
        data: response,
        total: response.length,
        page: 1,
        pageSize: options.pageSize || 20 // Use requested pageSize, not array length
      }
    }

    // If response already matches our format
    return response as CrudResponse<T>
  }

  // Read (GET) - Single item by ID
  async getById<T>(id: string | number): Promise<T> {
    return this.request<T>(`${this.baseUrl}/${id}/`)
  }

  // ENHANCED: Update (PUT/PATCH) with validation and error handling
  async update<T>(id: string | number, data: Partial<T>, partial = true): Promise<CrudOperationResult<T>> {
    try {
      // Validate ID and data
      if (!id) {
        throw new CrudError('ID is required for update operation',
          'update',
          this.endpoint
        )
      }

      if (!data || typeof data !== 'object') {
        throw new CrudError('Invalid data provided for update',
          'update',
          this.endpoint
        )
      }

      console.log(`🔧 Updating ${this.endpoint} ${id} with data:`, data)

      const result = await this.request<T>(`${this.baseUrl}/${id}/`, {
        method: partial ? 'PATCH' : 'PUT',
        body: JSON.stringify(data),
      }, 'update')

      console.log(`✅ Successfully updated ${this.endpoint} ${id}:`, result)

      return {
        success: true,
        data: result,
        message: `${this.endpoint} updated successfully`
      }
    } catch (error) {
      console.error(`❌ Failed to update ${this.endpoint} ${id}:`, error)

      const errorMessage = error instanceof CrudError
        ? error.message
        : `Failed to update ${this.endpoint}`

      return {
        success: false,
        error: errorMessage,
        message: errorMessage
      }
    }
  }

  // ENHANCED: Delete (DELETE) with validation and error handling
  async delete(id: string | number): Promise<CrudOperationResult<void>> {
    try {
      // Validate ID
      if (!id) {
        throw new CrudError('ID is required for delete operation',
          'delete',
          this.endpoint
        )
      }

      console.log(`🗑️ Deleting ${this.endpoint} ${id}`)

      await this.request<void>(`${this.baseUrl}/${id}/`, {
        method: 'DELETE',
      }, 'delete')

      console.log(`✅ Successfully deleted ${this.endpoint} ${id}`)

      return {
        success: true,
        message: `${this.endpoint} deleted successfully`
      }
    } catch (error) {
      console.error(`❌ Failed to delete ${this.endpoint} ${id}:`, error)

      const errorMessage = error instanceof CrudError
        ? error.message
        : `Failed to delete ${this.endpoint}`

      return {
        success: false,
        error: errorMessage,
        message: errorMessage
      }
    }
  }

  // Bulk operations
  async bulkCreate<T>(items: Partial<T>[]): Promise<T[]> {
    return this.request<T[]>(`${this.baseUrl}/bulk/`, {
      method: 'POST',
      body: JSON.stringify({ items }),
    })
  }

  async bulkUpdate<T>(updates: { id: string | number; data: Partial<T> }[]): Promise<T[]> {
    return this.request<T[]>(`${this.baseUrl}/bulk/`, {
      method: 'PATCH',
      body: JSON.stringify({ updates }),
    })
  }

  async bulkDelete(ids: (string | number)[]): Promise<void> {
    return this.request<void>(`${this.baseUrl}/bulk/`, {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    })
  }

  // Search
  async search<T>(query: string, options: CrudOptions = {}): Promise<CrudResponse<T>> {
    return this.getAll<T>({
      ...options,
      filters: {
        ...options.filters,
        search: query,
      },
    })
  }

  // Export
  async export(format: 'csv' | 'excel' | 'pdf' = 'csv', filters?: CrudFilters): Promise<Blob> {
    const params: any = new URLSearchParams()
    params.append('format', format)

    if (filters) {
      Object.entriesfilters.forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString())
        }
      })
    }

    // SECURITY FIX: Remove localStorage token access - use httpOnly cookies
    // Authentication handled automatically via cookies

    // Extract the resource name from the baseUrl (e.g., 'employees' from '/api/employees')
    const resourceName = this.baseUrl.split('/').pop()

    // Check if backend export endpoint exists for this resource
    const backendExportEndpoints = [
      'employees', 'departments', 'attendance', 'projects', 'tasks',
      'quotations', 'workflows', 'quality-records', 'job-postings',
      'sales-customers', 'sales-orders', 'certifications', 'training-programs',
      'vendors', 'kpis'
    ]

    if (backendExportEndpoints.includes(resourceName || '')) {
      // Use backend export endpoint
      const url = `/api/export/${resourceName}/?${params.toString()}`

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Export failed: ${response.status} - ${errorText}`)
      }
      return response.blob()
    } else {
      // For endpoints without backend export support, return a message
      const message = `Export not available for ${resourceName}. Backend endpoint not implemented.`
      console.warn(message)
      throw new Error(message)
    }
  }

  // Import
  async import(file: File): Promise<{ success: number; errors: string[] }> {
    const formData = new FormData()
    formData.append('file', file)
    const token = localStorage.getItem('access_token') || localStorage.getItem('token')
    const response = await fetch(`${this.baseUrl}/import/`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })
    if (!response.ok) {
      throw new Error(`Import failed: ${response.status}`)
    }
    return response.json()
  }
}

// Factory function to create CRUD services for different entities
export const createCrudService = (endpoint: string) => new CrudService(endpoint)

// Pre-configured services for common entities
// Import employeeAPI directly
import { employeeAPI } from './employeeAPI'

// Special employee service with data transformation
export const employeeService = {
  ...createCrudService('employees'),
  async getAll(params?: CrudOptions) {
    // Transform CrudOptions to EmployeeFiltersType format
    const { filters, ...otherParams } = params || {}
    const employeeFilters = {
      ...otherParams,
      ...filters // Flatten the filters object
    }
    console.log('🔧 EmployeeService - Transforming params:', { original: params, transformed: employeeFilters })
    return employeeAPI.getAll(employeeFilters)
  },
  async getById(id: number) {
    return employeeAPI.getById(id)
  },
  async create(data: Record<string, unknown>) {
    return employeeAPI.create(data)
  },
  async update(id: number, data: Record<string, unknown>) {
    return employeeAPI.update(id, data)
  },
  async delete(id: number) {
    return employeeAPI.delete(id)
  }
}
export const departmentService = createCrudService('departments')
// Removed duplicate - already declared below
export const projectService = createCrudService('projects')
export const taskService = createCrudService('tasks')
export const leaveService = createCrudService('leave-requests')
export const leaveManagementService = createCrudService('leave-requests') // Alternative naming
export const attendanceService = createCrudService('attendance')
export const expenseService = createCrudService('expenses')
export const assetService = createCrudService('assets')
export const supplierService = createCrudService('suppliers')
export const customerService = createCrudService('customers')
export const productService = createCrudService('products')
export const orderService = createCrudService('orders')
export const salesOrderService = createCrudService('sales-orders')
export const quotationService = createCrudService('quotations')
export const leadService = createCrudService('leads')
export const meetingService = createCrudService('meetings')
export const documentService = createCrudService('documents')
export const messageService = createCrudService('messages')
export const announcementService = createCrudService('announcements')
export const reportService = createCrudService('reports')
export const budgetService = createCrudService('budgets')
export const inventoryService = createCrudService('inventory')
export const vendorService = createCrudService('vendors')
export const performanceService = createCrudService('performance-reviews')
export const payrollService = createCrudService('payroll')
export const trainingService = createCrudService('training-programs')
export const complianceService = createCrudService('compliance-audits')
export const workflowService = createCrudService('workflows')

// Additional services for missing entities
export const purchaseOrderService = createCrudService('purchase-orders')
export const invoiceService = createCrudService('invoices')
export const ticketService = createCrudService('tickets')
export const calendarEventService = createCrudService('calendar-events')
export const trainingProgramService = createCrudService('training-programs')
export const certificationService = createCrudService('certifications')
export const complianceAuditService = createCrudService('compliance-audits')
export const riskManagementService = createCrudService('risk-management')
export const knowledgeBaseService = createCrudService('knowledge-base')
export const kpiService = createCrudService('kpi-metrics')
export const jobPostingService = createCrudService('job-postings')
export const jobPostingsService = createCrudService('job-postings') // Alternative naming
// HR Employees service with data transformation (same as employeeService)
export const hrEmployeesService = {
  ...createCrudService('employees'), // Use 'employees' endpoint, not 'hr-employees'
  async getAll(params?: CrudOptions) {
    // Transform CrudOptions to EmployeeFiltersType format
    const { filters, ...otherParams } = params || {}
    const employeeFilters = {
      ...otherParams,
      ...filters // Flatten the filters object
    }
    console.log('🔧 HREmployeesService - Transforming params:', { original: params, transformed: employeeFilters })
    return employeeAPI.getAll(employeeFilters)
  },
  async getById(id: number) {
    return employeeAPI.getById(id)
  },
  async create(data: Record<string, unknown>) {
    return employeeAPI.create(data)
  },
  async update(id: number, data: Record<string, unknown>) {
    return employeeAPI.update(id, data)
  },
  async delete(id: number) {
    return employeeAPI.delete(id)
  }
}
// CRITICAL FIX: Use existing 'departments' endpoint instead of non-existent 'hr-departments'
export const hrDepartmentService = createCrudService('departments')
// CRITICAL FIX: Use existing 'reports' endpoint instead of non-existent 'hr-reports'
export const hrReportService = createCrudService('reports')
export const candidateService = createCrudService('candidates')
export const interviewService = createCrudService('interviews')
export const onboardingService = createCrudService('onboarding')
export const offboardingService = createCrudService('offboarding')
export const timeTrackingService = createCrudService('time-tracking')
export const projectReportService = createCrudService('project-reports')
export const salesReportService = createCrudService('sales-reports')
export const analyticsService = createCrudService('analytics')
export const businessIntelligenceService = createCrudService('business-intelligence')
export const advancedAnalyticsService = createCrudService('advanced-analytics')
export const qualityManagementService = createCrudService('quality-management')
export const qualityRecordService = createCrudService('quality-records') // Alternative naming
export const customerFeedbackService = createCrudService('customer-feedback')
export const auditService = createCrudService('audits')
export const contractService = createCrudService('contracts')
export const policyService = createCrudService('policies')
export const procedureService = createCrudService('procedures')
export const notificationService = createCrudService('notifications')
export const settingsService = createCrudService('settings')
export const userService = createCrudService('users')
export const roleService = createCrudService('roles')
export const permissionService = createCrudService('permissions')
export const integrationService = createCrudService('integrations')
export const backupService = createCrudService('backups')
export const logService = createCrudService('logs')
export const systemHealthService = createCrudService('system-health')

// Personal Services
export const personalProfileService = createCrudService('personal-profiles')
export const personalMessageService = createCrudService('personal-messages')
export const personalCalendarService = createCrudService('personal-calendar')

// Finance Services
export const financeBudgetService = createCrudService('finance-budgets')
export const financeCustomerService = createCrudService('finance-customers')
export const financeReportService = createCrudService('finance-reports')

// Sales Services
export const salesCustomerService = createCrudService('sales-customers')

// KPI Services - FIXED: Create CRUD service for KPI management
export const kpiCrudService = createCrudService('kpi/kpis')

// Department Services
export const departmentProjectService = createCrudService('department-projects')
export const departmentCustomerService = createCrudService('department-customers')

// Employee Services
export const employeeTaskService = createCrudService('employee-tasks')
export const employeeLeaveService = createCrudService('employee-leave')

// Admin Services with Data Transformation
class UserManagementService extends CrudService {
  constructor() {
    super('employees') // Use employees endpoint instead of users
  }

  // Transform backend Employee data to frontend User interface
  private transformEmployeeToUser(employee: any): any {
    // Helper function to create full name with fallbacks
    const createFullName = (firstName: string, lastName: string, username: string, position: string) => {
      const first = firstName?.trim() || ''
      const last = lastName?.trim() || ''

      if (first && last) {
        return `${first} ${last}`
      } else if (first) {
        return first
      } else if (last) {
        return last
      } else if (username) {
        // Use username as fallback, make it more readable
        return username.charAt(0).toUpperCase() + username.slice(1).replace(/([A-Z])/g, ' $1')
      } else if (position) {
        return position
      } else {
        return 'Unknown User'
      }
    }

    // Helper function for Arabic names with fallbacks
    const createArabicName = (firstNameAr: string, lastNameAr: string, englishName: string) => {
      const firstAr = firstNameAr?.trim() || ''
      const lastAr = lastNameAr?.trim() || ''

      if (firstAr && lastAr) {
        return `${firstAr} ${lastAr}`
      } else if (firstAr) {
        return firstAr
      } else if (lastAr) {
        return lastAr
      } else if (englishName && englishName !== 'Unknown User') {
        return englishName // Use English name as fallback
      } else {
        return 'مستخدم غير معروف'
      }
    }

    const fullName = createFullName(employee.user?.first_name,
      employee.user?.last_name,
      employee.user?.username,
      employee.position
    )

    const fullNameAr = createArabicName(employee.first_name_ar,
      employee.last_name_ar,
      fullName
    )

    return {
      id: employee.id,
      fullName,
      fullNameAr,
      email: employee.user?.email || '',
      phone: employee.phone || '',
      role: this.mapRole(employee.user?.is_superuser, employee.user?.is_staff),
      roleAr: this.mapRoleAr(employee.user?.is_superuser, employee.user?.is_staff),
      department: employee.department_name || '',
      departmentAr: employee.department_name_ar || employee.department_name || '',
      status: employee.user?.is_active ? 'active' : 'inactive',
      lastLogin: employee.user?.last_login || 'Never',
      joinDate: employee.hire_date || employee.user?.date_joined?.split('T')[0] || '',
      position: employee.position || '',
      positionAr: employee.position_ar || employee.position || '',
      employeeId: employee.employee_id || '',
      // Keep original data for updates
      _original: employee
    }
  }
  private mapRole(isSuperuser: boolean, isStaff: boolean): string {
    if (isSuperuser) return 'superAdmin'
    if (isStaff) return 'admin'
    return 'employee'
  }
  private mapRoleAr(isSuperuser: boolean, isStaff: boolean): string {
    if (isSuperuser) return 'مدير النظام الأعلى'
    if (isStaff) return 'مدير'
    return 'موظف'
  }

  // Override getAll to transform data
  async getAll<T>(options: CrudOptions = {}): Promise<CrudResponse<T>> {
    const response = await super.getAll<any>(options)

    // The parent CrudService returns data in 'data' property, not 'results'
    const transformedResults = response.data?.map(employee => this.transformEmployeeToUser(employee)) || []

    return {
      ...response,
      data: transformedResults  // Return as 'data' to match parent structure
    }
  }

  // Override get to transform single item
  async get<T>(id: string | number): Promise<T> {
    const employee = await super.get<any>(id)
    return this.transformEmployeeToUser(employee) as T
  }

  // Override create to handle user creation
  async create<T>(data: Partial<T>): Promise<T> {
    // Transform frontend User data to backend Employee data
    const transformedData = this.transformUserToEmployee(data)
    const employee = await super.create<any>(transformedData)
    return this.transformEmployeeToUser(employee) as T
  }

  // Override update to handle user updates
  async update<T>(id: string | number, data: Partial<T>): Promise<T> {
    const transformedData = this.transformUserToEmployee(data)
    const employee = await super.update<any>(id, transformedData)
    return this.transformEmployeeToUser(employee) as T
  }
  private transformUserToEmployee(userData: any): any {
    const [firstName, ...lastNameParts] = (userData.fullName || '').split(' ')
    const lastName = lastNameParts.join(' ')
    const [firstNameAr, ...lastNameArParts] = (userData.fullNameAr || '').split(' ')
    const lastNameAr = lastNameArParts.join(' ')

    return {
      // User fields
      first_name: firstName || '',
      last_name: lastName || '',
      email: userData.email || '',
      username: userData.email || `user_${Date.now()}`,

      // Employee fields
      first_name_ar: firstNameAr || '',
      last_name_ar: lastNameAr || '',
      phone: userData.phone || '',
      position: userData.position || '',
      position_ar: userData.positionAr || userData.position || '',
      hire_date: userData.joinDate || new Date().toISOString().split('T')[0],
      is_active: userData.status === 'active',

      // Generate employee_id if not provided
      employee_id: userData.employee_id || `EMP${Date.now()}`,

      // Default values
      gender: 'M', // Default, should be configurable
      employment_status: 'FULL_TIME'
    }
  }
}
export const userManagementService = new UserManagementService()

export default CrudService
