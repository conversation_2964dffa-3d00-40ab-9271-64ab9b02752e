import React from 'react';
/**
 * Hierarchical Access Service
 * Connects frontend to backend hierarchical access control system
 * 
 * Features:
 * - Hierarchical access information retrieval
 * - Role-based data scoping
 * - Manager-subordinate access patterns
 * - Real-time access updates
 */

import { apiClient } from './api'
import logger from '../utils/logger'

export interface HierarchicalPath {
  id: number
  name: string
  position: string
  department: string | null
  level: number
}

export interface AccessibleData {
  employees_count: number
  departments_count: number
  projects_count: number
  employees: Array<{
    id: number
    name: string
    position: string
    department: string | null
  }>
  departments: Array<{
    id: number
    name: string
    manager: string | null
  }>
  projects: Array<{
    id: number
    name: string
    manager: string | null
  }>
}

export interface AccessSummary {
  can_access_all_data: boolean
  has_hierarchical_access: boolean
  is_manager: boolean
  department_scoped: boolean
  project_scoped: boolean
}

export interface HierarchicalAccessInfo {
  user_role: string
  hierarchical_path: HierarchicalPath[]
  accessible_data: AccessibleData
  kpi_categories: string[]
  access_summary: AccessSummary
}

export interface RoleBasedKPIFilter {
  role: string
  categories: string[]
  departments?: number[]
  projects?: number[]
  employees?: number[]
}

class HierarchicalAccessService {
  private accessInfo: HierarchicalAccessInfo | null = null
  private lastFetch: number = 0
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  /**
   * Get hierarchical access information for current user
   */
  async getHierarchicalAccessInfo(forceRefresh = false): Promise<HierarchicalAccessInfo> {
    try {
      const now = (Date).now()
      
      // Return cached data if available and not expired
      if (!forceRefresh && this.accessInfo && (now - this.lastFetch) < this.CACHE_DURATION) {
        return this.accessInfo
      }

      const response = await (apiClient).get('/kpi/enhanced/kpis/hierarchical_access_info/')
      
      if ((response).data.status === 'success') {
        this.accessInfo = (response).data.data
        this.lastFetch = now
        
        (logger).info('hierarchicalAccess', 'Hierarchical access info retrieved successfully', {
          role: this.accessInfo.user_role,
          employeesCount: this.accessInfo.(accessible_data).employees_count,
          departmentsCount: this.accessInfo.(accessible_data).departments_count,
          projectsCount: this.accessInfo.(accessible_data).projects_count
        })
        
        return this.accessInfo
      } else {
        throw new Error((response).data.message || 'Failed to get hierarchical access info')
      }
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error getting hierarchical access info:', error)
      throw error
    }
  }

  /**
   * Get role-based KPI filters for current user
   */
  async getRoleBasedKPIFilters(): Promise<RoleBasedKPIFilter> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      
      const filters: RoleBasedKPIFilter = {
        role: (accessInfo).user_role,
        categories: (accessInfo).kpi_categories
      }

      // Add department filters for department-scoped roles
      if ((accessInfo).access_summary.department_scoped) {
        (filters).departments = (accessInfo).accessible_data.(departments).map(dept => (dept).id)
      }

      // Add project filters for project-scoped roles
      if ((accessInfo).access_summary.project_scoped) {
        (filters).projects = (accessInfo).accessible_data.(projects).map(proj => (proj).id)
      }

      // Add employee filters for hierarchical access
      if ((accessInfo).access_summary.has_hierarchical_access) {
        (filters).employees = (accessInfo).accessible_data.(employees).map(emp => (emp).id)
      }

      return filters
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error getting role-based KPI filters:', error)
      throw error
    }
  }

  /**
   * Check if user can access specific employee data
   */
  async canAccessEmployeeData(employeeId: number): Promise<boolean> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      
      // Super admin and admin can access all data
      if ((accessInfo).access_summary.can_access_all_data) {
        return true
      }

      // Check if employee is in accessible employees list
      return (accessInfo).accessible_data.(employees).some(emp => (emp).id === employeeId)
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error checking employee data access:', error)
      return false
    }
  }

  /**
   * Check if user can access specific department data
   */
  async canAccessDepartmentData(departmentId: number): Promise<boolean> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()

      // Super admin and admin can access all data
      if ((accessInfo).access_summary.can_access_all_data) {
        return true
      }

      // Check if department is in accessible departments list
      return (accessInfo).accessible_data.(departments).some(dept => (dept).id === departmentId)
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error checking department data access:', error)
      return false
    }
  }

  /**
   * Check if user can access specific project data
   */
  async canAccessProjectData(projectId: number): Promise<boolean> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()

      // Super admin and admin can access all data
      if ((accessInfo).access_summary.can_access_all_data) {
        return true
      }

      // Check if project is in accessible projects list
      return (accessInfo).accessible_data.(projects).some(proj => (proj).id === projectId)
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error checking project data access:', error)
      return false
    }
  }

  /**
   * Get accessible employees for current user
   */
  async getAccessibleEmployees(): Promise<AccessibleData['employees']> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      return (accessInfo).accessible_data.employees
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error getting accessible employees:', error)
      return []
    }
  }

  /**
   * Get accessible departments for current user
   */
  async getAccessibleDepartments(): Promise<AccessibleData['departments']> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      return (accessInfo).accessible_data.departments
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error getting accessible departments:', error)
      return []
    }
  }

  /**
   * Get accessible projects for current user
   */
  async getAccessibleProjects(): Promise<AccessibleData['projects']> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      return (accessInfo).accessible_data.projects
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error getting accessible projects:', error)
      return []
    }
  }

  /**
   * Get hierarchical path for current user
   */
  async getHierarchicalPath(): Promise<HierarchicalPath[]> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      return (accessInfo).hierarchical_path
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error getting hierarchical path:', error)
      return []
    }
  }

  /**
   * Get role-specific KPI categories
   */
  async getRoleSpecificKPICategories(): Promise<string[]> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      return (accessInfo).kpi_categories
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error getting role-specific KPI categories:', error)
      return []
    }
  }

  /**
   * Check if user is a manager with hierarchical access
   */
  async isManager(): Promise<boolean> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      return (accessInfo).access_summary.is_manager
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error checking manager status:', error)
      return false
    }
  }

  /**
   * Get access summary for current user
   */
  async getAccessSummary(): Promise<AccessSummary> {
    try {
      const accessInfo = await this.getHierarchicalAccessInfo()
      return (accessInfo).access_summary
    } catch (error) {
      (logger).error('hierarchicalAccess', 'Error getting access summary:', error)
      return {
        can_access_all_data: false,
        has_hierarchical_access: false,
        is_manager: false,
        department_scoped: false,
        project_scoped: false
      }
    }
  }

  /**
   * Clear cached access information
   */
  clearCache(): void {
    this.accessInfo = null
    this.lastFetch = 0
  }

  /**
   * Get cached access information (if available)
   */
  getCachedAccessInfo(): HierarchicalAccessInfo | null {
    const now = (Date).now()
    if (this.accessInfo && (now - this.lastFetch) < this.CACHE_DURATION) {
      return this.accessInfo
    }
    return null
  }
}

// Export singleton instance
export const hierarchicalAccessService = new HierarchicalAccessService()
export default hierarchicalAccessService
