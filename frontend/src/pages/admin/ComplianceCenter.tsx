import React, { useState } from 'react'
import { 
  Shield, 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  Clock,
  Eye,
  Download,
  Upload,
  Settings,
  Users,
  Lock,
  Key,
  Database,
  Globe,
  Calendar,
  Target,
  Award,
  Zap,
  Activity,
  BarChart3,
  TrendingUp,
  RefreshCw,
  Search,
  Filter
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table'

interface ComplianceCenterProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    complianceCenter: 'مركز الامتثال',
    complianceOverview: 'نظرة عامة على الامتثال',
    regulatoryCompliance: 'الامتثال التنظيمي',
    dataProtection: 'حماية البيانات',
    auditManagement: 'إدارة التدقيق',
    policyManagement: 'إدارة السياسات',
    riskAssessment: 'تقييم المخاطر',
    complianceScore: 'نقاط الامتثال',
    activeViolations: 'الانتهاكات النشطة',
    pendingAudits: 'التدقيقات المعلقة',
    policyUpdates: 'تحديثات السياسات',
    complianceStatus: 'حالة الامتثال',
    gdprCompliance: 'امتثال GDPR',
    isoCompliance: 'امتثال ISO',
    soxCompliance: 'امتثال SOX',
    hipaaCompliance: 'امتثال HIPAA',
    pciCompliance: 'امتثال PCI',
    dataRetention: 'الاحتفاظ بالبيانات',
    dataClassification: 'تصنيف البيانات',
    accessControl: 'التحكم في الوصول',
    encryptionStatus: 'حالة التشفير',
    backupCompliance: 'امتثال النسخ الاحتياطي',
    auditTrail: 'مسار التدقيق',
    complianceReports: 'تقارير الامتثال',
    violationReports: 'تقارير الانتهاكات',
    riskReports: 'تقارير المخاطر',
    policyDocuments: 'وثائق السياسات',
    trainingRecords: 'سجلات التدريب',
    certifications: 'الشهادات',
    lastAudit: 'آخر تدقيق',
    nextAudit: 'التدقيق القادم',
    auditFrequency: 'تكرار التدقيق',
    auditScope: 'نطاق التدقيق',
    auditFindings: 'نتائج التدقيق',
    correctiveActions: 'الإجراءات التصحيحية',
    preventiveActions: 'الإجراءات الوقائية',
    complianceTraining: 'تدريب الامتثال',
    policyAcknowledgment: 'إقرار السياسة',
    incidentReporting: 'الإبلاغ عن الحوادث',
    breachNotification: 'إشعار الانتهاك',
    dataSubjectRights: 'حقوق موضوع البيانات',
    consentManagement: 'إدارة الموافقة',
    vendorCompliance: 'امتثال البائعين',
    thirdPartyRisk: 'مخاطر الطرف الثالث',
    complianceMetrics: 'مقاييس الامتثال',
    kpiTracking: 'تتبع مؤشرات الأداء',
    complianceDashboard: 'لوحة الامتثال',
    regulatoryUpdates: 'التحديثات التنظيمية',
    complianceCalendar: 'تقويم الامتثال',
    deadlineTracking: 'تتبع المواعيد النهائية',
    compliant: 'متوافق',
    nonCompliant: 'غير متوافق',
    partiallyCompliant: 'متوافق جزئياً',
    underReview: 'قيد المراجعة',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    critical: 'حرج',
    resolved: 'محلول',
    pending: 'معلق',
    inProgress: 'قيد التنفيذ',
    overdue: 'متأخر',
    upcoming: 'قادم',
    excellent: 'ممتاز',
    good: 'جيد',
    needsImprovement: 'يحتاج تحسين',
    viewDetails: 'عرض التفاصيل',
    generateReport: 'إنشاء تقرير',
    scheduleAudit: 'جدولة تدقيق',
    updatePolicy: 'تحديث السياسة',
    assignTraining: 'تعيين تدريب',
    reviewCompliance: 'مراجعة الامتثال'
  },
  en: {
    complianceCenter: 'Compliance Center',
    complianceOverview: 'Compliance Overview',
    regulatoryCompliance: 'Regulatory Compliance',
    dataProtection: 'Data Protection',
    auditManagement: 'Audit Management',
    policyManagement: 'Policy Management',
    riskAssessment: 'Risk Assessment',
    complianceScore: 'Compliance Score',
    activeViolations: 'Active Violations',
    pendingAudits: 'Pending Audits',
    policyUpdates: 'Policy Updates',
    complianceStatus: 'Compliance Status',
    gdprCompliance: 'GDPR Compliance',
    isoCompliance: 'ISO Compliance',
    soxCompliance: 'SOX Compliance',
    hipaaCompliance: 'HIPAA Compliance',
    pciCompliance: 'PCI Compliance',
    dataRetention: 'Data Retention',
    dataClassification: 'Data Classification',
    accessControl: 'Access Control',
    encryptionStatus: 'Encryption Status',
    backupCompliance: 'Backup Compliance',
    auditTrail: 'Audit Trail',
    complianceReports: 'Compliance Reports',
    violationReports: 'Violation Reports',
    riskReports: 'Risk Reports',
    policyDocuments: 'Policy Documents',
    trainingRecords: 'Training Records',
    certifications: 'Certifications',
    lastAudit: 'Last Audit',
    nextAudit: 'Next Audit',
    auditFrequency: 'Audit Frequency',
    auditScope: 'Audit Scope',
    auditFindings: 'Audit Findings',
    correctiveActions: 'Corrective Actions',
    preventiveActions: 'Preventive Actions',
    complianceTraining: 'Compliance Training',
    policyAcknowledgment: 'Policy Acknowledgment',
    incidentReporting: 'Incident Reporting',
    breachNotification: 'Breach Notification',
    dataSubjectRights: 'Data Subject Rights',
    consentManagement: 'Consent Management',
    vendorCompliance: 'Vendor Compliance',
    thirdPartyRisk: 'Third Party Risk',
    complianceMetrics: 'Compliance Metrics',
    kpiTracking: 'KPI Tracking',
    complianceDashboard: 'Compliance Dashboard',
    regulatoryUpdates: 'Regulatory Updates',
    complianceCalendar: 'Compliance Calendar',
    deadlineTracking: 'Deadline Tracking',
    compliant: 'Compliant',
    nonCompliant: 'Non-Compliant',
    partiallyCompliant: 'Partially Compliant',
    underReview: 'Under Review',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    critical: 'Critical',
    resolved: 'Resolved',
    pending: 'Pending',
    inProgress: 'In Progress',
    overdue: 'Overdue',
    upcoming: 'Upcoming',
    excellent: 'Excellent',
    good: 'Good',
    needsImprovement: 'Needs Improvement',
    viewDetails: 'View Details',
    generateReport: 'Generate Report',
    scheduleAudit: 'Schedule Audit',
    updatePolicy: 'Update Policy',
    assignTraining: 'Assign Training',
    reviewCompliance: 'Review Compliance'
  }
}
export default function ComplianceCenter({ language }: ComplianceCenterProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  // Compliance state
  const [complianceData, setComplianceData] = useState({
    overview: {
      complianceScore: 92,
      activeViolations: 2,
      pendingAudits: 3,
      policyUpdates: 5,
      lastAuditDate: '2024-01-15',
      nextAuditDate: '2024-04-15'
    },
    regulations: [
      { name: 'GDPR', status: 'compliant', score: 95, lastReview: '2024-01-10' },
      { name: 'ISO 27001', status: 'compliant', score: 88, lastReview: '2024-01-05' },
      { name: 'SOX', status: 'partiallyCompliant', score: 78, lastReview: '2023-12-20' },
      { name: 'HIPAA', status: 'compliant', score: 92, lastReview: '2024-01-08' },
      { name: 'PCI DSS', status: 'underReview', score: 85, lastReview: '2023-12-15' }
    ],
    violations: [
      {
        id: 1,
        type: 'Data Retention',
        severity: 'medium',
        description: 'Some user data retained beyond policy limits',
        status: 'inProgress',
        dueDate: '2024-02-15',
        assignee: 'Data Protection Officer'
      },
      {
        id: 2,
        type: 'Access Control',
        severity: 'high',
        description: 'Privileged access not properly reviewed',
        status: 'pending',
        dueDate: '2024-02-10',
        assignee: 'Security Team'
      }
    ],
    audits: [
      {
        id: 1,
        name: 'Annual Security Audit',
        type: 'Security',
        status: 'upcoming',
        scheduledDate: '2024-03-01',
        auditor: 'External Auditor',
        scope: 'Full System'
      },
      {
        id: 2,
        name: 'GDPR Compliance Review',
        type: 'Privacy',
        status: 'inProgress',
        scheduledDate: '2024-02-15',
        auditor: 'Internal Team',
        scope: 'Data Processing'
      },
      {
        id: 3,
        name: 'Financial Controls Audit',
        type: 'Financial',
        status: 'pending',
        scheduledDate: '2024-04-01',
        auditor: 'External Auditor',
        scope: 'Financial Systems'
      }
    ],
    policies: [
      { name: 'Data Privacy Policy', version: '(2).1', lastUpdated: '2024-01-10', status: 'active' },
      { name: 'Information Security Policy', version: '(3).0', lastUpdated: '2024-01-05', status: 'active' },
      { name: 'Access Control Policy', version: '1.8', lastUpdated: '2023-12-20', status: 'needsUpdate' },
      { name: 'Incident Response Policy', version: '(2).3', lastUpdated: '2024-01-08', status: 'active' },
      { name: 'Business Continuity Policy', version: '1.5', lastUpdated: '2023-11-15', status: 'needsUpdate' }
    ]
  })

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'compliant': case 'active': case 'resolved': return 'text-green-500 bg-green-500/20'
      case 'partiallyCompliant': case 'inProgress': case 'needsUpdate': return 'text-yellow-500 bg-yellow-500/20'
      case 'nonCompliant': case 'overdue': case 'critical': return 'text-red-500 bg-red-500/20'
      case 'underReview': case 'pending': case 'upcoming': return 'text-blue-500 bg-blue-500/20'
      default: return 'text-gray-500 bg-gray-500/20'
    }
  }

  const getSeverityColor = (severity: string): void => {
    switch (severity) {
      case 'critical': return 'text-red-500 bg-red-500/20'
      case 'high': return 'text-orange-500 bg-orange-500/20'
      case 'medium': return 'text-yellow-500 bg-yellow-500/20'
      case 'low': return 'text-green-500 bg-green-500/20'
      default: return 'text-gray-500 bg-gray-500/20'
    }
  }

  return (<div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t.complianceCenter}</h1>
              <p className="text-white/70">Comprehensive compliance management and regulatory oversight</p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <Download className="h-4 w-4 mr-2" />
                {t.generateReport}
              </Button>
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Compliance Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.complianceScore}</p>
                  <p className="text-2xl font-bold text-green-400">{complianceData.overview.complianceScore}%</p>
                  <p className="text-xs text-white/50">{t.excellent}</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeViolations}</p>
                  <p className="text-2xl font-bold text-yellow-400">{complianceData.overview.activeViolations}</p>
                  <p className="text-xs text-white/50">{t.medium} Risk</p>
                </div>
                <div className="p-3 rounded-lg bg-yellow-500">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.pendingAudits}</p>
                  <p className="text-2xl font-bold text-blue-400">{complianceData.overview.pendingAudits}</p>
                  <p className="text-xs text-white/50">Scheduled</p>
                </div>
                <div className="p-3 rounded-lg bg-blue-500">
                  <FileText className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.policyUpdates}</p>
                  <p className="text-2xl font-bold text-purple-400">{complianceData.overview.policyUpdates}</p>
                  <p className="text-xs text-white/50">This Month</p>
                </div>
                <div className="p-3 rounded-lg bg-purple-500">
                  <Settings className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-white/10 backdrop-blur-xl border-white/20">
            <TabsTrigger value="overview" className="data-[state=active]:bg-white/20 text-white">
              <BarChart3 className="h-4 w-4 mr-2" />
              {t.complianceOverview}
            </TabsTrigger>
            <TabsTrigger value="regulations" className="data-[state=active]:bg-white/20 text-white">
              <Shield className="h-4 w-4 mr-2" />
              {t.regulatoryCompliance}
            </TabsTrigger>
            <TabsTrigger value="audits" className="data-[state=active]:bg-white/20 text-white">
              <FileText className="h-4 w-4 mr-2" />
              {t.auditManagement}
            </TabsTrigger>
            <TabsTrigger value="policies" className="data-[state=active]:bg-white/20 text-white">
              <Settings className="h-4 w-4 mr-2" />
              {t.policyManagement}
            </TabsTrigger>
          </TabsList>

          {/* Compliance Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Compliance Score Breakdown */}
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Compliance Score Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {complianceData.regulations.map((reg, index) => (<div key={index} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-white">{reg.name}</span>
                          <span className="text-white/70">{reg.score}%</span>
                        </div>
                        <Progress value={reg.score} className="h-2" />
                        <div className="flex justify-between items-center">
                          <Badge className={getStatusColor(reg.status)}>
                            {t[reg.status as keyof typeof t]}
                          </Badge>
                          <span className="text-xs text-white/50">Last review: {reg.lastReview}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Violations */}
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Active Violations</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {complianceData.violations.map((violation) => (<div key={violation.id} className="p-4 rounded-lg bg-white/5 border border-white/10">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="text-white font-medium">{violation.type}</h4>
                          <Badge className={getSeverityColor(violation.severity)}>
                            {t[violation.severity as keyof typeof t]}
                          </Badge>
                        </div>
                        <p className="text-white/70 text-sm mb-3">{violation.description}</p>
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-white/50">Due: {violation.dueDate}</span>
                          <Badge className={getStatusColor(violation.status)}>
                            {t[violation.status as keyof typeof t]}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Regulatory Compliance Tab */}
          <TabsContent value="regulations" className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white">{t.regulatoryCompliance}</CardTitle>
                <CardDescription className="text-white/70">
                  Current compliance status across all regulations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/20">
                      <TableHead className="text-white">Regulation</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Score</TableHead>
                      <TableHead className="text-white">Last Review</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {complianceData.regulations.map((reg, index) => (<TableRow key={index} className="border-white/10">
                        <TableCell className="text-white font-medium">{reg.name}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(reg.status)}>
                            {t[reg.status as keyof typeof t]}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-white">{reg.score}%</TableCell>
                        <TableCell className="text-white">{reg.lastReview}</TableCell>
                        <TableCell>
                          <Button size="sm" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                            {t.viewDetails}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Audit Management Tab */}
          <TabsContent value="audits" className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white">{t.auditManagement}</CardTitle>
                <CardDescription className="text-white/70">
                  Scheduled and ongoing audits
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/20">
                      <TableHead className="text-white">Audit Name</TableHead>
                      <TableHead className="text-white">Type</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Date</TableHead>
                      <TableHead className="text-white">Auditor</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {complianceData.audits.map((audit) => (<TableRow key={audit.id} className="border-white/10">
                        <TableCell className="text-white font-medium">{audit.name}</TableCell>
                        <TableCell className="text-white">{audit.type}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(audit.status)}>
                            {t[audit.status as keyof typeof t]}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-white">{audit.scheduledDate}</TableCell>
                        <TableCell className="text-white">{audit.auditor}</TableCell>
                        <TableCell>
                          <Button size="sm" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                            {t.viewDetails}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Policy Management Tab */}
          <TabsContent value="policies" className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white">{t.policyManagement}</CardTitle>
                <CardDescription className="text-white/70">
                  Organizational policies and procedures
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/20">
                      <TableHead className="text-white">Policy Name</TableHead>
                      <TableHead className="text-white">Version</TableHead>
                      <TableHead className="text-white">Last Updated</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                      <TableHead className="text-white">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {complianceData.policies.map((policy, index) => (<TableRow key={index} className="border-white/10">
                        <TableCell className="text-white font-medium">{policy.name}</TableCell>
                        <TableCell className="text-white">{policy.version}</TableCell>
                        <TableCell className="text-white">{policy.lastUpdated}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(policy.status)}>
                            {t[policy.status as keyof typeof t]}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                              {t.viewDetails}
                            </Button>
                            {policy.status === 'needsUpdate' && (<Button size="sm" className="bg-yellow-500 hover:bg-yellow-600 text-white">
                                {t.updatePolicy}
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
