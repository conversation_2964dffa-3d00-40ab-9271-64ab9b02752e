import React, { useState, useEffect } from 'react'
import {
  Server,
  Database,
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  Settings,
  Users,
  Lock,
  Eye,
  RefreshCw,
  HardDrive,
  Cpu,
  MemoryStick,
  Network,
  Globe,
  Key,
  FileText,
  Clock,
  TrendingUp,
  BarChart3,
  Zap,
  Bug,
  Wrench,
  Monitor,
  Terminal,
  Gauge,
  Power,
  Wifi
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import EmployeeOnboardingStatus from '../../components/EmployeeOnboardingStatus'

interface PureSuperAdminDashboardProps {
  language: 'ar' | 'en'
}

const translations: any = {
  ar: {
    systemAdministration: 'إدارة النظام',
    systemOverview: 'نظرة عامة على النظام',
    systemHealth: 'صحة النظام',
    securityStatus: 'حالة الأمان',
    serverMetrics: 'مقاييس الخادم',
    databaseStatus: 'حالة قاعدة البيانات',
    systemLogs: 'سجلات النظام',
    activeUsers: 'المستخدمون النشطون',
    systemUptime: 'وقت تشغيل النظام',
    cpuUsage: 'استخدام المعالج',
    memoryUsage: 'استخدام الذاكرة',
    diskUsage: 'استخدام القرص',
    networkTraffic: 'حركة الشبكة',
    databaseSize: 'حجم قاعدة البيانات',
    activeConnections: 'الاتصالات النشطة',
    securityScore: 'نقاط الأمان',
    activeThreats: 'التهديدات النشطة',
    blockedAttacks: 'الهجمات المحجوبة',
    failedLogins: 'محاولات تسجيل الدخول الفاشلة',
    systemErrors: 'أخطاء النظام',
    backupStatus: 'حالة النسخ الاحتياطي',
    lastBackup: 'آخر نسخة احتياطية',
    apiRequests: 'طلبات API',
    responseTime: 'وقت الاستجابة',
    healthy: 'سليم',
    warning: 'تحذير',
    critical: 'حرج',
    excellent: 'ممتاز',
    good: 'جيد',
    poor: 'ضعيف',
    online: 'متصل',
    offline: 'غير متصل',
    enabled: 'مفعل',
    disabled: 'معطل',
    refresh: 'تحديث',
    configure: 'تكوين',
    monitor: 'مراقبة',
    manage: 'إدارة',
    quickActions: 'الإجراءات السريعة',
    systemSettings: 'إعدادات النظام',
    securityCenter: 'مركز الأمان',
    userManagement: 'إدارة المستخدمين',
    backupManagement: 'إدارة النسخ الاحتياطي',
    logViewer: 'عارض السجلات',
    systemMaintenance: 'صيانة النظام',
    employeeOnboarding: 'تفعيل الموظفين'
  },
  en: {
    systemAdministration: 'System Administration',
    systemOverview: 'System Overview',
    systemHealth: 'System Health',
    securityStatus: 'Security Status',
    serverMetrics: 'Server Metrics',
    databaseStatus: 'Database Status',
    systemLogs: 'System Logs',
    activeUsers: 'Active Users',
    systemUptime: 'System Uptime',
    cpuUsage: 'CPU Usage',
    memoryUsage: 'Memory Usage',
    diskUsage: 'Disk Usage',
    networkTraffic: 'Network Traffic',
    databaseSize: 'Database Size',
    activeConnections: 'Active Connections',
    securityScore: 'Security Score',
    activeThreats: 'Active Threats',
    blockedAttacks: 'Blocked Attacks',
    failedLogins: 'Failed Logins',
    systemErrors: 'System Errors',
    backupStatus: 'Backup Status',
    lastBackup: 'Last Backup',
    apiRequests: 'API Requests',
    responseTime: 'Response Time',
    healthy: 'Healthy',
    warning: 'Warning',
    critical: 'Critical',
    excellent: 'Excellent',
    good: 'Good',
    poor: 'Poor',
    online: 'Online',
    offline: 'Offline',
    enabled: 'Enabled',
    disabled: 'Disabled',
    refresh: 'Refresh',
    configure: 'Configure',
    monitor: 'Monitor',
    manage: 'Manage',
    quickActions: 'Quick Actions',
    systemSettings: 'System Settings',
    securityCenter: 'Security Center',
    userManagement: 'User Management',
    backupManagement: 'Backup Management',
    logViewer: 'Log Viewer',
    systemMaintenance: 'System Maintenance',
    employeeOnboarding: 'Employee Onboarding'
  }
}
export default function PureSuperAdminDashboard({ language }: PureSuperAdminDashboardProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  // Real system metrics state
  interface SystemMetricsState {
    systemHealth: string;
    uptime: string;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    memoryTotal: number;
    diskTotal: number;
    networkSent: number;
    networkRecv: number;
    databaseSize: string;
    activeConnections: number;
    totalTables: number;
    totalSystemUsers: number;
    activeSessions: number;
    activeUsers24h: number;
    superadminUsers: number;
    adminUsers: number;
    securityScore: number;
    activeThreats: number;
    blockedAttacks: number;
    failedLogins: number;
    systemInfo: {
      platform: string;
      pythonVersion: string;
      hostname: string;
    };
    lastUpdated: Date | null;
    loading: boolean;
    error: string | null;
  }

  const [systemMetrics, setSystemMetrics] = useState<SystemMetricsState>({
    systemHealth: 'loading',
    uptime: 'Loading...',
    cpuUsage: 0,
    memoryUsage: 0,
    diskUsage: 0,
    memoryTotal: 0,
    diskTotal: 0,
    networkSent: 0,
    networkRecv: 0,
    databaseSize: 'Loading...',
    activeConnections: 0,
    totalTables: 0,
    totalSystemUsers: 0,
    activeSessions: 0,
    activeUsers24h: 0,
    superadminUsers: 0,
    adminUsers: 0,
    securityScore: 0,
    activeThreats: 0,
    blockedAttacks: 0,
    failedLogins: 0,
    systemInfo: {
      platform: 'Loading...',
      pythonVersion: 'Loading...',
      hostname: 'Loading...'
    },
    lastUpdated: null,
    loading: true,
    error: null
  })

  // Fetch real system data
  const fetchSystemStats = async () => {
    try {
      const token = localStorage.getItem('access_token')
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
      const response = await fetch(`${API_BASE_URL}/superadmin/system-stats/`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()

      setSystemMetrics({
        systemHealth: data.system_health.status,
        uptime: data.system_health.uptime,
        cpuUsage: data.server_metrics.cpu_usage,
        memoryUsage: data.server_metrics.memory_usage,
        diskUsage: data.server_metrics.disk_usage,
        memoryTotal: data.server_metrics.memory_total,
        diskTotal: data.server_metrics.disk_total,
        networkSent: data.server_metrics.network_sent,
        networkRecv: data.server_metrics.network_recv,
        databaseSize: data.database_stats.size,
        activeConnections: data.database_stats.active_connections,
        totalTables: data.database_stats.total_tables,
        totalSystemUsers: data.user_stats.total_system_users,
        activeSessions: data.user_stats.active_sessions,
        activeUsers24h: data.user_stats.active_users_24h,
        superadminUsers: data.user_stats.superadmin_users,
        adminUsers: data.user_stats.admin_users,
        securityScore: data.security_stats.security_score,
        activeThreats: data.security_stats.active_threats,
        blockedAttacks: data.security_stats.blocked_attacks,
        failedLogins: data.security_stats.failed_logins_24h,
        systemInfo: data.system_info,
        lastUpdated: data.last_updated,
        loading: false,
        error: null
      })
    } catch (error) {
      console.error('Failed to fetch system stats:', error)
      setSystemMetrics(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load system data'
      }))
    }
  }

  // Initial data fetch and real-time updates
  useEffect(() => {
    // Fetch data immediately
    fetchSystemStats()

    // Set up interval for real-time updates every 30 seconds
    const interval = setInterval(() => {
      fetchSystemStats()
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'healthy': case 'excellent': case 'online': case 'success': return 'text-green-500'
      case 'warning': case 'good': return 'text-yellow-500'
      case 'critical': case 'poor': case 'offline': case 'error': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getStatusBadge = (status: string): void => {
    switch (status) {
      case 'healthy': case 'excellent': case 'online': case 'success': return 'bg-green-500'
      case 'warning': case 'good': return 'bg-yellow-500'
      case 'critical': case 'poor': case 'offline': case 'error': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  return (<div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t.systemAdministration}</h1>
              <p className="text-white/70">{t.systemOverview}</p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <RefreshCw className="h-4 w-4 mr-2" />
                {t.refresh}
              </Button>
              <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                <Settings className="h-4 w-4 mr-2" />
                {t.systemSettings}
              </Button>
            </div>
          </div>
        </div>

        {/* Error State */}
        {systemMetrics.error && (<div className="mb-8 p-4 rounded-lg bg-red-500/20 border border-red-500/50">
            <p className="text-red-300">Error loading system data: {systemMetrics.error}</p>
            <Button
              onClick={fetchSystemStats}
              className="mt-2 bg-red-500 hover:bg-red-600 text-white"
            >
              Retry
            </Button>
          </div>
        )}

        {/* System Health Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.systemHealth}</p>
                  {systemMetrics.loading ? (
                    <p className="text-2xl font-bold text-gray-400">Loading...</p>
                  ) : (<p className={`text-2xl font-bold ${getStatusColor(systemMetrics.systemHealth)}`}>
                      {t[systemMetrics.systemHealth as keyof typeof t] || systemMetrics.systemHealth}
                    </p>
                  )}
                </div>
                <div className={`p-3 rounded-lg ${systemMetrics.loading ? 'bg-gray-500' : getStatusBadge(systemMetrics.systemHealth)}`}>
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.securityScore}</p>
                  <p className="text-2xl font-bold text-green-400">
                    {systemMetrics.loading ? 'Loading...' : `${systemMetrics.securityScore}/100`}
                  </p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.systemUptime}</p>
                  <p className="text-2xl font-bold text-blue-400">
                    {systemMetrics.loading ? 'Loading...' : systemMetrics.uptime}
                  </p>
                </div>
                <div className="p-3 rounded-lg bg-blue-500">
                  <Clock className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">System Users (24h)</p>
                  <p className="text-2xl font-bold text-purple-400">
                    {systemMetrics.loading ? 'Loading...' : systemMetrics.activeUsers24h}
                  </p>
                </div>
                <div className="p-3 rounded-lg bg-purple-500">
                  <Users className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Server Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Cpu className="h-5 w-5" />
                {t.cpuUsage}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Current</span>
                  <span className="text-white">
                    {systemMetrics.loading ? 'Loading...' : `${systemMetrics.cpuUsage}%`}
                  </span>
                </div>
                <Progress value={systemMetrics.loading ? 0 : systemMetrics.cpuUsage} className="h-2" />
                <div className="text-xs text-white/50">
                  {systemMetrics.loading ? 'Loading...' :
                   systemMetrics.cpuUsage < 70 ? t.good :
                   systemMetrics.cpuUsage < 90 ? t.warning : t.critical}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <MemoryStick className="h-5 w-5" />
                {t.memoryUsage}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Current</span>
                  <span className="text-white">
                    {systemMetrics.loading ? 'Loading...' : `${systemMetrics.memoryUsage}%`}
                  </span>
                </div>
                <Progress value={systemMetrics.loading ? 0 : systemMetrics.memoryUsage} className="h-2" />
                <div className="text-xs text-white/50">
                  {systemMetrics.loading ? 'Loading...' :
                   systemMetrics.memoryUsage < 70 ? t.good :
                   systemMetrics.memoryUsage < 90 ? t.warning : t.critical}
                </div>
                <div className="text-xs text-white/40 mt-1">
                  {systemMetrics.loading ? '' : `Total: ${systemMetrics.memoryTotal} GB`}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <HardDrive className="h-5 w-5" />
                {t.diskUsage}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Current</span>
                  <span className="text-white">
                    {systemMetrics.loading ? 'Loading...' : `${systemMetrics.diskUsage}%`}
                  </span>
                </div>
                <Progress value={systemMetrics.loading ? 0 : systemMetrics.diskUsage} className="h-2" />
                <div className="text-xs text-white/50">
                  {systemMetrics.loading ? 'Loading...' :
                   systemMetrics.diskUsage < 70 ? t.good :
                   systemMetrics.diskUsage < 90 ? t.warning : t.critical}
                </div>
                <div className="text-xs text-white/40 mt-1">
                  {systemMetrics.loading ? '' : `Total: ${systemMetrics.diskTotal} GB`}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Status Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Database className="h-5 w-5" />
                {t.databaseStatus}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-white/70">{t.databaseSize}</span>
                <span className="text-white font-semibold">
                  {systemMetrics.loading ? 'Loading...' : systemMetrics.databaseSize}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">{t.activeConnections}</span>
                <span className="text-white font-semibold">
                  {systemMetrics.loading ? 'Loading...' : systemMetrics.activeConnections}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Total Tables</span>
                <span className="text-white font-semibold">
                  {systemMetrics.loading ? 'Loading...' : systemMetrics.totalTables}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Status</span>
                <Badge className="bg-green-500">{t.online}</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {t.securityStatus}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-white/70">{t.activeThreats}</span>
                <Badge className="bg-green-500">
                  {systemMetrics.loading ? 'Loading...' : systemMetrics.activeThreats}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">{t.blockedAttacks}</span>
                <Badge className="bg-blue-500">
                  {systemMetrics.loading ? 'Loading...' : systemMetrics.blockedAttacks}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">{t.failedLogins}</span>
                <Badge className="bg-yellow-500">
                  {systemMetrics.loading ? 'Loading...' : systemMetrics.failedLogins}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Active Sessions</span>
                <Badge className="bg-purple-500">
                  {systemMetrics.loading ? 'Loading...' : systemMetrics.activeSessions}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="bg-white/10 backdrop-blur-xl border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Zap className="h-5 w-5" />
              {t.quickActions}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <Button className="bg-blue-500 hover:bg-blue-600 text-white flex flex-col items-center p-4 h-auto">
                <Settings className="h-6 w-6 mb-2" />
                <span className="text-xs">{t.systemSettings}</span>
              </Button>
              <Button className="bg-red-500 hover:bg-red-600 text-white flex flex-col items-center p-4 h-auto">
                <Shield className="h-6 w-6 mb-2" />
                <span className="text-xs">{t.securityCenter}</span>
              </Button>
              <Button className="bg-purple-500 hover:bg-purple-600 text-white flex flex-col items-center p-4 h-auto">
                <Users className="h-6 w-6 mb-2" />
                <span className="text-xs">{t.userManagement}</span>
              </Button>
              <Button className="bg-green-500 hover:bg-green-600 text-white flex flex-col items-center p-4 h-auto">
                <HardDrive className="h-6 w-6 mb-2" />
                <span className="text-xs">{t.backupManagement}</span>
              </Button>
              <Button className="bg-orange-500 hover:bg-orange-600 text-white flex flex-col items-center p-4 h-auto">
                <FileText className="h-6 w-6 mb-2" />
                <span className="text-xs">{t.logViewer}</span>
              </Button>
              <Button className="bg-yellow-500 hover:bg-yellow-600 text-white flex flex-col items-center p-4 h-auto">
                <Wrench className="h-6 w-6 mb-2" />
                <span className="text-xs">{t.systemMaintenance}</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Employee Onboarding Status */}
        <Card className="bg-white/10 backdrop-blur-xl border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Users className="h-5 w-5" />
              {t.employeeOnboarding}
            </CardTitle>
            <CardDescription className="text-white/70">
              {language === 'ar' ? 'حالة تفعيل حسابات الموظفين الجدد' : 'New employee account activation status'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <EmployeeOnboardingStatus />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
