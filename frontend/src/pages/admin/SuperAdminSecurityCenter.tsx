import React, { useState, useEffect } from 'react'
import { 
  Shield, 
  Lock, 
  Key, 
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  Users,
  Clock,
  Activity,
  Ban,
  UserX,
  Settings,
  RefreshCw,
  Download,
  Search,
  Filter,
  Bell,
  Zap,
  Globe,
  Wifi,
  Server
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { Switch } from '../../components/ui/switch'
import { Label } from '../../components/ui/label'
import { Input } from '../../components/ui/input'

interface SuperAdminSecurityCenterProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    securityCenter: 'مركز الأمان',
    securityOverview: 'نظرة عامة على الأمان',
    twoFactorAuth: 'المصادقة الثنائية',
    passwordPolicies: 'سياسات كلمة المرور',
    sessionManagement: 'إدارة الجلسات',
    securityAlerts: 'تنبيهات الأمان',
    accessControl: 'التحكم في الوصول',
    securityLogs: 'سجلات الأمان',
    threatDetection: 'كشف التهديدات',
    securityScore: 'نقاط الأمان',
    activeThreats: 'التهديدات النشطة',
    blockedAttacks: 'الهجمات المحجوبة',
    failedLogins: 'محاولات تسجيل الدخول الفاشلة',
    activeSessions: 'الجلسات النشطة',
    suspiciousActivity: 'النشاط المشبوه',
    securityEvents: 'أحداث الأمان',
    ipBlacklist: 'القائمة السوداء للـ IP',
    userPermissions: 'أذونات المستخدمين',
    auditTrail: 'مسار التدقيق',
    securitySettings: 'إعدادات الأمان',
    enabled: 'مفعل',
    disabled: 'معطل',
    active: 'نشط',
    blocked: 'محجوب',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    critical: 'حرج',
    warning: 'تحذير',
    info: 'معلومات',
    configure: 'تكوين',
    monitor: 'مراقبة',
    block: 'حجب',
    unblock: 'إلغاء الحجب',
    refresh: 'تحديث',
    export: 'تصدير'
  },
  en: {
    securityCenter: 'Security Center',
    securityOverview: 'Security Overview',
    twoFactorAuth: 'Two-Factor Authentication',
    passwordPolicies: 'Password Policies',
    sessionManagement: 'Session Management',
    securityAlerts: 'Security Alerts',
    accessControl: 'Access Control',
    securityLogs: 'Security Logs',
    threatDetection: 'Threat Detection',
    securityScore: 'Security Score',
    activeThreats: 'Active Threats',
    blockedAttacks: 'Blocked Attacks',
    failedLogins: 'Failed Logins',
    activeSessions: 'Active Sessions',
    suspiciousActivity: 'Suspicious Activity',
    securityEvents: 'Security Events',
    ipBlacklist: 'IP Blacklist',
    userPermissions: 'User Permissions',
    auditTrail: 'Audit Trail',
    securitySettings: 'Security Settings',
    enabled: 'Enabled',
    disabled: 'Disabled',
    active: 'Active',
    blocked: 'Blocked',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    critical: 'Critical',
    warning: 'Warning',
    info: 'Info',
    configure: 'Configure',
    monitor: 'Monitor',
    block: 'Block',
    unblock: 'Unblock',
    refresh: 'Refresh',
    export: 'Export'
  }
}
export default function SuperAdminSecurityCenter({ language }: SuperAdminSecurityCenterProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  // Security state
  const [securityData, setSecurityData] = useState({
    securityScore: 95,
    activeThreats: 0,
    blockedAttacks: 127,
    failedLogins: 8,
    activeSessions: 45,
    suspiciousActivity: 2,
    twoFactorEnabled: true,
    passwordPolicyStrict: true,
    sessionTimeoutEnabled: true,
    ipBlacklistEnabled: true,
    threatDetectionEnabled: true,
    auditLoggingEnabled: true
  })

  const [securityAlerts] = useState([
    { id: 1, type: 'warning', message: 'Multiple failed login attempts from IP (192).168.1.100', time: '5 minutes ago' },
    { id: 2, type: 'info', message: 'New user registration from unusual location', time: '15 minutes ago' },
    { id: 3, type: 'critical', message: 'Potential SQL injection attempt blocked', time: '1 hour ago' }
  ])

  const [activeSessions] = useState([
    { id: 1, user: '<EMAIL>', ip: '(192).168.1.50', location: 'Office', device: 'Chrome/Windows', lastActivity: '2 minutes ago' },
    { id: 2, user: '<EMAIL>', ip: '(192).168.1.75', location: 'Office', device: 'Safari/MacOS', lastActivity: '5 minutes ago' },
    { id: 3, user: '<EMAIL>', ip: '(10).0.0.25', location: 'Remote', device: 'Firefox/Linux', lastActivity: '10 minutes ago' }
  ])

  // Real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSecurityData(prev => ({
        ...prev,
        blockedAttacks: prev.blockedAttacks + Math.floor(Math.random() * 3),
        failedLogins: Math.max(0, prev.failedLogins + Math.floor(Math.random() * 4 - 2)),
        activeSessions: Math.max(0, prev.activeSessions + Math.floor(Math.random() * 6 - 3))
      }))
    }, 10000)
    return () => clearInterval(interval)
  }, [])

  const handleToggle = (setting: string): void => {
    setSecurityData(prev => ({
      ...prev,
      [setting]: !prev[setting as keyof typeof prev]
    }))
  }

  const getAlertColor = (type: string): void => {
    switch (type) {
      case 'critical': return 'bg-red-500'
      case 'warning': return 'bg-yellow-500'
      case 'info': return 'bg-blue-500'
      default: return 'bg-gray-500'
    }
  }

  return (<div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t.securityCenter}</h1>
              <p className="text-white/70">Advanced security monitoring and management</p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <RefreshCw className="h-4 w-4 mr-2" />
                {t.refresh}
              </Button>
              <Button className="bg-red-500 hover:bg-red-600 text-white">
                <Bell className="h-4 w-4 mr-2" />
                {t.securityAlerts}
              </Button>
            </div>
          </div>
        </div>

        {/* Security Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.securityScore}</p>
                  <p className="text-2xl font-bold text-green-400">{securityData.securityScore}/100</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeThreats}</p>
                  <p className="text-2xl font-bold text-green-400">{securityData.activeThreats}</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.blockedAttacks}</p>
                  <p className="text-2xl font-bold text-blue-400">{securityData.blockedAttacks}</p>
                </div>
                <div className="p-3 rounded-lg bg-blue-500">
                  <Ban className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.failedLogins}</p>
                  <p className="text-2xl font-bold text-yellow-400">{securityData.failedLogins}</p>
                </div>
                <div className="p-3 rounded-lg bg-yellow-500">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-white/10 backdrop-blur-xl border-white/20">
            <TabsTrigger value="overview" className="data-[state=active]:bg-white/20 text-white">
              <Shield className="h-4 w-4 mr-2" />
              {t.securityOverview}
            </TabsTrigger>
            <TabsTrigger value="authentication" className="data-[state=active]:bg-white/20 text-white">
              <Key className="h-4 w-4 mr-2" />
              Authentication
            </TabsTrigger>
            <TabsTrigger value="sessions" className="data-[state=active]:bg-white/20 text-white">
              <Users className="h-4 w-4 mr-2" />
              {t.sessionManagement}
            </TabsTrigger>
            <TabsTrigger value="threats" className="data-[state=active]:bg-white/20 text-white">
              <Zap className="h-4 w-4 mr-2" />
              {t.threatDetection}
            </TabsTrigger>
            <TabsTrigger value="access" className="data-[state=active]:bg-white/20 text-white">
              <Lock className="h-4 w-4 mr-2" />
              {t.accessControl}
            </TabsTrigger>
          </TabsList>

          {/* Security Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Recent {t.securityAlerts}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {securityAlerts.map((alert) => (<div key={alert.id} className="flex items-start gap-3 p-3 rounded-lg bg-white/5">
                      <div className={`w-2 h-2 rounded-full mt-2 ${getAlertColor(alert.type)}`} />
                      <div className="flex-1">
                        <p className="text-white text-sm">{alert.message}</p>
                        <p className="text-white/50 text-xs">{alert.time}</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    {t.securitySettings}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="2fa" className="text-white">{t.twoFactorAuth}</Label>
                    <Switch
                      id="2fa"
                      checked={securityData.twoFactorEnabled}
                      onCheckedChange={() => handleToggle('twoFactorEnabled')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password-policy" className="text-white">{t.passwordPolicies}</Label>
                    <Switch
                      id="password-policy"
                      checked={securityData.passwordPolicyStrict}
                      onCheckedChange={() => handleToggle('passwordPolicyStrict')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="session-timeout" className="text-white">Session Timeout</Label>
                    <Switch
                      id="session-timeout"
                      checked={securityData.sessionTimeoutEnabled}
                      onCheckedChange={() => handleToggle('sessionTimeoutEnabled')}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="threat-detection" className="text-white">{t.threatDetection}</Label>
                    <Switch
                      id="threat-detection"
                      checked={securityData.threatDetectionEnabled}
                      onCheckedChange={() => handleToggle('threatDetectionEnabled')}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Authentication Tab */}
          <TabsContent value="authentication" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Key className="h-5 w-5" />
                    {t.twoFactorAuth}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-white/70">Status</span>
                    <Badge className={securityData.twoFactorEnabled ? 'bg-green-500' : 'bg-red-500'}>
                      {securityData.twoFactorEnabled ? t.enabled : t.disabled}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Users with 2FA</span>
                    <span className="text-white font-semibold">78%</span>
                  </div>
                  <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white">
                    <Settings className="h-4 w-4 mr-2" />
                    {t.configure} 2FA Settings
                  </Button>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Lock className="h-5 w-5" />
                    {t.passwordPolicies}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-white/70">Min Length</span>
                    <span className="text-white font-semibold">8 characters</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Complexity</span>
                    <Badge className="bg-green-500">{t.high}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Expiry</span>
                    <span className="text-white font-semibold">90 days</span>
                  </div>
                  <Button className="w-full bg-purple-500 hover:bg-purple-600 text-white">
                    <Settings className="h-4 w-4 mr-2" />
                    Update Policy
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Sessions Tab */}
          <TabsContent value="sessions" className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t.activeSessions} ({securityData.activeSessions})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {activeSessions.map((session) => (<div key={session.id} className="flex items-center justify-between p-3 rounded-lg bg-white/5">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-white font-medium">{session.user}</span>
                          <Badge className="bg-blue-500 text-xs">{session.location}</Badge>
                        </div>
                        <div className="text-white/70 text-sm">
                          {session.ip} • {session.device} • {session.lastActivity}
                        </div>
                      </div>
                      <Button size="sm" variant="outline" className="bg-red-500/20 border-red-500/50 text-red-400 hover:bg-red-500/30">
                        <UserX className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Continue with other tabs... */}
        </Tabs>
      </div>
    </div>
  )
}
