import React, { useState, useEffect } from 'react'
import { 
  Shield, 
  Lock, 
  Eye, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Users, 
  Key, 
  Globe, 
  Activity,
  FileText,
  Settings,
  Zap,
  Ban,
  UserX,
  Clock,
  MapPin,
  Smartphone,
  Monitor,
  Wifi,
  Database,
  Server,
  Network
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table'

interface SecurityCenterProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    securityCenter: 'مركز الأمان',
    securityOverview: 'نظرة عامة على الأمان',
    threatDetection: 'كشف التهديدات',
    accessControl: 'التحكم في الوصول',
    auditLogs: 'سجلات التدقيق',
    securityPolicies: 'سياسات الأمان',
    incidentResponse: 'الاستجابة للحوادث',
    securityScore: 'نقاط الأمان',
    activeThreats: 'التهديدات النشطة',
    blockedAttacks: 'الهجمات المحجوبة',
    vulnerabilities: 'نقاط الضعف',
    securityAlerts: 'تنبيهات الأمان',
    failedLogins: 'محاولات تسجيل دخول فاشلة',
    suspiciousActivity: 'نشاط مشبوه',
    dataBreaches: 'انتهاكات البيانات',
    malwareDetected: 'برامج ضارة مكتشفة',
    unauthorizedAccess: 'وصول غير مصرح',
    systemIntrusion: 'اختراق النظام',
    ddosAttack: 'هجوم DDoS',
    bruteForce: 'هجوم القوة الغاشمة',
    phishing: 'التصيد الاحتيالي',
    sqlInjection: 'حقن SQL',
    xssAttack: 'هجوم XSS',
    csrfAttack: 'هجوم CSRF',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    critical: 'حرج',
    resolved: 'محلول',
    investigating: 'قيد التحقيق',
    blocked: 'محجوب',
    allowed: 'مسموح',
    denied: 'مرفوض',
    pending: 'معلق',
    active: 'نشط',
    inactive: 'غير نشط',
    enabled: 'مفعل',
    disabled: 'معطل',
    viewDetails: 'عرض التفاصيل',
    blockUser: 'حجب المستخدم',
    allowAccess: 'السماح بالوصول',
    investigate: 'تحقيق',
    resolve: 'حل',
    escalate: 'تصعيد',
    quarantine: 'عزل',
    whitelist: 'قائمة بيضاء',
    blacklist: 'قائمة سوداء',
    twoFactorAuth: 'المصادقة الثنائية',
    passwordPolicy: 'سياسة كلمة المرور',
    sessionManagement: 'إدارة الجلسات',
    ipWhitelist: 'قائمة IP البيضاء',
    firewall: 'جدار الحماية',
    encryption: 'التشفير',
    backupSecurity: 'أمان النسخ الاحتياطي',
    networkSecurity: 'أمان الشبكة',
    applicationSecurity: 'أمان التطبيق',
    dataSecurity: 'أمان البيانات'
  },
  en: {
    securityCenter: 'Security Center',
    securityOverview: 'Security Overview',
    threatDetection: 'Threat Detection',
    accessControl: 'Access Control',
    auditLogs: 'Audit Logs',
    securityPolicies: 'Security Policies',
    incidentResponse: 'Incident Response',
    securityScore: 'Security Score',
    activeThreats: 'Active Threats',
    blockedAttacks: 'Blocked Attacks',
    vulnerabilities: 'Vulnerabilities',
    securityAlerts: 'Security Alerts',
    failedLogins: 'Failed Logins',
    suspiciousActivity: 'Suspicious Activity',
    dataBreaches: 'Data Breaches',
    malwareDetected: 'Malware Detected',
    unauthorizedAccess: 'Unauthorized Access',
    systemIntrusion: 'System Intrusion',
    ddosAttack: 'DDoS Attack',
    bruteForce: 'Brute Force Attack',
    phishing: 'Phishing',
    sqlInjection: 'SQL Injection',
    xssAttack: 'XSS Attack',
    csrfAttack: 'CSRF Attack',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    critical: 'Critical',
    resolved: 'Resolved',
    investigating: 'Investigating',
    blocked: 'Blocked',
    allowed: 'Allowed',
    denied: 'Denied',
    pending: 'Pending',
    active: 'Active',
    inactive: 'Inactive',
    enabled: 'Enabled',
    disabled: 'Disabled',
    viewDetails: 'View Details',
    blockUser: 'Block User',
    allowAccess: 'Allow Access',
    investigate: 'Investigate',
    resolve: 'Resolve',
    escalate: 'Escalate',
    quarantine: 'Quarantine',
    whitelist: 'Whitelist',
    blacklist: 'Blacklist',
    twoFactorAuth: 'Two-Factor Authentication',
    passwordPolicy: 'Password Policy',
    sessionManagement: 'Session Management',
    ipWhitelist: 'IP Whitelist',
    firewall: 'Firewall',
    encryption: 'Encryption',
    backupSecurity: 'Backup Security',
    networkSecurity: 'Network Security',
    applicationSecurity: 'Application Security',
    dataSecurity: 'Data Security'
  }
}
export default function SecurityCenter({ language }: SecurityCenterProps): React.ReactElement {
  const t = translations[language]
  const isRTL = language === 'ar'

  // Security metrics state
  const [securityMetrics, setSecurityMetrics] = useState({
    securityScore: 95,
    activeThreats: 0,
    blockedAttacks: 127,
    vulnerabilities: 2,
    failedLogins: 15,
    suspiciousActivities: 3,
    activeUsers: 89,
    blockedIPs: 45,
    quarantinedFiles: 0,
    securityAlerts: [
      {
        id: 1,
        type: 'bruteForce',
        severity: 'high',
        source: '(192).168.1.100',
        target: 'admin login',
        timestamp: new Date(Date.now() - 1000 * 60 * 30),
        status: 'blocked',
        description: 'Multiple failed login attempts detected'
      },
      {
        id: 2,
        type: 'suspiciousActivity',
        severity: 'medium',
        source: '(10).0.0.50',
        target: 'database access',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),
        status: 'investigating',
        description: 'Unusual database query patterns'
      },
      {
        id: 3,
        type: 'unauthorizedAccess',
        severity: 'low',
        source: '(172).16.0.25',
        target: 'file system',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),
        status: 'resolved',
        description: 'Attempted access to restricted directory'
      }
    ],
    recentAuditLogs: [
      {
        id: 1,
        user: '<EMAIL>',
        action: 'User Created',
        resource: 'User Management',
        timestamp: new Date(Date.now() - 1000 * 60 * 15),
        ip: '(192).168.1.10',
        status: 'success'
      },
      {
        id: 2,
        user: '<EMAIL>',
        action: 'Password Changed',
        resource: 'User Profile',
        timestamp: new Date(Date.now() - 1000 * 60 * 45),
        ip: '(192).168.1.20',
        status: 'success'
      },
      {
        id: 3,
        user: 'unknown',
        action: 'Login Attempt',
        resource: 'Authentication',
        timestamp: new Date(Date.now() - 1000 * 60 * 60),
        ip: '(192).168.1.100',
        status: 'failed'
      }
    ]
  })

  // Real-time updates from security API
  useEffect(() => {
    const updateSecurityMetrics = async () => {
      try {
        // TODO: Replace with real security API when available
        // For now, keep metrics static instead of using Math.random
        // The data should come from real security monitoring endpoints
      } catch (error) {
        console.error('Error updating security metrics:', error)
      }
    }

    // Update every 30 seconds with real data
    const interval = setInterval(updateSecurityMetrics, 30000)
    return () => clearInterval(interval)
  }, [])

  const getSeverityColor = (severity: string): void => {
    switch (severity) {
      case 'critical': return 'text-red-500 bg-red-500/20'
      case 'high': return 'text-orange-500 bg-orange-500/20'
      case 'medium': return 'text-yellow-500 bg-yellow-500/20'
      case 'low': return 'text-green-500 bg-green-500/20'
      default: return 'text-gray-500 bg-gray-500/20'
    }
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'blocked': case 'resolved': return 'text-green-500 bg-green-500/20'
      case 'investigating': case 'pending': return 'text-yellow-500 bg-yellow-500/20'
      case 'failed': case 'denied': return 'text-red-500 bg-red-500/20'
      case 'success': case 'allowed': return 'text-green-500 bg-green-500/20'
      default: return 'text-gray-500 bg-gray-500/20'
    }
  }

  return (<div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{t.securityCenter}</h1>
              <p className="text-white/70">Advanced security monitoring and threat management</p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                <Settings className="h-4 w-4 mr-2" />
                Security Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Security Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.securityScore}</p>
                  <p className="text-2xl font-bold text-green-400">{securityMetrics.securityScore}</p>
                  <p className="text-xs text-white/50">Excellent</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <Shield className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.activeThreats}</p>
                  <p className="text-2xl font-bold text-green-400">{securityMetrics.activeThreats}</p>
                  <p className="text-xs text-white/50">All Clear</p>
                </div>
                <div className="p-3 rounded-lg bg-green-500">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.blockedAttacks}</p>
                  <p className="text-2xl font-bold text-blue-400">{securityMetrics.blockedAttacks}</p>
                  <p className="text-xs text-white/50">Last 24h</p>
                </div>
                <div className="p-3 rounded-lg bg-blue-500">
                  <Ban className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 backdrop-blur-xl border-white/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/70 text-sm">{t.vulnerabilities}</p>
                  <p className="text-2xl font-bold text-yellow-400">{securityMetrics.vulnerabilities}</p>
                  <p className="text-xs text-white/50">Low Risk</p>
                </div>
                <div className="p-3 rounded-lg bg-yellow-500">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="threats" className="space-y-6">
          <TabsList className="bg-white/10 backdrop-blur-xl border-white/20">
            <TabsTrigger value="threats" className="data-[state=active]:bg-white/20 text-white">
              <AlertTriangle className="h-4 w-4 mr-2" />
              {t.threatDetection}
            </TabsTrigger>
            <TabsTrigger value="access" className="data-[state=active]:bg-white/20 text-white">
              <Lock className="h-4 w-4 mr-2" />
              {t.accessControl}
            </TabsTrigger>
            <TabsTrigger value="audit" className="data-[state=active]:bg-white/20 text-white">
              <FileText className="h-4 w-4 mr-2" />
              {t.auditLogs}
            </TabsTrigger>
            <TabsTrigger value="policies" className="data-[state=active]:bg-white/20 text-white">
              <Settings className="h-4 w-4 mr-2" />
              {t.securityPolicies}
            </TabsTrigger>
          </TabsList>

          {/* Threat Detection Tab */}
          <TabsContent value="threats" className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white">{t.securityAlerts}</CardTitle>
                <CardDescription className="text-white/70">
                  Recent security incidents and threats
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {securityMetrics.securityAlerts.map((alert) => (<div key={alert.id} className="flex items-center justify-between p-4 rounded-lg bg-white/5 border border-white/10">
                      <div className="flex items-center gap-4">
                        <div className={`p-2 rounded-lg ${getSeverityColor(alert.severity)}`}>
                          <AlertTriangle className="h-4 w-4" />
                        </div>
                        <div>
                          <h4 className="text-white font-medium">{t[alert.type as keyof typeof t]}</h4>
                          <p className="text-white/70 text-sm">{alert.description}</p>
                          <div className="flex items-center gap-4 mt-1 text-xs text-white/50">
                            <span>Source: {alert.source}</span>
                            <span>Target: {alert.target}</span>
                            <span>{alert.timestamp.toLocaleTimeString()}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getSeverityColor(alert.severity)}>
                          {t[alert.severity as keyof typeof t]}
                        </Badge>
                        <Badge className={getStatusColor(alert.status)}>
                          {t[alert.status as keyof typeof t]}
                        </Badge>
                        <Button size="sm" variant="outline" className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                          {t.viewDetails}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Access Control Tab */}
          <TabsContent value="access" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Access Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-white/70">Active Users</span>
                    <span className="text-white font-semibold">{securityMetrics.activeUsers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">{t.failedLogins}</span>
                    <span className="text-white font-semibold">{securityMetrics.failedLogins}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Blocked IPs</span>
                    <span className="text-white font-semibold">{securityMetrics.blockedIPs}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Quarantined Files</span>
                    <span className="text-white font-semibold">{securityMetrics.quarantinedFiles}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Security Controls</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white">
                    <Key className="h-4 w-4 mr-2" />
                    {t.twoFactorAuth}
                  </Button>
                  <Button className="w-full bg-purple-500 hover:bg-purple-600 text-white">
                    <Lock className="h-4 w-4 mr-2" />
                    {t.passwordPolicy}
                  </Button>
                  <Button className="w-full bg-green-500 hover:bg-green-600 text-white">
                    <Users className="h-4 w-4 mr-2" />
                    {t.sessionManagement}
                  </Button>
                  <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                    <Globe className="h-4 w-4 mr-2" />
                    {t.ipWhitelist}
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Audit Logs Tab */}
          <TabsContent value="audit" className="space-y-6">
            <Card className="bg-white/10 backdrop-blur-xl border-white/20">
              <CardHeader>
                <CardTitle className="text-white">{t.auditLogs}</CardTitle>
                <CardDescription className="text-white/70">
                  System activity and user actions log
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-white/20">
                      <TableHead className="text-white">User</TableHead>
                      <TableHead className="text-white">Action</TableHead>
                      <TableHead className="text-white">Resource</TableHead>
                      <TableHead className="text-white">IP Address</TableHead>
                      <TableHead className="text-white">Time</TableHead>
                      <TableHead className="text-white">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {securityMetrics.recentAuditLogs.map((log) => (<TableRow key={log.id} className="border-white/10">
                        <TableCell className="text-white">{log.user}</TableCell>
                        <TableCell className="text-white">{log.action}</TableCell>
                        <TableCell className="text-white">{log.resource}</TableCell>
                        <TableCell className="text-white">{log.ip}</TableCell>
                        <TableCell className="text-white">{log.timestamp.toLocaleTimeString()}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(log.status)}>
                            {t[log.status as keyof typeof t]}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Policies Tab */}
          <TabsContent value="policies" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Security Policies</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.twoFactorAuth}</span>
                    <Badge className="bg-green-500">{t.enabled}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.passwordPolicy}</span>
                    <Badge className="bg-green-500">{t.enabled}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.sessionManagement}</span>
                    <Badge className="bg-green-500">{t.enabled}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.firewall}</span>
                    <Badge className="bg-green-500">{t.enabled}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.encryption}</span>
                    <Badge className="bg-green-500">{t.enabled}</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/10 backdrop-blur-xl border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Security Modules</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.networkSecurity}</span>
                    <Badge className="bg-green-500">{t.active}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.applicationSecurity}</span>
                    <Badge className="bg-green-500">{t.active}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.dataSecurity}</span>
                    <Badge className="bg-green-500">{t.active}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white">{t.backupSecurity}</span>
                    <Badge className="bg-green-500">{t.active}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white">Intrusion Detection</span>
                    <Badge className="bg-green-500">{t.active}</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
