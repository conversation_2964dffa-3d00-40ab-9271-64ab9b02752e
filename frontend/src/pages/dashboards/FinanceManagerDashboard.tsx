import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import type { RootState } from '../../store'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  PieChart,
  BarChart3,
  Calculator,
  Wallet,
  Receipt,
  Target,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Eye,
  Plus,
  FileText
} from 'lucide-react'

interface FinanceManagerDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً بك',
    financeOverview: 'نظرة عامة على المالية',
    budgetManagement: 'إدارة الميزانية',
    expenseTracking: 'تتبع المصروفات',
    revenueAnalysis: 'تحليل الإيرادات',
    financialReports: 'التقارير المالية',
    quickActions: 'إجراءات سريعة',
    totalRevenue: 'إجمالي الإيرادات',
    totalExpenses: 'إجمالي المصروفات',
    netProfit: 'صافي الربح',
    budgetUtilization: 'استخدام الميزانية',
    pendingInvoices: 'الفواتير المعلقة',
    cashFlow: 'التدفق النقدي',
    monthlyBudget: 'الميزانية الشهرية',
    quarterlyTarget: 'الهدف الربعي',
    expenseCategories: 'فئات المصروفات',
    recentTransactions: 'المعاملات الحديثة',
    budgetAlerts: 'تنبيهات الميزانية',
    createBudget: 'إنشاء ميزانية',
    processPayment: 'معالجة دفعة',
    generateReport: 'إنشاء تقرير',
    reviewExpenses: 'مراجعة المصروفات',
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    thisMonth: 'هذا الشهر',
    lastMonth: 'الشهر الماضي',
    increase: 'زيادة',
    decrease: 'انخفاض',
    onTrack: 'على المسار الصحيح',
    overBudget: 'تجاوز الميزانية',
    underBudget: 'أقل من الميزانية'
  },
  en: {
    welcome: 'Welcome',
    financeOverview: 'Finance Overview',
    budgetManagement: 'Budget Management',
    expenseTracking: 'Expense Tracking',
    revenueAnalysis: 'Revenue Analysis',
    financialReports: 'Financial Reports',
    quickActions: 'Quick Actions',
    totalRevenue: 'Total Revenue',
    totalExpenses: 'Total Expenses',
    netProfit: 'Net Profit',
    budgetUtilization: 'Budget Utilization',
    pendingInvoices: 'Pending Invoices',
    cashFlow: 'Cash Flow',
    monthlyBudget: 'Monthly Budget',
    quarterlyTarget: 'Quarterly Target',
    expenseCategories: 'Expense Categories',
    recentTransactions: 'Recent Transactions',
    budgetAlerts: 'Budget Alerts',
    createBudget: 'Create Budget',
    processPayment: 'Process Payment',
    generateReport: 'Generate Report',
    reviewExpenses: 'Review Expenses',
    refresh: 'Refresh',
    viewDetails: 'View Details',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    increase: 'Increase',
    decrease: 'Decrease',
    onTrack: 'On Track',
    overBudget: 'Over Budget',
    underBudget: 'Under Budget'
  }
}

export default function FinanceManagerDashboard({ language }: FinanceManagerDashboardProps) {
  const { user } = useSelector((state: RootState => state.auth)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Finance-specific metrics
  const [financeMetrics, setFinanceMetrics] = useState({
    totalRevenue: 2850000,
    totalExpenses: 1920000,
    netProfit: 930000,
    budgetUtilization: 78.5,
    pendingInvoices: 45,
    cashFlow: 1250000,
    monthlyBudget: 2500000,
    quarterlyTarget: 7500000,
    expenseGrowth: 12.5,
    revenueGrowth: 18.3
  })

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const financialCards = [
    {
      title: t.totalRevenue,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(financeMetrics.totalRevenue),
      change: `+${financeMetrics.revenueGrowth}%`,
      trend: 'up',
      icon: TrendingUp,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.totalExpenses,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(financeMetrics.totalExpenses),
      change: `+${financeMetrics.expenseGrowth}%`,
      trend: 'up',
      icon: TrendingDown,
      color: 'from-red-500 to-red-600'
    },
    {
      title: t.netProfit,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(financeMetrics.netProfit),
      change: '+24.7%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.budgetUtilization,
      value: `${financeMetrics.budgetUtilization}%`,
      change: t.onTrack,
      trend: 'stable',
      icon: Target,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.pendingInvoices,
      value: financeMetrics.pendingInvoices.toString(),
      change: '-8',
      trend: 'down',
      icon: Receipt,
      color: 'from-orange-500 to-orange-600'
    },
    {
      title: t.cashFlow,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(financeMetrics.cashFlow),
      change: '+15.2%',
      trend: 'up',
      icon: Wallet,
      color: 'from-cyan-500 to-cyan-600'
    }
  ]

  const quickActions = [
    { title: t.createBudget, icon: Calculator, href: '/finance/budgets', color: 'from-blue-500 to-blue-600' },
    { title: t.reviewExpenses, icon: Receipt, href: '/finance/expenses', color: 'from-red-500 to-red-600' },
    { title: t.generateReport, icon: FileText, href: '/finance/reports', color: 'from-green-500 to-green-600' },
    { title: t.processPayment, icon: CreditCard, href: '/finance/payments', color: 'from-purple-500 to-purple-600' }
  ]

  const recentTransactions = [
    {
      id: 1,
      type: 'income',
      description: 'Payment from Client ABC',
      descriptionAr: 'دفعة من العميل ABC',
      amount: 125000,
      date: '2024-01-20',
      category: 'Revenue',
      categoryAr: 'إيرادات'
    },
    {
      id: 2,
      type: 'expense',
      description: 'Office Supplies Purchase',
      descriptionAr: 'شراء مستلزمات مكتبية',
      amount: -15000,
      date: '2024-01-19',
      category: 'Operations',
      categoryAr: 'عمليات'
    },
    {
      id: 3,
      type: 'expense',
      description: 'Software License Renewal',
      descriptionAr: 'تجديد رخصة البرمجيات',
      amount: -45000,
      date: '2024-01-18',
      category: 'Technology',
      categoryAr: 'تكنولوجيا'
    }
  ]

  const budgetAlerts = [
    {
      id: 1,
      type: 'warning',
      message: 'Marketing budget is 85% utilized',
      messageAr: 'ميزانية التسويق مستخدمة بنسبة 85%',
      category: 'Marketing',
      percentage: 85
    },
    {
      id: 2,
      type: 'danger',
      message: 'IT budget exceeded by 12%',
      messageAr: 'ميزانية تقنية المعلومات تجاوزت بنسبة 12%',
      category: 'IT',
      percentage: 112
    }
  ]

  const getStatusColor = (trend: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-400'
      case 'down':
        return 'text-red-400'
      default:
        return 'text-blue-400'
    }
  }

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'warning':
        return 'border-l-yellow-500 bg-yellow-500/10'
      case 'danger':
        return 'border-l-red-500 bg-red-500/10'
      default:
        return 'border-l-blue-500 bg-blue-500/10'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t.welcome}, {user?.name}
          </h1>
          <p className="text-white/70">لوحة تحكم مدير المالية - إدارة شاملة للشؤون المالية</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <FileText className="h-4 w-4 mr-2" />
            {t.generateReport}
          </Button>
        </div>
      </div>

      {/* Financial Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {financialCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${getStatusColor(card.trend)}`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.quickActions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  className="group flex flex-col items-center gap-3 p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-4 rounded-2xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-8 w-8 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-sm">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Transactions */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl">{t.recentTransactions}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button">
                <Eye className="h-4 w-4 mr-2" />
                {t.viewDetails}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`p-2 rounded-lg ${
                          transaction.type === 'income' ? 'bg-green-500/20' : 'bg-red-500/20'
                        }`}>
                          {transaction.type === 'income' ? 
                            <TrendingUp className="h-4 w-4 text-green-400" /> :
                            <TrendingDown className="h-4 w-4 text-red-400" />
                          }
                        </div>
                        <h4 className="text-white font-medium">
                          {language === 'ar' ? transaction.descriptionAr : transaction.description}
                        </h4>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-white/80">
                        <span>{transaction.date}</span>
                        <span>{language === 'ar' ? transaction.categoryAr : transaction.category}</span>
                      </div>
                    </div>
                    <div className={`text-lg font-bold ${
                      transaction.amount > 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(Math.abs(transaction.amount))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget Alerts */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            {t.budgetAlerts}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {budgetAlerts.map((alert) => (
              <div key={alert.id} className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all border-l-4 ${getAlertColor(alert.type)}`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-white font-medium mb-1">
                      {language === 'ar' ? alert.messageAr : alert.message}
                    </p>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          alert.percentage > 100 ? 'bg-red-500' : 
                          alert.percentage > 80 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(alert.percentage, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className={`text-lg font-bold ml-4 ${
                    alert.percentage > 100 ? 'text-red-400' : 
                    alert.percentage > 80 ? 'text-yellow-400' : 'text-green-400'
                  }`}>
                    {alert.percentage}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
