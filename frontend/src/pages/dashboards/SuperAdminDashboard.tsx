import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import type { RootState, AppDispatch } from '../../store'
import { fetchDashboardLayout, fetchWidgetData } from '../../store/slices/dashboardSlice'
import {
  Users,
  Server,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Settings,
  Shield,
  Database,
  Zap,
  Globe,
  RefreshCw,
  Building,
  Briefcase,
  UserCheck,
  UserX,
  Calendar,
  Target,
  Award,
  FileText,
  CreditCard,
  Wallet,
  Receipt,
  PieChart,
  LineChart,
  Monitor,
  Cpu,
  HardDrive,
  Wifi,
  Lock,
  Unlock,
  Eye,
  Download,
  Upload,
  Mail,
  Bell,
  Search,
  Filter,
  MoreHorizontal
} from 'lucide-react'

interface SuperAdminDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً بك',
    systemOverview: 'نظرة عامة على النظام',
    userManagement: 'إدارة المستخدمين',
    systemHealth: 'صحة النظام',
    securityStatus: 'حالة الأمان',
    performanceMetrics: 'مقاييس الأداء',
    recentActivities: 'الأنشطة الحديثة',
    quickActions: 'إجراءات سريعة',
    systemStats: 'إحصائيات النظام',
    userStats: 'إحصائيات المستخدمين',
    financialOverview: 'النظرة المالية',
    operationalMetrics: 'المقاييس التشغيلية',
    totalUsers: 'إجمالي المستخدمين',
    activeUsers: 'المستخدمون النشطون',
    systemUptime: 'وقت تشغيل النظام',
    serverLoad: 'حمولة الخادم',
    databaseSize: 'حجم قاعدة البيانات',
    apiRequests: 'طلبات API',
    securityAlerts: 'تنبيهات الأمان',
    backupStatus: 'حالة النسخ الاحتياطي',
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    manageUsers: 'إدارة المستخدمين',
    systemSettings: 'إعدادات النظام',
    securityCenter: 'مركز الأمان',
    analyticsCenter: 'مركز التحليلات',
    excellent: 'ممتاز',
    good: 'جيد',
    warning: 'تحذير',
    critical: 'حرج',
    // Enhanced metrics
    totalRevenue: 'إجمالي الإيرادات',
    totalExpenses: 'إجمالي المصروفات',
    netProfit: 'صافي الربح',
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    totalDepartments: 'إجمالي الأقسام',
    totalEmployees: 'إجمالي الموظفين',
    newEmployees: 'الموظفون الجدد',
    employeeTurnover: 'دوران الموظفين',
    avgSalary: 'متوسط الراتب',
    budgetUtilization: 'استخدام الميزانية',
    // System metrics
    cpuUsage: 'استخدام المعالج',
    memoryUsage: 'استخدام الذاكرة',
    diskUsage: 'استخدام القرص',
    networkTraffic: 'حركة الشبكة',
    responseTime: 'وقت الاستجابة',
    errorRate: 'معدل الأخطاء',
    // Security metrics
    securityScore: 'نقاط الأمان',
    activeThreats: 'التهديدات النشطة',
    blockedAttacks: 'الهجمات المحجوبة',
    lastBackup: 'آخر نسخة احتياطية',
    // Analytics
    userGrowth: 'نمو المستخدمين',
    revenueGrowth: 'نمو الإيرادات',
    projectSuccess: 'نجاح المشاريع',
    systemEfficiency: 'كفاءة النظام',
    // Charts
    userAnalytics: 'تحليلات المستخدمين',
    financialAnalytics: 'التحليلات المالية',
    projectAnalytics: 'تحليلات المشاريع',
    systemAnalytics: 'تحليلات النظام',
    performanceChart: 'مخطط الأداء',
    revenueChart: 'مخطط الإيرادات',
    userGrowthChart: 'مخطط نمو المستخدمين',
    departmentChart: 'مخطط الأقسام'
  },
  en: {
    welcome: 'Welcome',
    systemOverview: 'System Overview',
    userManagement: 'User Management',
    systemHealth: 'System Health',
    securityStatus: 'Security Status',
    performanceMetrics: 'Performance Metrics',
    recentActivities: 'Recent Activities',
    quickActions: 'Quick Actions',
    systemStats: 'System Statistics',
    userStats: 'User Statistics',
    financialOverview: 'Financial Overview',
    operationalMetrics: 'Operational Metrics',
    totalUsers: 'Total Users',
    activeUsers: 'Active Users',
    systemUptime: 'System Uptime',
    serverLoad: 'Server Load',
    databaseSize: 'Database Size',
    apiRequests: 'API Requests',
    securityAlerts: 'Security Alerts',
    backupStatus: 'Backup Status',
    refresh: 'Refresh',
    viewDetails: 'View Details',
    manageUsers: 'Manage Users',
    systemSettings: 'System Settings',
    securityCenter: 'Security Center',
    analyticsCenter: 'Analytics Center',
    excellent: 'Excellent',
    good: 'Good',
    warning: 'Warning',
    critical: 'Critical',
    // Enhanced metrics
    totalRevenue: 'Total Revenue',
    totalExpenses: 'Total Expenses',
    netProfit: 'Net Profit',
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    totalDepartments: 'Total Departments',
    totalEmployees: 'Total Employees',
    newEmployees: 'New Employees',
    employeeTurnover: 'Employee Turnover',
    avgSalary: 'Average Salary',
    budgetUtilization: 'Budget Utilization',
    // System metrics
    cpuUsage: 'CPU Usage',
    memoryUsage: 'Memory Usage',
    diskUsage: 'Disk Usage',
    networkTraffic: 'Network Traffic',
    responseTime: 'Response Time',
    errorRate: 'Error Rate',
    // Security metrics
    securityScore: 'Security Score',
    activeThreats: 'Active Threats',
    blockedAttacks: 'Blocked Attacks',
    lastBackup: 'Last Backup',
    // Analytics
    userGrowth: 'User Growth',
    revenueGrowth: 'Revenue Growth',
    projectSuccess: 'Project Success',
    systemEfficiency: 'System Efficiency',
    // Charts
    userAnalytics: 'User Analytics',
    financialAnalytics: 'Financial Analytics',
    projectAnalytics: 'Project Analytics',
    systemAnalytics: 'System Analytics',
    performanceChart: 'Performance Chart',
    revenueChart: 'Revenue Chart',
    userGrowthChart: 'User Growth Chart',
    departmentChart: 'Department Chart'
  }
}

export default function SuperAdminDashboard({ language }: SuperAdminDashboardProps) {
  const dispatch = useDispatch<AppDispatch>()
  const { user } = useSelector((state: RootState) => state.auth)
  const { currentLayout, isLoading } = useSelector((state: RootState) => state.dashboard)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Comprehensive system metrics - initialized with defaults, will be populated from API
  const [systemMetrics, setSystemMetrics] = useState({
    // User metrics
    totalUsers: 0,
    activeUsers: 0,
    newUsers: 0,
    userGrowthRate: 0,

    // Financial metrics
    totalRevenue: 0,
    totalExpenses: 0,
    netProfit: 0,
    revenueGrowth: 0,
    budgetUtilization: 0,

    // Project metrics
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    projectSuccessRate: 0,

    // HR metrics
    totalEmployees: 0,
    newEmployees: 0,
    employeeTurnover: 0,
    avgSalary: 0,
    totalDepartments: 0,

    // System health
    systemUptime: 0,
    serverLoad: 0,
    cpuUsage: 0,
    memoryUsage: 0,
    diskUsage: 0,
    databaseSize: 0,

    // Performance metrics
    apiRequests: 0,
    responseTime: 0,
    errorRate: 0,
    networkTraffic: 0,

    // Security metrics
    securityScore: 0,
    securityAlerts: 0,
    activeThreats: 0,
    blockedAttacks: 0,
    backupStatus: 'unknown',
    lastBackup: 'unknown'
  })

  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user?.role.id === 'super_admin') {
      dispatch(fetchDashboardLayout('super_admin'))
    }
  }, [dispatch, user])

  // Fetch real system metrics from API
  useEffect(() => {
    const fetchSystemMetrics = async () => {
      try {
        setLoading(true)

        // Import APIs
        const { dashboardAPI } = await import('../../services/api')
        const { employeeAPI } = await import('../../services/employeeAPI')
        const { projectAPI } = await import('../../services/projectAPI')

        // Fetch data from multiple APIs in parallel
        const [dashboardData, systemData, employeeData, projectData] = await Promise.allSettled([
          dashboardAPI.getStats(),
          dashboardAPI.getSystemStats(),
          employeeAPI.getStats(),
          projectAPI.getStats()
        ])

        // Process results and update state
        const dashboard = dashboardData.status === 'fulfilled' ? dashboardData.value : null
        const system = systemData.status === 'fulfilled' ? systemData.value : null
        const employees = employeeData.status === 'fulfilled' ? employeeData.value : null
        const projects = projectData.status === 'fulfilled' ? projectData.value : null

        setSystemMetrics(prev => ({
          ...prev,
          // User metrics from dashboard/system APIs
          totalUsers: dashboard?.total_employees || 0,
          activeUsers: system?.activeUsers || 0,

          // Financial metrics from dashboard API
          totalRevenue: dashboard?.monthly_revenue || 0,
          totalExpenses: dashboard?.monthly_expenses || 0,
          netProfit: (dashboard?.monthly_revenue || 0) - (dashboard?.monthly_expenses || 0),

          // Project metrics
          totalProjects: projects?.total || 0,
          activeProjects: dashboard?.active_projects || 0,

          // HR metrics
          totalEmployees: employees?.total || dashboard?.total_employees || 0,
          totalDepartments: dashboard?.total_departments || 0,

          // System health from system API
          systemUptime: system?.uptime ? parseFloat(system.uptime.replace('%', '')) : 0,
          serverLoad: system?.serverLoad || system?.systemLoad || 0,
          cpuUsage: system?.cpuUsage || 0,
          memoryUsage: system?.memoryUsage || 0,
          diskUsage: system?.diskUsage || 0,

          // Performance metrics
          apiRequests: system?.apiRequests || 0,
          responseTime: system?.responseTime || 0,
          errorRate: system?.errorRate || 0
        }))

      } catch (error) {
        console.error('Failed to fetch system metrics:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSystemMetrics()
  }, [])

  // Real-time updates with actual API calls (every 30 seconds)
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const { dashboardAPI } = await import('../../services/api')
        const systemData = await dashboardAPI.getSystemStats()

        setSystemMetrics(prev => ({
          ...prev,
          activeUsers: systemData.activeUsers || prev.activeUsers,
          serverLoad: systemData.serverLoad || systemData.systemLoad || prev.serverLoad,
          apiRequests: systemData.apiRequests || prev.apiRequests,
          responseTime: systemData.responseTime || prev.responseTime
        }))
      } catch (error) {
        console.error('Failed to update real-time metrics:', error)
      }
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      // Import APIs
      const { dashboardAPI } = await import('../../services/api')
      const { employeeAPI } = await import('../../services/employeeAPI')
      const { projectAPI } = await import('../../services/projectAPI')

      // Fetch fresh data
      const [dashboardData, systemData, employeeData, projectData] = await Promise.allSettled([
        dashboardAPI.getStats(),
        dashboardAPI.getSystemStats(),
        employeeAPI.getStats(),
        projectAPI.getStats()
      ])

      // Process and update metrics (same logic as initial fetch)
      const dashboard = dashboardData.status === 'fulfilled' ? dashboardData.value : null
      const system = systemData.status === 'fulfilled' ? systemData.value : null
      const employees = employeeData.status === 'fulfilled' ? employeeData.value : null
      const projects = projectData.status === 'fulfilled' ? projectData.value : null

      setSystemMetrics(prev => ({
        ...prev,
        totalUsers: dashboard?.total_employees || prev.totalUsers,
        activeUsers: system?.activeUsers || prev.activeUsers,
        totalRevenue: dashboard?.monthly_revenue || prev.totalRevenue,
        totalExpenses: dashboard?.monthly_expenses || prev.totalExpenses,
        netProfit: (dashboard?.monthly_revenue || 0) - (dashboard?.monthly_expenses || 0),
        totalProjects: projects?.total || prev.totalProjects,
        activeProjects: dashboard?.active_projects || prev.activeProjects,
        totalEmployees: employees?.total || dashboard?.total_employees || prev.totalEmployees,
        totalDepartments: dashboard?.total_departments || prev.totalDepartments,
        serverLoad: system?.serverLoad || system?.systemLoad || prev.serverLoad,
        cpuUsage: system?.cpuUsage || prev.cpuUsage,
        memoryUsage: system?.memoryUsage || prev.memoryUsage,
        apiRequests: system?.apiRequests || prev.apiRequests,
        responseTime: system?.responseTime || prev.responseTime
      }))

    } catch (error) {
      console.error('Failed to refresh dashboard data:', error)
    } finally {
      setRefreshing(false)
    }
  }

  // Comprehensive metric cards for Super Admin
  const overviewCards = [
    // User metrics
    {
      title: t.totalUsers,
      value: systemMetrics.totalUsers.toLocaleString(),
      change: `+${systemMetrics.userGrowthRate}%`,
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600',
      category: 'users'
    },
    {
      title: t.activeUsers,
      value: systemMetrics.activeUsers.toString(),
      change: '+5',
      trend: 'up',
      icon: Activity,
      color: 'from-green-500 to-green-600',
      category: 'users'
    },
    {
      title: t.newEmployees,
      value: systemMetrics.newEmployees.toString(),
      change: 'هذا الشهر',
      trend: 'stable',
      icon: UserCheck,
      color: 'from-emerald-500 to-emerald-600',
      category: 'users'
    }
  ]

  const financialCards = [
    {
      title: t.totalRevenue,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(systemMetrics.totalRevenue),
      change: `+${systemMetrics.revenueGrowth}%`,
      trend: 'up',
      icon: DollarSign,
      color: 'from-green-500 to-green-600',
      category: 'financial'
    },
    {
      title: t.totalExpenses,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(systemMetrics.totalExpenses),
      change: '+8.5%',
      trend: 'up',
      icon: Receipt,
      color: 'from-red-500 to-red-600',
      category: 'financial'
    },
    {
      title: t.netProfit,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
      }).format(systemMetrics.netProfit),
      change: '+22.1%',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-purple-500 to-purple-600',
      category: 'financial'
    }
  ]

  const projectCards = [
    {
      title: t.totalProjects,
      value: systemMetrics.totalProjects.toString(),
      change: '+3',
      trend: 'up',
      icon: Briefcase,
      color: 'from-blue-500 to-blue-600',
      category: 'projects'
    },
    {
      title: t.activeProjects,
      value: systemMetrics.activeProjects.toString(),
      change: '+2',
      trend: 'up',
      icon: Target,
      color: 'from-cyan-500 to-cyan-600',
      category: 'projects'
    },
    {
      title: t.completedProjects,
      value: systemMetrics.completedProjects.toString(),
      change: '+1',
      trend: 'up',
      icon: CheckCircle,
      color: 'from-green-500 to-green-600',
      category: 'projects'
    }
  ]

  const systemHealthCards = [
    {
      title: t.systemUptime,
      value: `${systemMetrics.systemUptime}%`,
      change: 'مستقر',
      trend: 'stable',
      icon: Server,
      color: 'from-emerald-500 to-emerald-600',
      category: 'system'
    },
    {
      title: t.cpuUsage,
      value: `${systemMetrics.cpuUsage}%`,
      change: systemMetrics.cpuUsage > 80 ? 'عالي' : 'طبيعي',
      trend: systemMetrics.cpuUsage > 80 ? 'warning' : 'stable',
      icon: Cpu,
      color: 'from-orange-500 to-orange-600',
      category: 'system'
    },
    {
      title: t.memoryUsage,
      value: `${systemMetrics.memoryUsage}%`,
      change: systemMetrics.memoryUsage > 80 ? 'عالي' : 'طبيعي',
      trend: systemMetrics.memoryUsage > 80 ? 'warning' : 'stable',
      icon: HardDrive,
      color: 'from-purple-500 to-purple-600',
      category: 'system'
    },
    {
      title: t.securityScore,
      value: `${systemMetrics.securityScore}/100`,
      change: 'ممتاز',
      trend: 'up',
      icon: Shield,
      color: 'from-green-500 to-green-600',
      category: 'security'
    },
    {
      title: t.apiRequests,
      value: systemMetrics.apiRequests.toLocaleString(),
      change: '+1.2K',
      trend: 'up',
      icon: Globe,
      color: 'from-cyan-500 to-cyan-600',
      category: 'performance'
    },
    {
      title: t.responseTime,
      value: `${systemMetrics.responseTime}ms`,
      change: 'سريع',
      trend: 'stable',
      icon: Zap,
      color: 'from-yellow-500 to-yellow-600',
      category: 'performance'
    }
  ]

  const quickActions = [
    { title: t.manageUsers, icon: Users, href: '/employees', color: 'from-blue-500 to-blue-600' },
    { title: t.systemSettings, icon: Settings, href: '/settings', color: 'from-gray-500 to-gray-600' },
    { title: t.securityCenter, icon: Shield, href: '/security', color: 'from-red-500 to-red-600' },
    { title: t.analyticsCenter, icon: BarChart3, href: '/analytics', color: 'from-green-500 to-green-600' }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'user_login',
      message: 'New user login from Riyadh',
      messageAr: 'تسجيل دخول مستخدم جديد من الرياض',
      timestamp: '2 minutes ago',
      icon: Users,
      severity: 'info'
    },
    {
      id: 2,
      type: 'security_alert',
      message: 'Failed login attempt detected',
      messageAr: 'تم اكتشاف محاولة تسجيل دخول فاشلة',
      timestamp: '5 minutes ago',
      icon: AlertTriangle,
      severity: 'warning'
    },
    {
      id: 3,
      type: 'system_update',
      message: 'System backup completed successfully',
      messageAr: 'تم إكمال النسخ الاحتياطي للنظام بنجاح',
      timestamp: '10 minutes ago',
      icon: CheckCircle,
      severity: 'success'
    },
    {
      id: 4,
      type: 'performance',
      message: 'Server performance optimized',
      messageAr: 'تم تحسين أداء الخادم',
      timestamp: '15 minutes ago',
      icon: TrendingUp,
      severity: 'success'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-400'
      case 'warning':
        return 'text-yellow-400'
      case 'critical':
        return 'text-red-400'
      default:
        return 'text-blue-400'
    }
  }

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-400" />
      default:
        return <Activity className="h-4 w-4 text-blue-400" />
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t.welcome}, {user?.name}
          </h1>
          <p className="text-white/70">لوحة تحكم المدير الرئيسي - إدارة شاملة للنظام</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <BarChart3 className="h-4 w-4 mr-2" />
            {t.analyticsCenter}
          </Button>
          <Button variant="outline" className="glass-button">
            <Settings className="h-4 w-4 mr-2" />
            {t.systemSettings}
          </Button>
        </div>
      </div>

      {/* Overview Cards - Top Level Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {overviewCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${getStatusColor(card.trend)}`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Financial Overview */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            {t.financialOverview}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {financialCards.map((card, index) => (
              <div key={index} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-center justify-between mb-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${card.color}`}>
                    <card.icon className="h-5 w-5 text-white" />
                  </div>
                  <span className={`text-sm font-medium ${getStatusColor(card.trend)}`}>
                    {card.change}
                  </span>
                </div>
                <p className="text-white/70 text-sm mb-1">{card.title}</p>
                <p className="text-xl font-bold text-white">{card.value}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Project Overview */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <Briefcase className="h-5 w-5" />
            {t.projectAnalytics}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {projectCards.map((card, index) => (
              <div key={index} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-center justify-between mb-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${card.color}`}>
                    <card.icon className="h-5 w-5 text-white" />
                  </div>
                  <span className={`text-sm font-medium ${getStatusColor(card.trend)}`}>
                    {card.change}
                  </span>
                </div>
                <p className="text-white/70 text-sm mb-1">{card.title}</p>
                <p className="text-xl font-bold text-white">{card.value}</p>
              </div>
            ))}
          </div>

          {/* Project Success Rate Chart */}
          <div className="mt-6 p-4 glass-card border-white/10">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-white font-medium">معدل نجاح المشاريع</h4>
              <span className="text-green-400 font-bold">{systemMetrics.projectSuccessRate}%</span>
            </div>
            <div className="w-full bg-white/20 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full transition-all duration-1000"
                style={{ width: `${systemMetrics.projectSuccessRate}%` }}
              ></div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Health & Performance */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            {t.systemHealth}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            {systemHealthCards.map((card, index) => (
              <div key={index} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-center justify-between mb-3">
                  <div className={`p-2 rounded-lg bg-gradient-to-r ${card.color}`}>
                    <card.icon className="h-4 w-4 text-white" />
                  </div>
                  <span className={`text-xs font-medium ${getStatusColor(card.trend)}`}>
                    {card.change}
                  </span>
                </div>
                <p className="text-white/70 text-xs mb-1">{card.title}</p>
                <p className="text-lg font-bold text-white">{card.value}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Analytics Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <LineChart className="h-5 w-5" />
              {t.userGrowthChart}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-white/80">نمو المستخدمين الشهري</span>
                <span className="text-green-400 font-bold">+{systemMetrics.userGrowthRate}%</span>
              </div>

              {/* Simple chart representation */}
              <div className="space-y-3">
                {['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'].map((month, index) => {
                  const value = 60 + (index * 8) + Math.random() * 10
                  return (
                    <div key={month} className="flex items-center gap-3">
                      <span className="text-white/70 text-sm w-16">{month}</span>
                      <div className="flex-1 bg-white/20 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${value}%` }}
                        ></div>
                      </div>
                      <span className="text-white text-sm w-12">{Math.round(value)}%</span>
                    </div>
                  )
                })}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Revenue Chart */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {t.revenueChart}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-white/80">نمو الإيرادات الربعي</span>
                <span className="text-green-400 font-bold">+{systemMetrics.revenueGrowth}%</span>
              </div>

              {/* Revenue bars */}
              <div className="space-y-3">
                {[
                  { quarter: 'Q1 2023', value: 4200000, growth: 15.2 },
                  { quarter: 'Q2 2023', value: 4850000, growth: 18.5 },
                  { quarter: 'Q3 2023', value: 5200000, growth: 22.1 },
                  { quarter: 'Q4 2023', value: 5850000, growth: 25.8 }
                ].map((item, index) => (
                  <div key={item.quarter} className="flex items-center gap-3">
                    <span className="text-white/70 text-sm w-20">{item.quarter}</span>
                    <div className="flex-1 bg-white/20 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-green-500 to-emerald-500 h-3 rounded-full transition-all duration-1000"
                        style={{ width: `${(item.value / 6000000) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-white text-sm w-24">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR',
                        notation: 'compact'
                      }).format(item.value)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Department Analytics */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            {t.departmentChart}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: 'تقنية المعلومات', employees: 25, budget: 2500000, color: 'from-blue-500 to-blue-600' },
              { name: 'المالية', employees: 18, budget: 1800000, color: 'from-green-500 to-green-600' },
              { name: 'التسويق', employees: 15, budget: 1500000, color: 'from-purple-500 to-purple-600' },
              { name: 'الموارد البشرية', employees: 12, budget: 1200000, color: 'from-orange-500 to-orange-600' }
            ].map((dept, index) => (
              <div key={dept.name} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-center gap-3 mb-3">
                  <div className={`w-4 h-4 rounded-full bg-gradient-to-r ${dept.color}`}></div>
                  <h4 className="text-white font-medium text-sm">{dept.name}</h4>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-white/70 text-xs">الموظفون</span>
                    <span className="text-white text-xs font-medium">{dept.employees}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70 text-xs">الميزانية</span>
                    <span className="text-white text-xs font-medium">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR',
                        notation: 'compact'
                      }).format(dept.budget)}
                    </span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className={`bg-gradient-to-r ${dept.color} h-2 rounded-full transition-all duration-1000`}
                      style={{ width: `${(dept.employees / 25) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions & Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.quickActions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  className="group flex flex-col items-center gap-3 p-4 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-xs">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl">{t.recentActivities}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button">
                {t.viewDetails}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-3 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="p-2 rounded-lg bg-white/10">
                    {getSeverityIcon(activity.severity)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white text-sm font-medium mb-1">
                      {language === 'ar' ? activity.messageAr : activity.message}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-white/60">
                      <Clock className="h-3 w-3" />
                      <span>{activity.timestamp}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Security & Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {t.securityStatus}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-white/80">نقاط الأمان</span>
                <span className="text-green-400 font-bold">{systemMetrics.securityScore}/100</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full transition-all duration-1000"
                  style={{ width: `${systemMetrics.securityScore}%` }}
                ></div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 glass-card border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <Lock className="h-4 w-4 text-green-400" />
                    <span className="text-white/70 text-sm">التهديدات النشطة</span>
                  </div>
                  <p className="text-green-400 font-bold text-lg">{systemMetrics.activeThreats}</p>
                </div>
                <div className="p-3 glass-card border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-400" />
                    <span className="text-white/70 text-sm">تنبيهات الأمان</span>
                  </div>
                  <p className="text-yellow-400 font-bold text-lg">{systemMetrics.securityAlerts}</p>
                </div>
                <div className="p-3 glass-card border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <Shield className="h-4 w-4 text-blue-400" />
                    <span className="text-white/70 text-sm">الهجمات المحجوبة</span>
                  </div>
                  <p className="text-blue-400 font-bold text-lg">{systemMetrics.blockedAttacks}</p>
                </div>
                <div className="p-3 glass-card border-white/10">
                  <div className="flex items-center gap-2 mb-2">
                    <Database className="h-4 w-4 text-purple-400" />
                    <span className="text-white/70 text-sm">آخر نسخة احتياطية</span>
                  </div>
                  <p className="text-purple-400 font-bold text-sm">{systemMetrics.lastBackup}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              {t.performanceMetrics}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-white/80">الأداء العام</span>
                <span className="text-blue-400 font-bold">
                  {systemMetrics.serverLoad < 70 ? t.excellent : systemMetrics.serverLoad < 85 ? t.good : t.warning}
                </span>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">استخدام المعالج</span>
                  <span className="text-white text-sm">{systemMetrics.cpuUsage}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-1000 ${
                      systemMetrics.cpuUsage < 70 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                      systemMetrics.cpuUsage < 85 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                      'bg-gradient-to-r from-red-500 to-red-600'
                    }`}
                    style={{ width: `${systemMetrics.cpuUsage}%` }}
                  ></div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">استخدام الذاكرة</span>
                  <span className="text-white text-sm">{systemMetrics.memoryUsage}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-1000 ${
                      systemMetrics.memoryUsage < 70 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                      systemMetrics.memoryUsage < 85 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                      'bg-gradient-to-r from-red-500 to-red-600'
                    }`}
                    style={{ width: `${systemMetrics.memoryUsage}%` }}
                  ></div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">استخدام القرص</span>
                  <span className="text-white text-sm">{systemMetrics.diskUsage}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-1000 ${
                      systemMetrics.diskUsage < 70 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                      systemMetrics.diskUsage < 85 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                      'bg-gradient-to-r from-red-500 to-red-600'
                    }`}
                    style={{ width: `${systemMetrics.diskUsage}%` }}
                  ></div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="p-2 glass-card border-white/10">
                  <p className="text-white/70">وقت الاستجابة</p>
                  <p className="text-green-400 font-bold">{systemMetrics.responseTime}ms</p>
                </div>
                <div className="p-2 glass-card border-white/10">
                  <p className="text-white/70">معدل الأخطاء</p>
                  <p className="text-blue-400 font-bold">{systemMetrics.errorRate}%</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Efficiency Summary */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <Target className="h-5 w-5" />
            ملخص كفاءة النظام
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4 glass-card border-white/10">
              <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center">
                <TrendingUp className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-white font-medium mb-1">نمو الإيرادات</h4>
              <p className="text-green-400 text-2xl font-bold">+{systemMetrics.revenueGrowth}%</p>
              <p className="text-white/60 text-sm">هذا الربع</p>
            </div>

            <div className="text-center p-4 glass-card border-white/10">
              <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-white font-medium mb-1">نمو المستخدمين</h4>
              <p className="text-blue-400 text-2xl font-bold">+{systemMetrics.userGrowthRate}%</p>
              <p className="text-white/60 text-sm">هذا الشهر</p>
            </div>

            <div className="text-center p-4 glass-card border-white/10">
              <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-white font-medium mb-1">نجاح المشاريع</h4>
              <p className="text-purple-400 text-2xl font-bold">{systemMetrics.projectSuccessRate}%</p>
              <p className="text-white/60 text-sm">معدل النجاح</p>
            </div>

            <div className="text-center p-4 glass-card border-white/10">
              <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-between">
                <Server className="h-8 w-8 text-white" />
              </div>
              <h4 className="text-white font-medium mb-1">وقت التشغيل</h4>
              <p className="text-orange-400 text-2xl font-bold">{systemMetrics.systemUptime}%</p>
              <p className="text-white/60 text-sm">هذا الشهر</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
