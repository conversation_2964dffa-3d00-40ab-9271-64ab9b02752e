import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import type { RootState, AppDispatch } from '../../store'
import {
  User,
  Calendar,
  CheckSquare,
  Clock,
  Award,
  MessageSquare,
  FileText,
  TrendingUp,
  Target,
  Coffee,
  MapPin,
  Phone,
  Mail,
  RefreshCw,
  Plus,
  Eye
} from 'lucide-react'

interface EmployeeDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً بك',
    myProfile: 'ملفي الشخصي',
    myTasks: 'مهامي',
    mySchedule: 'جدولي',
    myAttendance: 'حضوري',
    leaveBalance: 'رصيد الإجازات',
    teamUpdates: 'تحديثات الفريق',
    quickActions: 'إجراءات سريعة',
    personalInfo: 'المعلومات الشخصية',
    workInfo: 'معلومات العمل',
    todayTasks: 'مهام اليوم',
    upcomingMeetings: 'الاجتماعات القادمة',
    recentMessages: 'الرسائل الحديثة',
    performanceOverview: 'نظرة عامة على الأداء',
    attendanceRecord: 'سجل الحضور',
    leaveRequests: 'طلبات الإجازة',
    tasksCompleted: 'المهام المكتملة',
    tasksInProgress: 'المهام قيد التنفيذ',
    tasksPending: 'المهام المعلقة',
    tasksOverdue: 'المهام المتأخرة',
    presentDays: 'أيام الحضور',
    absentDays: 'أيام الغياب',
    lateDays: 'أيام التأخير',
    overtimeHours: 'ساعات العمل الإضافي',
    annualLeave: 'الإجازة السنوية',
    sickLeave: 'الإجازة المرضية',
    emergencyLeave: 'الإجازة الطارئة',
    available: 'متاح',
    used: 'مستخدم',
    pending: 'معلق',
    requestLeave: 'طلب إجازة',
    viewProfile: 'عرض الملف الشخصي',
    updateInfo: 'تحديث المعلومات',
    viewTasks: 'عرض المهام',
    sendMessage: 'إرسال رسالة',
    viewCalendar: 'عرض التقويم',
    checkAttendance: 'تسجيل الحضور',
    refresh: 'تحديث',
    viewAll: 'عرض الكل',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    completed: 'مكتمل',
    inProgress: 'قيد التنفيذ',
    overdue: 'متأخر',
    today: 'اليوم',
    tomorrow: 'غداً',
    thisWeek: 'هذا الأسبوع'
  },
  en: {
    welcome: 'Welcome',
    myProfile: 'My Profile',
    myTasks: 'My Tasks',
    mySchedule: 'My Schedule',
    myAttendance: 'My Attendance',
    leaveBalance: 'Leave Balance',
    teamUpdates: 'Team Updates',
    quickActions: 'Quick Actions',
    personalInfo: 'Personal Information',
    workInfo: 'Work Information',
    todayTasks: "Today's Tasks",
    upcomingMeetings: 'Upcoming Meetings',
    recentMessages: 'Recent Messages',
    performanceOverview: 'Performance Overview',
    attendanceRecord: 'Attendance Record',
    leaveRequests: 'Leave Requests',
    tasksCompleted: 'Tasks Completed',
    tasksInProgress: 'Tasks In Progress',
    tasksPending: 'Tasks Pending',
    tasksOverdue: 'Tasks Overdue',
    presentDays: 'Present Days',
    absentDays: 'Absent Days',
    lateDays: 'Late Days',
    overtimeHours: 'Overtime Hours',
    annualLeave: 'Annual Leave',
    sickLeave: 'Sick Leave',
    emergencyLeave: 'Emergency Leave',
    available: 'Available',
    used: 'Used',
    pending: 'Pending',
    requestLeave: 'Request Leave',
    viewProfile: 'View Profile',
    updateInfo: 'Update Info',
    viewTasks: 'View Tasks',
    sendMessage: 'Send Message',
    viewCalendar: 'View Calendar',
    checkAttendance: 'Check Attendance',
    refresh: 'Refresh',
    viewAll: 'View All',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    completed: 'Completed',
    inProgress: 'In Progress',
    overdue: 'Overdue',
    today: 'Today',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week'
  }
}

export default function EmployeeDashboard({ language }: EmployeeDashboardProps) {
  const { user } = useSelector((state: RootState => state.auth)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Employee-specific data
  const [employeeData, setEmployeeData] = useState({
    tasks: {
      completed: 45,
      inProgress: 8,
      pending: 12,
      overdue: 2
    },
    attendance: {
      presentDays: 22,
      absentDays: 1,
      lateDays: 2,
      overtimeHours: 15
    },
    leave: {
      annual: { available: 25, used: 5, pending: 0 },
      sick: { available: 10, used: 2, pending: 1 },
      emergency: { available: 5, used: 0, pending: 0 }
    },
    performance: {
      score: 88,
      goalsCompleted: 7,
      totalGoals: 10,
      feedback: 'Excellent work on recent projects'
    }
  })

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const quickActions = [
    { title: t.requestLeave, icon: Calendar, href: '/hr/leave/request', color: 'from-blue-500 to-blue-600' },
    { title: t.viewTasks, icon: CheckSquare, href: '/projects/tasks', color: 'from-green-500 to-green-600' },
    { title: t.sendMessage, icon: MessageSquare, href: '/communication/messages', color: 'from-purple-500 to-purple-600' },
    { title: t.updateInfo, icon: User, href: '/profile', color: 'from-orange-500 to-orange-600' }
  ]

  const todayTasks = [
    {
      id: 1,
      title: 'Review project documentation',
      titleAr: 'مراجعة وثائق المشروع',
      priority: 'high',
      status: 'inProgress',
      dueTime: '2:00 PM',
      project: 'Mobile App Development'
    },
    {
      id: 2,
      title: 'Team meeting preparation',
      titleAr: 'التحضير لاجتماع الفريق',
      priority: 'medium',
      status: 'pending',
      dueTime: '4:00 PM',
      project: 'Website Redesign'
    },
    {
      id: 3,
      title: 'Update client presentation',
      titleAr: 'تحديث عرض العميل',
      priority: 'high',
      status: 'overdue',
      dueTime: '10:00 AM',
      project: 'Marketing Campaign'
    }
  ]

  const upcomingMeetings = [
    {
      id: 1,
      title: 'Weekly Team Standup',
      titleAr: 'اجتماع الفريق الأسبوعي',
      time: '10:00 AM',
      duration: '30 min',
      attendees: 8,
      location: 'Conference Room A'
    },
    {
      id: 2,
      title: 'Project Review',
      titleAr: 'مراجعة المشروع',
      time: '2:00 PM',
      duration: '1 hour',
      attendees: 5,
      location: 'Virtual Meeting'
    }
  ]

  const recentMessages = [
    {
      id: 1,
      sender: 'أحمد محمد',
      message: 'Great work on the latest feature!',
      messageAr: 'عمل رائع على الميزة الأخيرة!',
      timestamp: '10 minutes ago',
      unread: true
    },
    {
      id: 2,
      sender: 'فاطمة علي',
      message: 'Can we schedule a meeting for tomorrow?',
      messageAr: 'هل يمكننا جدولة اجتماع لغداً؟',
      timestamp: '1 hour ago',
      unread: false
    }
  ]

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-500/10'
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-500/10'
      case 'low':
        return 'border-l-green-500 bg-green-500/10'
      default:
        return 'border-l-blue-500 bg-blue-500/10'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t.welcome}, {user?.name}
          </h1>
          <p className="text-white/70">لوحة تحكم الموظف - إدارة مهامك وأنشطتك اليومية</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <Clock className="h-4 w-4 mr-2" />
            {t.checkAttendance}
          </Button>
        </div>
      </div>

      {/* Personal Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.tasksCompleted}</p>
                <p className="text-2xl font-bold text-green-400">{employeeData.tasks.completed}</p>
              </div>
              <CheckSquare className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.tasksInProgress}</p>
                <p className="text-2xl font-bold text-blue-400">{employeeData.tasks.inProgress}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.presentDays}</p>
                <p className="text-2xl font-bold text-emerald-400">{employeeData.attendance.presentDays}</p>
              </div>
              <Calendar className="h-8 w-8 text-emerald-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">Performance Score</p>
                <p className="text-2xl font-bold text-purple-400">{employeeData.performance.score}%</p>
              </div>
              <Award className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.quickActions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  className="group flex flex-col items-center gap-3 p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-4 rounded-2xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-8 w-8 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-sm">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Today's Tasks */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl">{t.todayTasks}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button">
                <Eye className="h-4 w-4 mr-2" />
                {t.viewAll}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {todayTasks.map((task) => (
                <div key={task.id} className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all border-l-4 ${getPriorityColor(task.priority)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-white font-medium">
                          {language === 'ar' ? task.titleAr : task.title}
                        </h4>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                          {t[task.status as keyof typeof t]}
                        </span>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-white/80">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{task.dueTime}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Target className="h-3 w-3" />
                          <span>{task.project}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Award className="h-3 w-3" />
                          <span className={`font-medium ${
                            task.priority === 'high' ? 'text-red-400' :
                            task.priority === 'medium' ? 'text-yellow-400' : 'text-green-400'
                          }`}>
                            {t[task.priority as keyof typeof t]}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button size="sm" className="glass-button">
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Leave Balance & Upcoming Meetings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t.leaveBalance}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {Object.entries(employeeData.leave).map(([type, data]) => (
                <div key={type} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-white/80 font-medium">
                      {t[`${type}Leave` as keyof typeof t]}
                    </span>
                    <span className="text-white text-sm">
                      {data.available - data.used} / {data.available}
                    </span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
                      style={{ width: `${((data.available - data.used) / data.available) * 100}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-white/60">
                    <span>{t.used}: {data.used}</span>
                    <span>{t.pending}: {data.pending}</span>
                  </div>
                </div>
              ))}
              <Button className="w-full glass-button mt-4">
                <Plus className="h-4 w-4 mr-2" />
                {t.requestLeave}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {t.upcomingMeetings}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingMeetings.map((meeting) => (
                <div key={meeting.id} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-start justify-between mb-3">
                    <h4 className="text-white font-medium">
                      {language === 'ar' ? meeting.titleAr : meeting.title}
                    </h4>
                    <span className="text-blue-400 text-sm font-medium">{meeting.time}</span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm text-white/80">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{meeting.duration}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      <span>{meeting.attendees} attendees</span>
                    </div>
                    <div className="flex items-center gap-1 col-span-2">
                      <MapPin className="h-3 w-3" />
                      <span>{meeting.location}</span>
                    </div>
                  </div>
                </div>
              ))}
              <Button variant="outline" className="w-full glass-button">
                <Calendar className="h-4 w-4 mr-2" />
                {t.viewCalendar}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
