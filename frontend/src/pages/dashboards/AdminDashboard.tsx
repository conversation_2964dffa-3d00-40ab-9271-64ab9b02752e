import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Building2, 
  TrendingUp, 
  DollarSign,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface DashboardStats {
  totalEmployees: number;
  totalDepartments: number;
  activeProjects: number;
  monthlyRevenue: number;
  pendingTasks: number;
  completedTasks: number;
}

interface AdminDashboardProps {
  language: 'ar' | 'en';
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ language }) => {
  const [stats, setStats] = useState<DashboardStats>({
    totalEmployees: 0,
    totalDepartments: 0,
    activeProjects: 0,
    monthlyRevenue: 0,
    pendingTasks: 0,
    completedTasks: 0
  });
  const [loading, setLoading] = useState<boolean>(true);

  const translations = {
    ar: {
      title: 'لوحة تحكم المدير',
      totalEmployees: 'إجمالي الموظفين',
      totalDepartments: 'إجمالي الأقسام',
      activeProjects: 'المشاريع النشطة',
      monthlyRevenue: 'الإيرادات الشهرية',
      pendingTasks: 'المهام المعلقة',
      completedTasks: 'المهام المكتملة',
      viewDetails: 'عرض التفاصيل',
      loading: 'جاري التحميل...'
    },
    en: {
      title: 'Admin Dashboard',
      totalEmployees: 'Total Employees',
      totalDepartments: 'Total Departments',
      activeProjects: 'Active Projects',
      monthlyRevenue: 'Monthly Revenue',
      pendingTasks: 'Pending Tasks',
      completedTasks: 'Completed Tasks',
      viewDetails: 'View Details',
      loading: 'Loading...'
    }
  };

  const t = translations[language];

  useEffect(() => {
    const fetchDashboardData = async (): Promise<void> => {
      try {
        setLoading(true);

        // Import dashboard API
        const { dashboardAPI } = await import('../../services/api');

        // Fetch real data from backend
        const dashboardData = await dashboardAPI.getStats();

        // Map backend data to component state
        setStats({
          totalEmployees: dashboardData.total_employees || 0,
          totalDepartments: dashboardData.total_departments || 0,
          activeProjects: dashboardData.active_projects || 0,
          monthlyRevenue: dashboardData.monthly_revenue || 0,
          pendingTasks: dashboardData.pending_tasks || 0,
          completedTasks: 0 // This would need to be added to backend API
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Set fallback data on error
        setStats({
          totalEmployees: 0,
          totalDepartments: 0,
          activeProjects: 0,
          monthlyRevenue: 0,
          pendingTasks: 0,
          completedTasks: 0
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat(language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const handleViewDetails = (section: string): void => {
    console.log(`Viewing details for: ${section}`);
    // Navigation logic would go here
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t.loading}</p>
        </div>
      </div>
    );
  }

  const statsCards = [
    {
      title: t.totalEmployees,
      value: stats.totalEmployees.toString(),
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      section: 'employees'
    },
    {
      title: t.totalDepartments,
      value: stats.totalDepartments.toString(),
      icon: Building2,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      section: 'departments'
    },
    {
      title: t.activeProjects,
      value: stats.activeProjects.toString(),
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      section: 'projects'
    },
    {
      title: t.monthlyRevenue,
      value: formatCurrency(stats.monthlyRevenue),
      icon: DollarSign,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      section: 'revenue'
    },
    {
      title: t.pendingTasks,
      value: stats.pendingTasks.toString(),
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      section: 'pending-tasks'
    },
    {
      title: t.completedTasks,
      value: stats.completedTasks.toString(),
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      section: 'completed-tasks'
    }
  ];

  return (
    <div className="p-6 space-y-6" dir={language === 'ar' ? 'rtl' : 'ltr'}>
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">{t.title}</h1>
        <Badge variant="outline" className="text-sm">
          {new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statsCards.map((card, index) => {
          const IconComponent = card.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {card.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${card.bgColor}`}>
                  <IconComponent className={`h-5 w-5 ${card.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900 mb-2">
                  {card.value}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewDetails(card.section)}
                  className="w-full"
                >
                  {t.viewDetails}
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default AdminDashboard;
