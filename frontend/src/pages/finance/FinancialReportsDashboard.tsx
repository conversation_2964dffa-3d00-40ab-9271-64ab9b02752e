/**
 * Financial Reports Dashboard
 * Central hub for all financial reports with quick access and overview
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  BarChart3,
  Pie<PERSON>hart,
  TrendingUp,
  Calendar,
  Download,
  Eye,
  RefreshCw,
  Building,
  CreditCard,
  DollarSign,
  Activity,
  ArrowRight,
  Clock,
  CheckCircle,
  Edit,
  Trash2
} from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useCrud } from '@/hooks/useCrud'
import { reportService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface FinancialReportsDashboardProps {
  language: 'ar' | 'en'
}

interface FinancialReport {
  id: number
  name: string
  name_ar?: string
  type: string
  description?: string
  description_ar?: string
  status: 'DRAFT' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  created_date: string
  completed_date?: string
  file_size?: number
  file_url?: string
  parameters?: any
  created_by: number
  created_by_name?: string
}

interface ReportSummary {
  name: string
  description: string
  last_generated: string
  status: 'READY' | 'GENERATING' | 'ERROR'
  key_metrics: {
    [key: string]: number | string
  }
}

interface DashboardData {
  profit_loss: ReportSummary
  balance_sheet: ReportSummary
  cash_flow: ReportSummary
  aging_reports: ReportSummary
  financial_ratios: {
    current_ratio: number
    quick_ratio: number
    debt_to_equity: number
    gross_profit_margin: number
    net_profit_margin: number
    return_on_assets: number
  }
  period_comparison: {
    revenue_growth: number
    expense_growth: number
    profit_growth: number
    cash_growth: number
  }
}

const translations = {
  ar: {
    financialReports: 'التقارير المالية',
    reportsDashboard: 'لوحة تحكم التقارير',
    quickAccess: 'الوصول السريع',
    keyMetrics: 'المؤشرات الرئيسية',
    periodComparison: 'مقارنة الفترات',
    financialRatios: 'النسب المالية',
    // Reports
    profitLossReport: 'قائمة الدخل',
    balanceSheetReport: 'الميزانية العمومية',
    cashFlowReport: 'قائمة التدفقات النقدية',
    agingReports: 'تقارير الأعمار',
    // Descriptions
    profitLossDesc: 'تحليل الإيرادات والمصروفات والربحية',
    balanceSheetDesc: 'الأصول والخصوم وحقوق الملكية',
    cashFlowDesc: 'التدفقات النقدية التشغيلية والاستثمارية والتمويلية',
    agingDesc: 'تحليل أعمار الذمم المدينة والدائنة',
    // Actions
    viewReport: 'عرض التقرير',
    generateReport: 'إنشاء التقرير',
    downloadPDF: 'تحميل PDF',
    scheduleReport: 'جدولة التقرير',
    refresh: 'تحديث',
    // Status
    ready: 'جاهز',
    generating: 'جاري الإنشاء',
    error: 'خطأ',
    lastGenerated: 'آخر إنشاء',
    // Metrics
    totalRevenue: 'إجمالي الإيرادات',
    netIncome: 'صافي الدخل',
    totalAssets: 'إجمالي الأصول',
    totalLiabilities: 'إجمالي الخصوم',
    operatingCashFlow: 'التدفق النقدي التشغيلي',
    freeCashFlow: 'التدفق النقدي الحر',
    // Ratios
    currentRatio: 'نسبة التداول',
    quickRatio: 'النسبة السريعة',
    debtToEquity: 'نسبة الدين إلى حقوق الملكية',
    grossProfitMargin: 'هامش الربح الإجمالي',
    netProfitMargin: 'هامش الربح الصافي',
    returnOnAssets: 'العائد على الأصول',
    // Growth
    revenueGrowth: 'نمو الإيرادات',
    expenseGrowth: 'نمو المصروفات',
    profitGrowth: 'نمو الأرباح',
    cashGrowth: 'نمو النقد',
    // Time periods
    thisMonth: 'هذا الشهر',
    thisQuarter: 'هذا الربع',
    thisYear: 'هذا العام',
    vsLastPeriod: 'مقارنة بالفترة السابقة'
  },
  en: {
    financialReports: 'Financial Reports',
    reportsDashboard: 'Reports Dashboard',
    quickAccess: 'Quick Access',
    keyMetrics: 'Key Metrics',
    periodComparison: 'Period Comparison',
    financialRatios: 'Financial Ratios',
    // Reports
    profitLossReport: 'Profit & Loss Report',
    balanceSheetReport: 'Balance Sheet Report',
    cashFlowReport: 'Cash Flow Report',
    agingReports: 'Aging Reports',
    // Descriptions
    profitLossDesc: 'Revenue, expenses, and profitability analysis',
    balanceSheetDesc: 'Assets, liabilities, and equity position',
    cashFlowDesc: 'Operating, investing, and financing cash flows',
    agingDesc: 'Accounts receivable and payable aging analysis',
    // Actions
    viewReport: 'View Report',
    generateReport: 'Generate Report',
    downloadPDF: 'Download PDF',
    scheduleReport: 'Schedule Report',
    refresh: 'Refresh',
    // Status
    ready: 'Ready',
    generating: 'Generating',
    error: 'Error',
    lastGenerated: 'Last Generated',
    // Metrics
    totalRevenue: 'Total Revenue',
    netIncome: 'Net Income',
    totalAssets: 'Total Assets',
    totalLiabilities: 'Total Liabilities',
    operatingCashFlow: 'Operating Cash Flow',
    freeCashFlow: 'Free Cash Flow',
    // Ratios
    currentRatio: 'Current Ratio',
    quickRatio: 'Quick Ratio',
    debtToEquity: 'Debt-to-Equity',
    grossProfitMargin: 'Gross Profit Margin',
    netProfitMargin: 'Net Profit Margin',
    returnOnAssets: 'Return on Assets',
    // Growth
    revenueGrowth: 'Revenue Growth',
    expenseGrowth: 'Expense Growth',
    profitGrowth: 'Profit Growth',
    cashGrowth: 'Cash Growth',
    // Time periods
    thisMonth: 'This Month',
    thisQuarter: 'This Quarter',
    thisYear: 'This Year',
    vsLastPeriod: 'vs Last Period'
  }
}

const FinancialReportsDashboard: React.FC<FinancialReportsDashboardProps> = ({ language }) => {
  const t = translations[language]
  const navigate = useNavigate()
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)

  // Use the generic CRUD hook for reports
  const {
    items: reports,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<FinancialReport>({
    service: reportService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'FAILED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): React.ReactElement => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-3 w-3" />
      case 'PROCESSING':
        return <RefreshCw className="h-3 w-3 animate-spin" />
      case 'DRAFT':
        return <FileText className="h-3 w-3" />
      case 'FAILED':
        return <Clock className="h-3 w-3" />
      default:
        return <FileText className="h-3 w-3" />
    }
  }

  const getReportTypeIcon = (type: string): React.ReactElement => {
    switch (type.toLowerCase()) {
      case 'profit_loss':
      case 'income_statement':
        return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'balance_sheet':
        return <Building className="h-4 w-4 text-blue-400" />
      case 'cash_flow':
        return <DollarSign className="h-4 w-4 text-purple-400" />
      case 'aging':
        return <Calendar className="h-4 w-4 text-orange-400" />
      default:
        return <BarChart3 className="h-4 w-4 text-gray-400" />
    }
  }

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return 'N/A'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  // Table columns configuration
  const columns: TableColumn<FinancialReport>[] = [
    {
      key: 'name',
      label: 'Report Name',
      sortable: true,
      render: (item: FinancialReport) => (
        <div className="flex items-center gap-2">
          {getReportTypeIcon(item.type)}
          <div>
            <span className="text-white font-medium">{item.name}</span>
            <div className="text-white/60 text-sm">{item.type}</div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (item: FinancialReport) => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{item.status}</span>
        </Badge>
      )
    },
    {
      key: 'created_by_name',
      label: 'Created By',
      render: (item: FinancialReport) => (
        <span className="text-white/80">{item.created_by_name || 'N/A'}</span>
      )
    },
    {
      key: 'created_date',
      label: 'Created Date',
      sortable: true,
      render: (item: FinancialReport) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{new Date(item.created_date).toLocaleDateString()}</span>
        </div>
      )
    },
    {
      key: 'completed_date',
      label: 'Completed Date',
      sortable: true,
      render: (item: FinancialReport) => (
        <div className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3 text-green-400" />
          <span className="text-white/80">
            {item.completed_date ? new Date(item.completed_date).toLocaleDateString() : 'N/A'}
          </span>
        </div>
      )
    },
    {
      key: 'file_size',
      label: 'File Size',
      render: (item: FinancialReport) => (
        <span className="text-white/80">{formatFileSize(item.file_size)}</span>
      )
    },
    {
      key: 'file_url',
      label: 'Actions',
      render: (item: FinancialReport) => (
        <div className="flex items-center gap-2">
          {item.file_url && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => window.open(item.file_url, '_blank')}
              className="text-blue-400 hover:text-blue-300"
            >
              <Download className="h-3 w-3" />
            </Button>
          )}
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<FinancialReport>[] = [
    {
      label: 'View',
      icon: Eye,
      onClick: (item: FinancialReport) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: (item: FinancialReport) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: 'Delete',
      icon: Trash2,
      onClick: (item: FinancialReport) => {
        if (confirm('Are you sure you want to delete this report?')) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: 'Report Name',
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: 'Report Name (Arabic)',
      type: 'text'
    },
    {
      name: 'type',
      label: 'Report Type',
      type: 'select',
      required: true,
      options: [
        { label: 'Profit & Loss', value: 'profit_loss' },
        { label: 'Balance Sheet', value: 'balance_sheet' },
        { label: 'Cash Flow', value: 'cash_flow' },
        { label: 'Aging Report', value: 'aging' },
        { label: 'Budget Report', value: 'budget' },
        { label: 'Expense Report', value: 'expense' },
        { label: 'Asset Report', value: 'asset' },
        { label: 'Custom Report', value: 'custom' }
      ]
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea'
    },
    {
      name: 'description_ar',
      label: 'Description (Arabic)',
      type: 'textarea'
    },
    {
      name: 'status',
      label: 'Status',
      type: 'select',
      required: true,
      options: [
        { label: 'Draft', value: 'DRAFT' },
        { label: 'Processing', value: 'PROCESSING' },
        { label: 'Completed', value: 'COMPLETED' },
        { label: 'Failed', value: 'FAILED' }
      ]
    },
    {
      name: 'parameters',
      label: 'Parameters JSON',
      type: 'textarea',
      placeholder: '{"start_date": "2024-01-01", "end_date": "2024-12-31"}'
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<FinancialReport>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'}/finance/reports/dashboard/`, {
          credentials: 'include',
          headers: { 'Content-Type': 'application/json' }
        })

        if (response.ok) {
          const data = await response.json()
          setDashboardData(data)
        } else {
          console.error('Failed to fetch dashboard data')
          // Set mock data as fallback
          setDashboardData(getMockDashboardData())
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
        // Set mock data as fallback
        setDashboardData(getMockDashboardData())
      }
    }

    fetchDashboardData()
  }, [])

  // Mock dashboard data fallback
  const getMockDashboardData = (): DashboardData => ({
    profit_loss: {
      name: 'Profit & Loss Statement',
      description: 'Revenue, expenses, and profitability analysis',
      last_generated: '2024-01-15',
      status: 'READY',
      key_metrics: {
        total_revenue: 2500000,
        net_income: 450000,
        gross_margin: 0.65
      }
    },
    balance_sheet: {
      name: 'Balance Sheet',
      description: 'Assets, liabilities, and equity position',
      last_generated: '2024-01-15',
      status: 'READY',
      key_metrics: {
        total_assets: 8500000,
        total_liabilities: 3200000,
        equity: 5300000
      }
    },
    cash_flow: {
      name: 'Cash Flow Statement',
      description: 'Operating, investing, and financing cash flows',
      last_generated: '2024-01-14',
      status: 'READY',
      key_metrics: {
        operating_cash_flow: 380000,
        investing_cash_flow: -150000,
        financing_cash_flow: -80000
      }
    },
    aging_reports: {
      name: 'Aging Reports',
      description: 'Accounts receivable and payable aging analysis',
      last_generated: '2024-01-15',
      status: 'READY',
      key_metrics: {
        current_receivables: 450000,
        overdue_receivables: 85000,
        current_payables: 320000
      }
    },
    financial_ratios: {
      current_ratio: 2.1,
      quick_ratio: 1.8,
      debt_to_equity: 0.6,
      gross_profit_margin: 0.65,
      net_profit_margin: 0.18,
      return_on_assets: 0.12
    },
    period_comparison: {
      revenue_growth: 0.15,
      expense_growth: 0.08,
      profit_growth: 0.22,
      cash_growth: 0.05
    }
  })

  return (
    <div className="space-y-6">
      {/* Dashboard Overview Cards */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-blue-600 to-blue-700 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">{t.totalRevenue}</p>
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('ar-SA', { style: 'currency', currency: 'SAR' })
                      .format(dashboardData.profit_loss.key_metrics.total_revenue as number)}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-600 to-green-700 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">{t.netIncome}</p>
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('ar-SA', { style: 'currency', currency: 'SAR' })
                      .format(dashboardData.profit_loss.key_metrics.net_income as number)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-600 to-purple-700 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">{t.totalAssets}</p>
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('ar-SA', { style: 'currency', currency: 'SAR' })
                      .format(dashboardData.balance_sheet.key_metrics.total_assets as number)}
                  </p>
                </div>
                <Building className="h-8 w-8 text-purple-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-600 to-orange-700 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">Cash Flow</p>
                  <p className="text-2xl font-bold">
                    {new Intl.NumberFormat('ar-SA', { style: 'currency', currency: 'SAR' })
                      .format(dashboardData.cash_flow.key_metrics.operating_cash_flow as number)}
                  </p>
                </div>
                <Activity className="h-8 w-8 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* CRUD Table for Reports */}
      <CrudTable
        title={t.financialReports}
        data={reports}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder="Search reports..."
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText="Create Report"
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? 'Create Report' : modalMode === 'edit' ? 'Edit Report' : 'View Report'}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}

export default FinancialReportsDashboard
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'READY':
        return 'text-green-600'
      case 'GENERATING':
        return 'text-yellow-600'
      case 'ERROR':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }
  const getGrowthColor = (growth: number): void => {
    if (growth === 0) return 'text-gray-500'
    return growth > 0 ? 'text-green-600' : 'text-red-600'
  }
  const getGrowthIcon = (growth: number): void => {
    if (growth === 0) return null
    return growth > 0 ? 
      <TrendingUp className="h-4 w-4 inline ml-1" /> : 
      <TrendingUp className="h-4 w-4 inline ml-1 rotate-180" />
  }

  const reportCards = [
    {
      title: t.profitLossReport,
      description: t.profitLossDesc,
      icon: BarChart3,
      color: 'blue',
      route: '/finance/reports/profit-loss',
      data: dashboardData?.profit_loss
    },
    {
      title: t.balanceSheetReport,
      description: t.balanceSheetDesc,
      icon: Building,
      color: 'green',
      route: '/finance/reports/balance-sheet',
      data: dashboardData?.balance_sheet
    },
    {
      title: t.cashFlowReport,
      description: t.cashFlowDesc,
      icon: Activity,
      color: 'purple',
      route: '/finance/reports/cash-flow',
      data: dashboardData?.cash_flow
    },
    {
      title: t.agingReports,
      description: t.agingDesc,
      icon: Clock,
      color: 'orange',
      route: '/finance/aging-reports',
      data: dashboardData?.aging_reports
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t.financialReports}</h1>
          <p className="text-gray-600 mt-1">{t.reportsDashboard}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchDashboardData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {t.refresh}
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            {t.downloadPDF}
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      {dashboardData && (<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{t.totalRevenue}</p>
                  <p className="text-2xl font-bold mt-1">
                    {formatCurrency(Number(dashboardData.profit_loss.key_metrics.total_revenue) || 0)}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{t.netIncome}</p>
                  <p className="text-2xl font-bold mt-1">
                    {formatCurrency(Number(dashboardData.profit_loss.key_metrics.net_income) || 0)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{t.totalAssets}</p>
                  <p className="text-2xl font-bold mt-1">
                    {formatCurrency(Number(dashboardData.balance_sheet.key_metrics.total_assets) || 0)}
                  </p>
                </div>
                <Building className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{t.operatingCashFlow}</p>
                  <p className="text-2xl font-bold mt-1">
                    {formatCurrency(Number(dashboardData.cash_flow.key_metrics.operating_cash_flow) || 0)}
                  </p>
                </div>
                <Activity className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Reports Quick Access */}
      <Card>
        <CardHeader>
          <CardTitle>{t.quickAccess}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {reportCards.map((report, index) => {
              const IconComponent = report.icon
              return (<Card key={index} className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <IconComponent className={`h-6 w-6 text-${report.color}-600`} />
                          <h3 className="font-semibold text-lg">{report.title}</h3>
                        </div>
                        <p className="text-gray-600 text-sm mb-4">{report.description}</p>
                        
                        {report.data && (<div className="flex items-center gap-2 mb-4">
                            {getStatusIcon(report.data.status)}
                            <span className={`text-sm ${getStatusColor(report.data.status)}`}>
                              {t[report.data.status.toLowerCase() as keyof typeof t] || report.data.status}
                            </span>
                            {report.data.last_generated && (<span className="text-xs text-gray-500 ml-2">
                                {t.lastGenerated}: {new Date(report.data.last_generated).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
                              </span>
                            )}
                          </div>
                        )}
                        
                        <div className="flex gap-2">
                          <Button 
                            size="sm" 
                            onClick={() => navigate(report.route)}
                            className="flex-1"
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            {t.viewReport}
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <ArrowRight className="h-5 w-5 text-gray-400 ml-4" />
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Financial Ratios */}
      {dashboardData && (<Card>
          <CardHeader>
            <CardTitle>{t.financialRatios}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">{t.currentRatio}</h4>
                <p className="text-2xl font-bold text-blue-600">
                  {formatRatio(dashboardData.financial_ratios.current_ratio)}
                </p>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">{t.grossProfitMargin}</h4>
                <p className="text-2xl font-bold text-green-600">
                  {formatPercent(dashboardData.financial_ratios.gross_profit_margin)}
                </p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-medium text-purple-800 mb-2">{t.returnOnAssets}</h4>
                <p className="text-2xl font-bold text-purple-600">
                  {formatPercent(dashboardData.financial_ratios.return_on_assets)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Period Comparison */}
      {dashboardData && (<Card>
          <CardHeader>
            <CardTitle>{t.periodComparison}</CardTitle>
            <p className="text-sm text-gray-600">{t.vsLastPeriod}</p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-sm font-medium text-gray-600 mb-1">{t.revenueGrowth}</p>
                <p className={`text-xl font-bold ${getGrowthColor(dashboardData.period_comparison.revenue_growth)}`}>
                  {formatPercent(Math.abs(dashboardData.period_comparison.revenue_growth))}
                  {getGrowthIcon(dashboardData.period_comparison.revenue_growth)}
                </p>
              </div>
              
              <div className="text-center">
                <p className="text-sm font-medium text-gray-600 mb-1">{t.expenseGrowth}</p>
                <p className={`text-xl font-bold ${getGrowthColor(dashboardData.period_comparison.expense_growth)}`}>
                  {formatPercent(Math.abs(dashboardData.period_comparison.expense_growth))}
                  {getGrowthIcon(dashboardData.period_comparison.expense_growth)}
                </p>
              </div>
              
              <div className="text-center">
                <p className="text-sm font-medium text-gray-600 mb-1">{t.profitGrowth}</p>
                <p className={`text-xl font-bold ${getGrowthColor(dashboardData.period_comparison.profit_growth)}`}>
                  {formatPercent(Math.abs(dashboardData.period_comparison.profit_growth))}
                  {getGrowthIcon(dashboardData.period_comparison.profit_growth)}
                </p>
              </div>
              
              <div className="text-center">
                <p className="text-sm font-medium text-gray-600 mb-1">{t.cashGrowth}</p>
                <p className={`text-xl font-bold ${getGrowthColor(dashboardData.period_comparison.cash_growth)}`}>
                  {formatPercent(Math.abs(dashboardData.period_comparison.cash_growth))}
                  {getGrowthIcon(dashboardData.period_comparison.cash_growth)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
export default FinancialReportsDashboard