/**
 * Collections Page - AR Collections Management
 * Manages overdue invoices, collection activities, and customer follow-ups
 */

import React, { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus, 
  Phone, 
  Mail,
  Calendar,
  DollarSign,
  Clock,
  AlertTriangle,
  Users,
  TrendingUp,
  FileText,
  MessageSquare,
  CheckCircle,
  XCircle
} from 'lucide-react'

interface CollectionsProps {
  language: 'ar' | 'en'
}

interface CollectionItem {
  id: number
  invoice_number: string
  customer_id: number
  customer_name: string
  customer_email: string
  customer_phone: string
  total_amount: number
  remaining_balance: number
  days_overdue: number
  due_date: string
  last_contact_date?: string
  last_contact_method?: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  collection_status: 'NEW' | 'CONTACTED' | 'PROMISED' | 'LEGAL' | 'WRITTEN_OFF'
  next_follow_up_date?: string
  notes?: string
  payment_plan?: boolean
}

interface CollectionActivity {
  id: number
  invoice_id: number
  activity_type: 'CALL' | 'EMAIL' | 'LETTER' | 'MEETING' | 'PAYMENT'
  activity_date: string
  description: string
  result: string
  created_by: string
}

const translations = {
  ar: {
    collections: 'التحصيلات',
    overdueInvoices: 'الفواتير المتأخرة',
    collectionActivities: 'أنشطة التحصيل',
    addActivity: 'إضافة نشاط',
    searchCollections: 'البحث في التحصيلات...',
    invoiceNumber: 'رقم الفاتورة',
    customer: 'العميل',
    amount: 'المبلغ',
    daysOverdue: 'الأيام المتأخرة',
    priority: 'الأولوية',
    status: 'الحالة',
    lastContact: 'آخر اتصال',
    nextFollowUp: 'المتابعة التالية',
    actions: 'الإجراءات',
    // Priority levels
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    urgent: 'عاجل',
    // Collection status
    new: 'جديد',
    contacted: 'تم الاتصال',
    promised: 'وعد بالدفع',
    legal: 'إجراء قانوني',
    writtenOff: 'شطب',
    // Activity types
    call: 'مكالمة',
    email: 'بريد إلكتروني',
    letter: 'خطاب',
    meeting: 'اجتماع',
    payment: 'دفعة',
    // Summary cards
    totalOverdue: 'إجمالي المتأخر',
    overdueCount: 'عدد المتأخرة',
    averageDaysOverdue: 'متوسط الأيام المتأخرة',
    collectionRate: 'معدل التحصيل',
    urgentCases: 'الحالات العاجلة',
    thisWeekTarget: 'هدف هذا الأسبوع',
    noCollectionsFound: 'لم يتم العثور على تحصيلات',
    allPriorities: 'جميع الأولويات',
    allStatuses: 'جميع الحالات',
    contactCustomer: 'اتصال بالعميل',
    sendEmail: 'إرسال بريد',
    scheduleFollowUp: 'جدولة متابعة',
    addNote: 'إضافة ملاحظة',
    paymentPlan: 'خطة دفع'
  },
  en: {
    collections: 'Collections',
    overdueInvoices: 'Overdue Invoices',
    collectionActivities: 'Collection Activities',
    addActivity: 'Add Activity',
    searchCollections: 'Search collections...',
    invoiceNumber: 'Invoice Number',
    customer: 'Customer',
    amount: 'Amount',
    daysOverdue: 'Days Overdue',
    priority: 'Priority',
    status: 'Status',
    lastContact: 'Last Contact',
    nextFollowUp: 'Next Follow-up',
    actions: 'Actions',
    // Priority levels
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    // Collection status
    new: 'New',
    contacted: 'Contacted',
    promised: 'Promised',
    legal: 'Legal',
    writtenOff: 'Written Off',
    // Activity types
    call: 'Call',
    email: 'Email',
    letter: 'Letter',
    meeting: 'Meeting',
    payment: 'Payment',
    // Summary cards
    totalOverdue: 'Total Overdue',
    overdueCount: 'Overdue Count',
    averageDaysOverdue: 'Average Days Overdue',
    collectionRate: 'Collection Rate',
    urgentCases: 'Urgent Cases',
    thisWeekTarget: 'This Week Target',
    noCollectionsFound: 'No collections found',
    allPriorities: 'All Priorities',
    allStatuses: 'All Statuses',
    contactCustomer: 'Contact Customer',
    sendEmail: 'Send Email',
    scheduleFollowUp: 'Schedule Follow-up',
    addNote: 'Add Note',
    paymentPlan: 'Payment Plan'
  }
}
const Collections: React.FC<CollectionsProps> = ({ language }) => {
  const t = translations[language]
  const [collections, setCollections] = useState<CollectionItem[]>([])
  const [activities, setActivities] = useState<CollectionActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [priorityFilter, setPriorityFilter] = useState<string>('all')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  useEffect(() => {
    fetchCollections()
    fetchActivities()
  }, [])
  const fetchCollections = async () => {
    try {
      setLoading(true)
      const token = (localStorage).getItem('token')
      const response = await fetch('/api/customer-invoices/overdue/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if ((response).ok) {
        const data = await (response).json()
        setCollections((data).results || data)
      }
    } catch (error) {
      console.error('Error fetching collections:', error)
    } finally {
      setLoading(false)
    }
  }
  const fetchActivities = async () => {
    try {
      const token = (localStorage).getItem('token')
      const response = await fetch('/api/collection-activities/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if ((response).ok) {
        const data = await (response).json()
        setActivities((data).results || data)
      }
    } catch (error) {
      console.error('Error fetching collection activities:', error)
    }
  }
  const filteredCollections = (collections).filter(item => {
    const matchesSearch = 
      (item).invoice_number.toLowerCase().includes((searchTerm).toLowerCase()) ||
      (item).customer_name.toLowerCase().includes((searchTerm).toLowerCase()) ||
      (item).customer_email.toLowerCase().includes((searchTerm).toLowerCase())
    
    const matchesPriority = priorityFilter === 'all' || (item).priority === priorityFilter
    const matchesStatus = statusFilter === 'all' || (item).collection_status === statusFilter
    
    return matchesSearch && matchesPriority && matchesStatus
  })
  const formatCurrency = (amount: number): string => {
    return new (Intl).NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')
  }
  const getPriorityBadge = (priority: string): void => {
    switch (priority) {
      case 'LOW':
        return <Badge variant="secondary">{(t).low}</Badge>
      case 'MEDIUM':
        return <Badge variant="outline">{(t).medium}</Badge>
      case 'HIGH':
        return <Badge variant="default" className="bg-orange-600">{(t).high}</Badge>
      case 'URGENT':
        return <Badge variant="destructive">{(t).urgent}</Badge>
      default:
        return <Badge variant="secondary">{priority}</Badge>
    }
  }
  const getStatusBadge = (status: string): void => {
    switch (status) {
      case 'NEW':
        return <Badge variant="secondary">{(t).new}</Badge>
      case 'CONTACTED':
        return <Badge variant="outline">{(t).contacted}</Badge>
      case 'PROMISED':
        return <Badge variant="default" className="bg-yellow-600">{(t).promised}</Badge>
      case 'LEGAL':
        return <Badge variant="destructive">{(t).legal}</Badge>
      case 'WRITTEN_OFF':
        return <Badge variant="secondary" className="bg-gray-600">{(t).writtenOff}</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }
  const getDaysOverdueColor = (days: number): void => {
    if (days <= 30) return 'text-yellow-600'
    if (days <= 60) return 'text-orange-600'
    return 'text-red-600'
  }

  // Calculate summary statistics
  const totalOverdue = (collections).reduce((sum, item) => sum + (item).remaining_balance, 0)
  const overdueCount = (collections).length
  const averageDaysOverdue = (collections).length > 0 ?
    (collections).reduce((sum, item) => sum + (item).days_overdue, 0) / (collections).length : 0
  const urgentCases = (collections).filter(item => (item).priority === 'URGENT').length

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t).collections}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة التحصيلات والمتابعة مع العملاء' : 'Collections Management & Customer Follow-up'}
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {(t).addActivity}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-red-50 to-orange-50 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).totalOverdue}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">{formatCurrency(totalOverdue)}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).overdueCount}</p>
                <p className="text-2xl font-bold mt-1">{overdueCount}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).averageDaysOverdue}</p>
                <p className="text-2xl font-bold mt-1">{(Math).round(averageDaysOverdue)}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).urgentCases}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">{urgentCases}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={(t).searchCollections}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm((e).target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={priorityFilter}
                onChange={(e: any) => setPriorityFilter((e).target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{(t).allPriorities}</option>
                <option value="LOW">{(t).low}</option>
                <option value="MEDIUM">{(t).medium}</option>
                <option value="HIGH">{(t).high}</option>
                <option value="URGENT">{(t).urgent}</option>
              </select>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e: any) => setStatusFilter((e).target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{(t).allStatuses}</option>
                <option value="NEW">{(t).new}</option>
                <option value="CONTACTED">{(t).contacted}</option>
                <option value="PROMISED">{(t).promised}</option>
                <option value="LEGAL">{(t).legal}</option>
                <option value="WRITTEN_OFF">{(t).writtenOff}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Collections Table */}
      <Card>
        <CardHeader>
          <CardTitle>{(t).overdueInvoices}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t).invoiceNumber}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t).customer}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{(t).amount}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t).daysOverdue}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t).priority}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t).status}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{(t).lastContact}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{(t).actions}</th>
                </tr>
              </thead>
              <tbody>
                {(filteredCollections).map((item) => (<tr key={(item).id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {(item).invoice_number}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <span className="font-medium">{(item).customer_name}</span>
                        <div className="flex items-center gap-2 mt-1">
                          <Mail className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{(item).customer_email}</span>
                        </div>
                        {(item).customer_phone && (<div className="flex items-center gap-2">
                            <Phone className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-500">{(item).customer_phone}</span>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency((item).remaining_balance)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <span className={`font-bold ${getDaysOverdueColor((item).days_overdue)}`}>
                        {(item).days_overdue}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-center">
                      {getPriorityBadge((item).priority)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      {getStatusBadge((item).collection_status)}
                    </td>
                    <td className="py-3 px-4">
                      {(item).last_contact_date ? (<div>
                          <span className="text-sm">{formatDate((item).last_contact_date)}</span>
                          <p className="text-xs text-gray-500">{(item).last_contact_method}</p>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">
                          {language === 'ar' ? 'لا يوجد' : 'None'}
                        </span>
                      )}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center gap-1">
                        <Button variant="outline" size="sm" title={(t).contactCustomer}>
                          <Phone className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm" title={(t).sendEmail}>
                          <Mail className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm" title={(t).addNote}>
                          <MessageSquare className="h-3 w-3" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {(filteredCollections).length === 0 && (<div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{(t).noCollectionsFound}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle>{(t).collectionActivities}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {(activities).slice(0, 5).map((activity) => (<div key={(activity).id} className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0">
                  {(activity).activity_type === 'CALL' && <Phone className="h-5 w-5 text-blue-600" />}
                  {(activity).activity_type === 'EMAIL' && <Mail className="h-5 w-5 text-green-600" />}
                  {(activity).activity_type === 'LETTER' && <FileText className="h-5 w-5 text-purple-600" />}
                  {(activity).activity_type === 'MEETING' && <Users className="h-5 w-5 text-orange-600" />}
                  {(activity).activity_type === 'PAYMENT' && <CheckCircle className="h-5 w-5 text-green-600" />}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{(activity).description}</h4>
                    <span className="text-sm text-gray-500">{formatDate((activity).activity_date)}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{(activity).result}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'ar' ? 'بواسطة' : 'by'} {(activity).created_by}
                  </p>
                </div>
              </div>
            ))}
            
            {(activities).length === 0 && (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {language === 'ar' ? 'لا توجد أنشطة تحصيل' : 'No collection activities'}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
export default Collections