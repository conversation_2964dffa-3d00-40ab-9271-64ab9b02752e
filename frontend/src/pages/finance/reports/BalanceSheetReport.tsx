/**
 * Balance Sheet Report
 * Comprehensive balance sheet with assets, liabilities, and equity
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Calendar,
  Download,
  Printer,
  TrendingUp,
  TrendingDown,
  Building,
  CreditCard,
  PieChart,
  FileText,
  RefreshCw,
  Scale
} from 'lucide-react'

interface BalanceSheetReportProps {
  language: 'ar' | 'en'
}

interface BSLineItem {
  account_code: string
  account_name: string
  current_period: number
  previous_period: number
  variance: number
  variance_percent: number
  account_type: 'ASSET' | 'LIABILITY' | 'EQUITY'
  is_subtotal?: boolean
  is_total?: boolean
  level: number
}

interface BSReport {
  company_name: string
  report_date: string
  comparison_date: string
  currency: string
  // Assets
  current_assets: BSLineItem[]
  non_current_assets: BSLineItem[]
  total_assets: BSLineItem
  // Liabilities
  current_liabilities: BSLineItem[]
  non_current_liabilities: BSLineItem[]
  total_liabilities: BSLineItem
  // Equity
  equity: BSLineItem[]
  total_equity: BSLineItem
  // Verification
  total_liabilities_and_equity: BSLineItem
  balance_check: boolean
  generated_at: string
}

const translations = {
  ar: {
    balanceSheet: 'الميزانية العمومية',
    statementOfFinancialPosition: 'بيان المركز المالي',
    asOf: 'كما في',
    comparedTo: 'مقارنة بـ',
    assets: 'الأصول',
    currentAssets: 'الأصول المتداولة',
    nonCurrentAssets: 'الأصول غير المتداولة',
    totalAssets: 'إجمالي الأصول',
    liabilities: 'الخصوم',
    currentLiabilities: 'الخصوم المتداولة',
    nonCurrentLiabilities: 'الخصوم غير المتداولة',
    totalLiabilities: 'إجمالي الخصوم',
    equity: 'حقوق الملكية',
    totalEquity: 'إجمالي حقوق الملكية',
    totalLiabilitiesAndEquity: 'إجمالي الخصوم وحقوق الملكية',
    currentPeriod: 'الفترة الحالية',
    previousPeriod: 'الفترة السابقة',
    variance: 'التغيير',
    variancePercent: 'نسبة التغيير',
    accountCode: 'رمز الحساب',
    accountName: 'اسم الحساب',
    amount: 'المبلغ',
    // Actions
    downloadPDF: 'تحميل PDF',
    print: 'طباعة',
    export: 'تصدير',
    refresh: 'تحديث',
    selectDate: 'اختيار التاريخ',
    // Status
    loading: 'جاري التحميل...',
    noData: 'لا توجد بيانات',
    generatedAt: 'تم إنشاؤه في',
    balanceVerified: 'الميزانية متوازنة',
    balanceError: 'خطأ في توازن الميزانية',
    // Ratios
    currentRatio: 'نسبة التداول',
    quickRatio: 'النسبة السريعة',
    debtToEquity: 'نسبة الدين إلى حقوق الملكية',
    assetTurnover: 'معدل دوران الأصول',
    workingCapital: 'رأس المال العامل',
    // Asset categories
    cashAndEquivalents: 'النقد وما في حكمه',
    accountsReceivable: 'الذمم المدينة',
    inventory: 'المخزون',
    prepaidExpenses: 'المصروفات المدفوعة مقدماً',
    propertyPlantEquipment: 'الممتلكات والمصانع والمعدات',
    intangibleAssets: 'الأصول غير الملموسة',
    // Liability categories
    accountsPayable: 'الذمم الدائنة',
    shortTermDebt: 'الديون قصيرة الأجل',
    accruedLiabilities: 'الخصوم المستحقة',
    longTermDebt: 'الديون طويلة الأجل',
    // Equity categories
    shareCapital: 'رأس المال',
    retainedEarnings: 'الأرباح المحتجزة',
    otherEquity: 'حقوق ملكية أخرى'
  },
  en: {
    balanceSheet: 'Balance Sheet',
    statementOfFinancialPosition: 'Statement of Financial Position',
    asOf: 'As of',
    comparedTo: 'Compared to',
    assets: 'Assets',
    currentAssets: 'Current Assets',
    nonCurrentAssets: 'Non-Current Assets',
    totalAssets: 'Total Assets',
    liabilities: 'Liabilities',
    currentLiabilities: 'Current Liabilities',
    nonCurrentLiabilities: 'Non-Current Liabilities',
    totalLiabilities: 'Total Liabilities',
    equity: 'Equity',
    totalEquity: 'Total Equity',
    totalLiabilitiesAndEquity: 'Total Liabilities and Equity',
    currentPeriod: 'Current Period',
    previousPeriod: 'Previous Period',
    variance: 'Variance',
    variancePercent: 'Variance %',
    accountCode: 'Account Code',
    accountName: 'Account Name',
    amount: 'Amount',
    // Actions
    downloadPDF: 'Download PDF',
    print: 'Print',
    export: 'Export',
    refresh: 'Refresh',
    selectDate: 'Select Date',
    // Status
    loading: 'Loading...',
    noData: 'No data available',
    generatedAt: 'Generated at',
    balanceVerified: 'Balance Sheet Verified',
    balanceError: 'Balance Sheet Error',
    // Ratios
    currentRatio: 'Current Ratio',
    quickRatio: 'Quick Ratio',
    debtToEquity: 'Debt-to-Equity',
    assetTurnover: 'Asset Turnover',
    workingCapital: 'Working Capital',
    // Asset categories
    cashAndEquivalents: 'Cash and Equivalents',
    accountsReceivable: 'Accounts Receivable',
    inventory: 'Inventory',
    prepaidExpenses: 'Prepaid Expenses',
    propertyPlantEquipment: 'Property, Plant & Equipment',
    intangibleAssets: 'Intangible Assets',
    // Liability categories
    accountsPayable: 'Accounts Payable',
    shortTermDebt: 'Short-term Debt',
    accruedLiabilities: 'Accrued Liabilities',
    longTermDebt: 'Long-term Debt',
    // Equity categories
    shareCapital: 'Share Capital',
    retainedEarnings: 'Retained Earnings',
    otherEquity: 'Other Equity'
  }
}
const BalanceSheetReport: React.FC<BalanceSheetReportProps> = ({ language }) => {
  const t = translations[language]
  const [report, setReport] = useState<BSReport | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0])
  const [comparisonDate, setComparisonDate] = useState(new Date((Date).now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  )
  useEffect(() => {
    fetchReport()
  }, [selectedDate, comparisonDate])
  const fetchReport = async () => {
    try {
      setLoading(true)
      const token = (localStorage).getItem('token')
      const response = await fetch(`/api/financial-reports/balance-sheet/?date=${selectedDate}&comparison=${comparisonDate}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if ((response).ok) {
        const data = await (response).json()
        setReport(data)
      }
    } catch (error) {
      console.error('Error fetching balance sheet:', error)
    } finally {
      setLoading(false)
    }
  }
  const formatCurrency = (amount: number): string => {
    return new (Intl).NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }
  const formatPercent = (percent: number): string => {
    return new (Intl).NumberFormat('ar-SA', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(percent / 100)
  }
  const formatRatio = (ratio: number): string => {
    return new (Intl).NumberFormat('ar-SA', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(ratio)
  }
  const getVarianceColor = (variance: number): void => {
    if (variance === 0) return 'text-gray-500'
    return variance > 0 ? 'text-green-600' : 'text-red-600'
  }
  const getVarianceIcon = (variance: number): void => {
    if (variance === 0) return null
    return variance > 0 ? 
      <TrendingUp className="h-4 w-4 inline ml-1" /> : 
      <TrendingDown className="h-4 w-4 inline ml-1" />
  }
  const renderLineItem = (item: BSLineItem): void => {
    const indentClass = `pl-${(item).level * 4}`
    const fontWeight = (item).is_total ? 'font-bold' : (item).is_subtotal ? 'font-semibold' : 'font-normal'
    const borderClass = (item).is_total ? 'border-t-2 border-b-2 border-gray-800' : 
                       (item).is_subtotal ? 'border-t border-gray-400' : ''

    return (<tr key={(item).account_code} className={`${borderClass} hover:bg-gray-50`}>
        <td className={`py-2 px-4 ${indentClass} ${fontWeight}`}>
          {(item).account_code && (<span className="text-gray-500 text-sm mr-2">{(item).account_code}</span>
          )}
          {(item).account_name}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight}`}>
          {formatCurrency((item).current_period)}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight}`}>
          {formatCurrency((item).previous_period)}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight} ${getVarianceColor((item).variance)}`}>
          {formatCurrency((Math).abs((item).variance))}
          {getVarianceIcon((item).variance)}
        </td>
        <td className={`py-2 px-4 text-right ${fontWeight} ${getVarianceColor((item).variance)}`}>
          {(item).variance_percent !== 0 && formatPercent((Math).abs((item).variance_percent))}
        </td>
      </tr>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!report) {
    return (<div className="text-center py-8">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">{(t).noData}</p>
      </div>
    )
  }

  // Calculate financial ratios
  const currentAssets = (report).current_assets.reduce((sum, item) => sum + (item).current_period, 0)
  const currentLiabilities = (report).current_liabilities.reduce((sum, item) => sum + (item).current_period, 0)
  const totalAssets = (report).total_assets.current_period
  const totalLiabilities = (report).total_liabilities.current_period
  const totalEquity = (report).total_equity.current_period
  const currentRatio = currentLiabilities > 0 ? currentAssets / currentLiabilities : 0
  const debtToEquityRatio = totalEquity > 0 ? totalLiabilities / totalEquity : 0
  const workingCapital = currentAssets - currentLiabilities

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t).balanceSheet}</h1>
          <p className="text-gray-600 mt-1">{(t).statementOfFinancialPosition}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchReport}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {(t).refresh}
          </Button>
          <Button variant="outline">
            <Printer className="h-4 w-4 mr-2" />
            {(t).print}
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            {(t).downloadPDF}
          </Button>
        </div>
      </div>

      {/* Date Selection */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="font-medium">{(t).selectDate}:</span>
            </div>
            <div className="flex gap-2 items-center">
              <input
                type="date"
                value={selectedDate}
                onChange={(e: any) => setSelectedDate((e).target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-gray-500">{(t).comparedTo}</span>
              <input
                type="date"
                value={comparisonDate}
                onChange={(e: any) => setComparisonDate((e).target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Balance Verification */}
      <Card className={(report).balance_check ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2">
            <Scale className={`h-5 w-5 ${(report).balance_check ? 'text-green-600' : 'text-red-600'}`} />
            <span className={`font-medium ${(report).balance_check ? 'text-green-800' : 'text-red-800'}`}>
              {(report).balance_check ? (t).balanceVerified : (t).balanceError}
            </span>
            <span className="text-sm text-gray-600 ml-4">
              {(t).totalAssets}: {formatCurrency(totalAssets)} = 
              {(t).totalLiabilitiesAndEquity}: {formatCurrency((report).total_liabilities_and_equity.current_period)}
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Key Financial Ratios */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).currentRatio}</p>
                <p className="text-2xl font-bold mt-1">{formatRatio(currentRatio)}</p>
              </div>
              <CreditCard className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).debtToEquity}</p>
                <p className="text-2xl font-bold mt-1">{formatRatio(debtToEquityRatio)}</p>
              </div>
              <PieChart className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).workingCapital}</p>
                <p className="text-2xl font-bold mt-1">{formatCurrency(workingCapital)}</p>
              </div>
              <Building className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).totalAssets}</p>
                <p className="text-2xl font-bold mt-1">{formatCurrency(totalAssets)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Balance Sheet */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>{(report).company_name}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {(t).balanceSheet} - {(t).asOf} {new Date((report).report_date).toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
              </p>
            </div>
            <div className="text-right text-sm text-gray-500">
              {(t).generatedAt}: {new Date((report).generated_at).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-gray-800">
                  <th className="text-left py-3 px-4 font-bold">{(t).accountName}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t).currentPeriod}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t).previousPeriod}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t).variance}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t).variancePercent}</th>
                </tr>
              </thead>
              <tbody>
                {/* Assets Section */}
                <tr className="bg-blue-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-blue-800 uppercase">
                    {(t).assets}
                  </td>
                </tr>
                
                {/* Current Assets */}
                <tr className="bg-blue-25">
                  <td colSpan={5} className="py-2 px-4 font-semibold text-blue-700">
                    {(t).currentAssets}
                  </td>
                </tr>
                {(report).current_assets.map(item => renderLineItem(item))}
                
                {/* Non-Current Assets */}
                <tr className="bg-blue-25">
                  <td colSpan={5} className="py-2 px-4 font-semibold text-blue-700">
                    {(t).nonCurrentAssets}
                  </td>
                </tr>
                {(report).non_current_assets.map(item => renderLineItem(item))}
                
                {/* Total Assets */}
                {renderLineItem((report).total_assets)}
                
                {/* Liabilities Section */}
                <tr className="bg-red-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-red-800 uppercase">
                    {(t).liabilities}
                  </td>
                </tr>
                
                {/* Current Liabilities */}
                <tr className="bg-red-25">
                  <td colSpan={5} className="py-2 px-4 font-semibold text-red-700">
                    {(t).currentLiabilities}
                  </td>
                </tr>
                {(report).current_liabilities.map(item => renderLineItem(item))}
                
                {/* Non-Current Liabilities */}
                <tr className="bg-red-25">
                  <td colSpan={5} className="py-2 px-4 font-semibold text-red-700">
                    {(t).nonCurrentLiabilities}
                  </td>
                </tr>
                {(report).non_current_liabilities.map(item => renderLineItem(item))}
                
                {/* Total Liabilities */}
                {renderLineItem((report).total_liabilities)}
                
                {/* Equity Section */}
                <tr className="bg-green-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-green-800 uppercase">
                    {(t).equity}
                  </td>
                </tr>
                {(report).equity.map(item => renderLineItem(item))}
                
                {/* Total Equity */}
                {renderLineItem((report).total_equity)}
                
                {/* Total Liabilities and Equity */}
                {renderLineItem((report).total_liabilities_and_equity)}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
export default BalanceSheetReport