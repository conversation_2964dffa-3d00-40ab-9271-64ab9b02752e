/**
 * Profit & Loss Report (Income Statement)
 * Comprehensive P&L statement with period comparison and drill-down capabilities
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Calendar,
  Download,
  Printer,
  TrendingUp,
  TrendingDown,
  DollarSign,
  BarChart3,
  FileText,
  Filter,
  RefreshCw
} from 'lucide-react'

interface ProfitLossReportProps {
  language: 'ar' | 'en'
}

interface PLLineItem {
  account_code: string
  account_name: string
  current_period: number
  previous_period: number
  variance: number
  variance_percent: number
  account_type: 'REVENUE' | 'EXPENSE' | 'COGS'
  is_subtotal?: boolean
  is_total?: boolean
  level: number
}

interface PLReport {
  company_name: string
  report_period: string
  comparison_period: string
  currency: string
  revenue: PLLineItem[]
  cost_of_goods_sold: PLLineItem[]
  gross_profit: PLLineItem
  operating_expenses: PLLineItem[]
  operating_income: PLLineItem
  other_income: PLLineItem[]
  other_expenses: PLLineItem[]
  net_income_before_tax: PLLineItem
  tax_expense: PLLineItem
  net_income: PLLineItem
  generated_at: string
}

const translations = {
  ar: {
    profitLossReport: 'قائمة الدخل',
    incomeStatement: 'بيان الأرباح والخسائر',
    forPeriod: 'للفترة',
    comparedTo: 'مقارنة بـ',
    revenue: 'الإيرادات',
    totalRevenue: 'إجمالي الإيرادات',
    costOfGoodsSold: 'تكلفة البضاعة المباعة',
    grossProfit: 'إجمالي الربح',
    operatingExpenses: 'المصروفات التشغيلية',
    operatingIncome: 'الدخل التشغيلي',
    otherIncome: 'إيرادات أخرى',
    otherExpenses: 'مصروفات أخرى',
    netIncomeBeforeTax: 'صافي الدخل قبل الضريبة',
    taxExpense: 'مصروف الضريبة',
    netIncome: 'صافي الدخل',
    currentPeriod: 'الفترة الحالية',
    previousPeriod: 'الفترة السابقة',
    variance: 'التغيير',
    variancePercent: 'نسبة التغيير',
    accountCode: 'رمز الحساب',
    accountName: 'اسم الحساب',
    amount: 'المبلغ',
    // Actions
    downloadPDF: 'تحميل PDF',
    print: 'طباعة',
    export: 'تصدير',
    refresh: 'تحديث',
    selectPeriod: 'اختيار الفترة',
    // Periods
    thisMonth: 'هذا الشهر',
    lastMonth: 'الشهر الماضي',
    thisQuarter: 'هذا الربع',
    lastQuarter: 'الربع الماضي',
    thisYear: 'هذا العام',
    lastYear: 'العام الماضي',
    custom: 'مخصص',
    // Status
    loading: 'جاري التحميل...',
    noData: 'لا توجد بيانات',
    generatedAt: 'تم إنشاؤه في',
    // Metrics
    grossProfitMargin: 'هامش الربح الإجمالي',
    operatingMargin: 'هامش التشغيل',
    netProfitMargin: 'هامش الربح الصافي',
    revenueGrowth: 'نمو الإيرادات',
    // Formatting
    increase: 'زيادة',
    decrease: 'انخفاض',
    favorable: 'إيجابي',
    unfavorable: 'سلبي'
  },
  en: {
    profitLossReport: 'Profit & Loss Report',
    incomeStatement: 'Income Statement',
    forPeriod: 'For Period',
    comparedTo: 'Compared to',
    revenue: 'Revenue',
    totalRevenue: 'Total Revenue',
    costOfGoodsSold: 'Cost of Goods Sold',
    grossProfit: 'Gross Profit',
    operatingExpenses: 'Operating Expenses',
    operatingIncome: 'Operating Income',
    otherIncome: 'Other Income',
    otherExpenses: 'Other Expenses',
    netIncomeBeforeTax: 'Net Income Before Tax',
    taxExpense: 'Tax Expense',
    netIncome: 'Net Income',
    currentPeriod: 'Current Period',
    previousPeriod: 'Previous Period',
    variance: 'Variance',
    variancePercent: 'Variance %',
    accountCode: 'Account Code',
    accountName: 'Account Name',
    amount: 'Amount',
    // Actions
    downloadPDF: 'Download PDF',
    print: 'Print',
    export: 'Export',
    refresh: 'Refresh',
    selectPeriod: 'Select Period',
    // Periods
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    thisQuarter: 'This Quarter',
    lastQuarter: 'Last Quarter',
    thisYear: 'This Year',
    lastYear: 'Last Year',
    custom: 'Custom',
    // Status
    loading: 'Loading...',
    noData: 'No data available',
    generatedAt: 'Generated at',
    // Metrics
    grossProfitMargin: 'Gross Profit Margin',
    operatingMargin: 'Operating Margin',
    netProfitMargin: 'Net Profit Margin',
    revenueGrowth: 'Revenue Growth',
    // Formatting
    increase: 'Increase',
    decrease: 'Decrease',
    favorable: 'Favorable',
    unfavorable: 'Unfavorable'
  }
}
const ProfitLossReport: React.FC<ProfitLossReportProps> = ({ language }) => {
  const t = translations[language]
  const [report, setReport] = useState<PLReport | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth')
  const [comparisonPeriod, setComparisonPeriod] = useState('lastMonth')
  useEffect(() => {
    fetchReport()
  }, [selectedPeriod, comparisonPeriod])
  const fetchReport = async () => {
    try {
      setLoading(true)
      const token = (localStorage).getItem('token')
      const response = await fetch(`/api/financial-reports/profit-loss/?period=${selectedPeriod}&comparison=${comparisonPeriod}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if ((response).ok) {
        const data = await (response).json()
        setReport(data)
      }
    } catch (error) {
      console.error('Error fetching P&L report:', error)
    } finally {
      setLoading(false)
    }
  }
  const formatCurrency = (amount: number): string => {
    return new (Intl).NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }
  const formatPercent = (percent: number): string => {
    return new (Intl).NumberFormat('ar-SA', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(percent / 100)
  }
  const getVarianceColor = (variance: number, isRevenue: boolean = false): void => {
    if (variance === 0) return 'text-gray-500'
    
    // For revenue, positive variance is good (green)
    // For expenses, negative variance is good (green)
    const isPositive = variance > 0
    const isFavorable = isRevenue ? isPositive : !isPositive
    
    return isFavorable ? 'text-green-600' : 'text-red-600'
  }
  const getVarianceIcon = (variance: number): void => {
    if (variance === 0) return null
    return variance > 0 ? 
      <TrendingUp className="h-4 w-4 inline ml-1" /> : 
      <TrendingDown className="h-4 w-4 inline ml-1" />
  }
  const renderLineItem = (item: PLLineItem, isRevenue: boolean = false): void => {
    const indentClass = `pl-${(item).level * 4}`
    const fontWeight = (item).is_total ? 'font-bold' : (item).is_subtotal ? 'font-semibold' : 'font-normal'
    const borderClass = (item).is_total ? 'border-t-2 border-b-2 border-gray-800' : 
                       (item).is_subtotal ? 'border-t border-gray-400' : ''

    return (<tr key={(item).account_code} className={`${borderClass} hover:bg-gray-50`}>
        <td className={`py-2 px-4 ${indentClass} ${fontWeight}`}>
          {(item).account_code && (<span className="text-gray-500 text-sm mr-2">{(item).account_code}</span>
          )}
          {(item).account_name}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight}`}>
          {formatCurrency((item).current_period)}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight}`}>
          {formatCurrency((item).previous_period)}
        </td>
        <td className={`py-2 px-4 text-right font-mono ${fontWeight} ${getVarianceColor((item).variance, isRevenue)}`}>
          {formatCurrency((Math).abs((item).variance))}
          {getVarianceIcon((item).variance)}
        </td>
        <td className={`py-2 px-4 text-right ${fontWeight} ${getVarianceColor((item).variance, isRevenue)}`}>
          {(item).variance_percent !== 0 && formatPercent((Math).abs((item).variance_percent))}
        </td>
      </tr>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!report) {
    return (<div className="text-center py-8">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">{(t).noData}</p>
      </div>
    )
  }

  // Calculate key ratios
  const totalRevenue = (report).revenue.reduce((sum, item) => sum + (item).current_period, 0)
  const grossProfitMargin = totalRevenue > 0 ? ((report).gross_profit.current_period / totalRevenue) * 100 : 0
  const operatingMargin = totalRevenue > 0 ? ((report).operating_income.current_period / totalRevenue) * 100 : 0
  const netProfitMargin = totalRevenue > 0 ? ((report).net_income.current_period / totalRevenue) * 100 : 0

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t).profitLossReport}</h1>
          <p className="text-gray-600 mt-1">{(t).incomeStatement}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={fetchReport}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {(t).refresh}
          </Button>
          <Button variant="outline">
            <Printer className="h-4 w-4 mr-2" />
            {(t).print}
          </Button>
          <Button>
            <Download className="h-4 w-4 mr-2" />
            {(t).downloadPDF}
          </Button>
        </div>
      </div>

      {/* Period Selection */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="font-medium">{(t).selectPeriod}:</span>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedPeriod}
                onChange={(e: any) => setSelectedPeriod((e).target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="thisMonth">{(t).thisMonth}</option>
                <option value="lastMonth">{(t).lastMonth}</option>
                <option value="thisQuarter">{(t).thisQuarter}</option>
                <option value="lastQuarter">{(t).lastQuarter}</option>
                <option value="thisYear">{(t).thisYear}</option>
                <option value="lastYear">{(t).lastYear}</option>
              </select>
              <span className="self-center text-gray-500">{(t).comparedTo}</span>
              <select
                value={comparisonPeriod}
                onChange={(e: any) => setComparisonPeriod((e).target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="lastMonth">{(t).lastMonth}</option>
                <option value="lastQuarter">{(t).lastQuarter}</option>
                <option value="lastYear">{(t).lastYear}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).grossProfitMargin}</p>
                <p className="text-2xl font-bold mt-1">{formatPercent(grossProfitMargin)}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).operatingMargin}</p>
                <p className="text-2xl font-bold mt-1">{formatPercent(operatingMargin)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).netProfitMargin}</p>
                <p className="text-2xl font-bold mt-1">{formatPercent(netProfitMargin)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).revenueGrowth}</p>
                <p className="text-2xl font-bold mt-1">
                  {(report).revenue.length > 0 && formatPercent((report).revenue[0].variance_percent)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* P&L Statement */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>{(report).company_name}</CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {(t).incomeStatement} - {(report).report_period}
              </p>
            </div>
            <div className="text-right text-sm text-gray-500">
              {(t).generatedAt}: {new Date((report).generated_at).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-gray-800">
                  <th className="text-left py-3 px-4 font-bold">{(t).accountName}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t).currentPeriod}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t).previousPeriod}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t).variance}</th>
                  <th className="text-right py-3 px-4 font-bold">{(t).variancePercent}</th>
                </tr>
              </thead>
              <tbody>
                {/* Revenue Section */}
                <tr className="bg-blue-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-blue-800 uppercase">
                    {(t).revenue}
                  </td>
                </tr>
                {(report).revenue.map(item => renderLineItem(item, true))}
                
                {/* Cost of Goods Sold */}
                <tr className="bg-red-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-red-800 uppercase">
                    {(t).costOfGoodsSold}
                  </td>
                </tr>
                {(report).cost_of_goods_sold.map(item => renderLineItem(item))}
                
                {/* Gross Profit */}
                {renderLineItem((report).gross_profit, true)}
                
                {/* Operating Expenses */}
                <tr className="bg-orange-50">
                  <td colSpan={5} className="py-2 px-4 font-bold text-orange-800 uppercase">
                    {(t).operatingExpenses}
                  </td>
                </tr>
                {(report).operating_expenses.map(item => renderLineItem(item))}
                
                {/* Operating Income */}
                {renderLineItem((report).operating_income, true)}
                
                {/* Other Income */}
                {(report).other_income.length > 0 && (<>
                    <tr className="bg-green-50">
                      <td colSpan={5} className="py-2 px-4 font-bold text-green-800 uppercase">
                        {(t).otherIncome}
                      </td>
                    </tr>
                    {(report).other_income.map(item => renderLineItem(item, true))}
                  </>
                )}
                
                {/* Other Expenses */}
                {(report).other_expenses.length > 0 && (<>
                    <tr className="bg-gray-50">
                      <td colSpan={5} className="py-2 px-4 font-bold text-gray-800 uppercase">
                        {(t).otherExpenses}
                      </td>
                    </tr>
                    {(report).other_expenses.map(item => renderLineItem(item))}
                  </>
                )}
                
                {/* Net Income Before Tax */}
                {renderLineItem((report).net_income_before_tax, true)}
                
                {/* Tax Expense */}
                {renderLineItem((report).tax_expense)}
                
                {/* Net Income */}
                {renderLineItem((report).net_income, true)}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
export default ProfitLossReport