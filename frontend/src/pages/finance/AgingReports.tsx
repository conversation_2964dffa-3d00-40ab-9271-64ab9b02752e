/**
 * Aging Reports Page - AP and AR Aging Analysis
 * Provides aging analysis for both accounts payable and receivable
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Users,
  Building,
  FileText,
  Download
} from 'lucide-react'

interface AgingReportsProps {
  language: 'ar' | 'en'
}

interface AgingData {
  vendor_id?: number
  customer_id?: number
  vendor_name?: string
  customer_name?: string
  current: number
  '1-30_days': number
  '31-60_days': number
  '60+_days': number
  total: number
}

const translations = {
  ar: {
    agingReports: 'تقارير الأعمار',
    accountsPayable: 'الحسابات الدائنة',
    accountsReceivable: 'الحسابات المدينة',
    vendorAging: 'أعمار الموردين',
    customerAging: 'أعمار العملاء',
    current: 'الحالي',
    days1to30: '1-30 يوم',
    days31to60: '31-60 يوم',
    days60Plus: '60+ يوم',
    total: 'الإجمالي',
    vendor: 'المورد',
    customer: 'العميل',
    amount: 'المبلغ',
    exportReport: 'تصدير التقرير',
    summary: 'الملخص',
    totalOutstanding: 'إجمالي المستحق',
    overdueAmount: 'المبلغ المتأخر',
    averageDaysOutstanding: 'متوسط الأيام المستحقة',
    noDataFound: 'لم يتم العثور على بيانات',
    refreshData: 'تحديث البيانات',
    asOfDate: 'كما في تاريخ',
    payableAging: 'أعمار المدفوعات',
    receivableAging: 'أعمار المقبوضات'
  },
  en: {
    agingReports: 'Aging Reports',
    accountsPayable: 'Accounts Payable',
    accountsReceivable: 'Accounts Receivable',
    vendorAging: 'Vendor Aging',
    customerAging: 'Customer Aging',
    current: 'Current',
    days1to30: '1-30 Days',
    days31to60: '31-60 Days',
    days60Plus: '60+ Days',
    total: 'Total',
    vendor: 'Vendor',
    customer: 'Customer',
    amount: 'Amount',
    exportReport: 'Export Report',
    summary: 'Summary',
    totalOutstanding: 'Total Outstanding',
    overdueAmount: 'Overdue Amount',
    averageDaysOutstanding: 'Average Days Outstanding',
    noDataFound: 'No data found',
    refreshData: 'Refresh Data',
    asOfDate: 'As of Date',
    payableAging: 'Payable Aging',
    receivableAging: 'Receivable Aging'
  }
}
const AgingReports: React.FC<AgingReportsProps> = ({ language }) => {
  const t = translations[language]
  const [vendorAging, setVendorAging] = useState<AgingData[]>([])
  const [customerAging, setCustomerAging] = useState<AgingData[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'payable' | 'receivable'>('payable')
  useEffect(() => {
    fetchAgingData()
  }, [])
  const fetchAgingData = async () => {
    try {
      setLoading(true)
      const token = (localStorage).getItem('token')
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      }

      // Fetch vendor aging
      const vendorResponse = await fetch('/api/vendors/aging_report/', { headers })
      if ((vendorResponse).ok) {
        const vendorData = await (vendorResponse).json()
        setVendorAging(vendorData)
      }

      // Fetch customer aging
      const customerResponse = await fetch('/api/customer-invoices/aging_report/', { headers })
      if ((customerResponse).ok) {
        const customerData = await (customerResponse).json()
        setCustomerAging(customerData)
      }
    } catch (error) {
      console.error('Error fetching aging data:', error)
    } finally {
      setLoading(false)
    }
  }
  const formatCurrency = (amount: number): string => {
    return new (Intl).NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }
  const calculateTotals = (data: AgingData[]): void => {
    return (data).reduce((acc, item) => ({
      current: (acc).current + (item).current,
      days1to30: (acc).days1to30 + item['1-30_days'],
      days31to60: (acc).days31to60 + item['31-60_days'],
      days60Plus: (acc).days60Plus + item['60+_days'],
      total: (acc).total + (item).total
    }), {
      current: 0,
      days1to30: 0,
      days31to60: 0,
      days60Plus: 0,
      total: 0
    })
  }
  const getAgingColor = (days: string): void => {
    switch (days) {
      case 'current':
        return 'text-green-600'
      case '1-30':
        return 'text-yellow-600'
      case '31-60':
        return 'text-orange-600'
      case '60+':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const vendorTotals = calculateTotals(vendorAging)
  const customerTotals = calculateTotals(customerAging)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{(t).agingReports}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'تحليل أعمار الحسابات المدينة والدائنة' : 'Accounts Payable & Receivable Aging Analysis'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={fetchAgingData}>
            <TrendingUp className="h-4 w-4 mr-2" />
            {(t).refreshData}
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {(t).exportReport}
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).accountsPayable}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">{formatCurrency((vendorTotals).total)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{(t).accountsReceivable}</p>
                <p className="text-2xl font-bold mt-1 text-green-600">{formatCurrency((customerTotals).total)}</p>
              </div>
              <TrendingDown className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AP {(t).overdueAmount}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">
                  {formatCurrency((vendorTotals).days31to60 + (vendorTotals).days60Plus)}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">AR {(t).overdueAmount}</p>
                <p className="text-2xl font-bold mt-1 text-orange-600">
                  {formatCurrency((customerTotals).days31to60 + (customerTotals).days60Plus)}
                </p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setActiveTab('payable')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'payable'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Building className="h-4 w-4 inline mr-2" />
          {(t).payableAging}
        </button>
        <button
          onClick={() => setActiveTab('receivable')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'receivable'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Users className="h-4 w-4 inline mr-2" />
          {(t).receivableAging}
        </button>
      </div>

      {/* Aging Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            {activeTab === 'payable' ? (t).vendorAging : (t).customerAging}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">
                    {activeTab === 'payable' ? (t).vendor : (t).customer}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 text-green-600">
                    {(t).current}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 text-yellow-600">
                    {(t).days1to30}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 text-orange-600">
                    {(t).days31to60}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 text-red-600">
                    {(t).days60Plus}
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">
                    {(t).total}
                  </th>
                </tr>
              </thead>
              <tbody>
                {(activeTab === 'payable' ? vendorAging : customerAging).map((item, index) => (<tr key={index} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <span className="font-medium">
                        {activeTab === 'payable' ? (item).vendor_name : (item).customer_name}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-right font-mono text-green-600">
                      {formatCurrency((item).current)}
                    </td>
                    <td className="py-3 px-4 text-right font-mono text-yellow-600">
                      {formatCurrency(item['1-30_days'])}
                    </td>
                    <td className="py-3 px-4 text-right font-mono text-orange-600">
                      {formatCurrency(item['31-60_days'])}
                    </td>
                    <td className="py-3 px-4 text-right font-mono text-red-600">
                      {formatCurrency(item['60+_days'])}
                    </td>
                    <td className="py-3 px-4 text-right font-mono font-bold">
                      {formatCurrency((item).total)}
                    </td>
                  </tr>
                ))}
                
                {/* Totals Row */}
                <tr className="border-t-2 border-gray-300 bg-gray-50 font-bold">
                  <td className="py-3 px-4 font-bold">{(t).total}</td>
                  <td className="py-3 px-4 text-right font-mono text-green-600">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals).current : (customerTotals).current)}
                  </td>
                  <td className="py-3 px-4 text-right font-mono text-yellow-600">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals).days1to30 : (customerTotals).days1to30)}
                  </td>
                  <td className="py-3 px-4 text-right font-mono text-orange-600">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals).days31to60 : (customerTotals).days31to60)}
                  </td>
                  <td className="py-3 px-4 text-right font-mono text-red-600">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals).days60Plus : (customerTotals).days60Plus)}
                  </td>
                  <td className="py-3 px-4 text-right font-mono font-bold text-lg">
                    {formatCurrency(activeTab === 'payable' ? (vendorTotals).total : (customerTotals).total)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          {((activeTab === 'payable' ? vendorAging : customerAging).length === 0) && (<div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{(t).noDataFound}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* As of Date */}
      <div className="text-center text-sm text-gray-500">
        <Calendar className="h-4 w-4 inline mr-1" />
        {(t).asOfDate}: {new Date().toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')}
      </div>
    </div>
  )
}
export default AgingReports