import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  TrendingUp, 
  TrendingDown, 
  RefreshCw, 
  Calculator,
  ArrowRightLeft,
  Upload,
  Download
} from 'lucide-react';
// Language prop will be passed from parent component

interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  is_base_currency: boolean;
  is_active: boolean;
}

interface ExchangeRate {
  from_currency: string;
  to_currency: string;
  rate: number;
  date: string;
}

interface CurrencyConverter {
  fromCurrency: string;
  toCurrency: string;
  amount: string;
  convertedAmount: string;
}

interface ExchangeRateDashboardProps {
  language: string;
}
const ExchangeRateDashboard: React.FC<ExchangeRateDashboardProps> = ({ language }) => {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [currentRates, setCurrentRates] = useState<ExchangeRate[]>([]);
  const [loading, setLoading] = useState(true);
  const [isConverterOpen, setIsConverterOpen] = useState(false);
  const [isBulkUpdateOpen, setIsBulkUpdateOpen] = useState(false);
  const [converter, setConverter] = useState<CurrencyConverter>({
    fromCurrency: '',
    toCurrency: '',
    amount: '1',
    convertedAmount: ''
  });

  const [bulkRates, setBulkRates] = useState('');
  useEffect(() => {
    fetchCurrencies();
    fetchCurrentRates();
  }, []);
  const fetchCurrencies: any = async () => {
    try {
      const response = await fetch('/api/currencies/active/');
      if ((response).ok) {
        const data: any = await (response).json();
        setCurrencies(data);
      }
    } catch (error) {
      console.error('Error fetching currencies:', error);
    }
  };
  const fetchCurrentRates: any = async () => {
    try {
      const response = await fetch('/api/exchange-rates/current_rates/');
      if ((response).ok) {
        const data: any = await (response).json();
        setCurrentRates(data);
      }
    } catch (error) {
      console.error('Error fetching current rates:', error);
    } finally {
      setLoading(false);
    }
  };
  const convertCurrency: any = async () => {
    if (!(converter).fromCurrency || !(converter).toCurrency || !(converter).amount) {
      return;
    }

    try {
      const response: any = await fetch(`/api/exchange-rates/get_rate/?from_currency=${(converter).fromCurrency}&to_currency=${(converter).toCurrency}`
      );
      
      if ((response).ok) {
        const data: any = await (response).json();
        const convertedAmount: any = (parseFloat((converter).amount) * (data).rate).toFixed(6);
        setConverter(prev => ({ ...prev, convertedAmount }));
      }
    } catch (error) {
      console.error('Error converting currency:', error);
    }
  };
  const handleBulkUpdate: any = async () => {
    try {
      const rates = (bulkRates).split('\n').map(line => {
        const [from_currency, to_currency, rate, effective_date] = (line).split(',');
        return {
          from_currency: from_currency?.trim(),
          to_currency: to_currency?.trim(),
          rate: parseFloat(rate?.trim()),
          effective_date: effective_date?.trim() || new Date().toISOString().split('T')[0],
          source: 'manual'
        };
      }).filter(rate => (rate).from_currency && (rate).to_currency && !isNaN((rate).rate));

      const response: any = await fetch('/api/exchange-rates/bulk_update/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ rates }),
      });

      if ((response).ok) {
        fetchCurrentRates();
        setIsBulkUpdateOpen(false);
        setBulkRates('');
      }
    } catch (error) {
      console.error('Error bulk updating rates:', error);
    }
  };
  const getBaseCurrency: any = (): void => {
    return (currencies).find(c => (c).is_base_currency);
  };
  const formatRate: any = (rate: number): string => {
    return (rate).toFixed(6);
  };
  const getRateChange: any = (fromCode: string, toCode: string): void => {
    // Calculate real rate change from historical data
    const currentRate = (exchangeRates).find(rate =>
      (rate).from_currency === fromCode && (rate).to_currency === toCode
    );

    if (currentRate && (currentRate).previous_rate) {
      const change: any = ((currentRate).rate - (currentRate).previous_rate) / (currentRate).previous_rate;
      return change;
    }

    return 0; // No change if no historical data
  };
  const exportRates: any = (): void => {
    const csvContent = [
      'From Currency,To Currency,Rate,Date',
      ...(currentRates).map(rate => 
        `${(rate).from_currency},${(rate).to_currency},${(rate).rate},${(rate).date}`
      )
    ].join('\n');
    const blob: any = new Blob([csvContent], { type: 'text/csv' });
    const url: any = window.URL.createObjectURL(blob);
    const a: any = (document).createElement('a');
    (a).href = url;
    (a).download = `exchange-rates-${new Date().toISOString().split('T')[0]}.csv`;
    (a).click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  const baseCurrency: any = getBaseCurrency();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'لوحة أسعار الصرف' : 'Exchange Rate Dashboard'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? `أسعار الصرف الحالية مقابل ${baseCurrency?.code || 'العملة الأساسية'}`
              : `Current exchange rates against ${baseCurrency?.code || 'base currency'}`
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportRates}>
            <Download className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          
          <Dialog open={isBulkUpdateOpen} onOpenChange={setIsBulkUpdateOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'تحديث مجمع' : 'Bulk Update'}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'تحديث أسعار الصرف بالجملة' : 'Bulk Update Exchange Rates'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>
                    {language === 'ar' 
                      ? 'أسعار الصرف (CSV: من_العملة,إلى_العملة,السعر,التاريخ)'
                      : 'Exchange Rates (CSV: from_currency,to_currency,rate,date)'
                    }
                  </Label>
                  <textarea
                    className="w-full h-40 p-3 border rounded-md font-mono text-sm"
                    value={bulkRates}
                    onChange={(e: any) => setBulkRates((e).target.value)}
                    placeholder={`USD,SAR,(3).75,2024-01-20
EUR,SAR,(4).10,2024-01-20
GBP,SAR,(4).75,2024-01-20`}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleBulkUpdate} className="flex-1">
                    {language === 'ar' ? 'تحديث الأسعار' : 'Update Rates'}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setIsBulkUpdateOpen(false)}
                  >
                    {language === 'ar' ? 'إلغاء' : 'Cancel'}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isConverterOpen} onOpenChange={setIsConverterOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Calculator className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'محول العملات' : 'Currency Converter'}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'محول العملات' : 'Currency Converter'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>{language === 'ar' ? 'من' : 'From'}</Label>
                    <Select 
                      value={(converter).fromCurrency} 
                      onValueChange={(value) => setConverter({...converter, fromCurrency: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={language === 'ar' ? 'اختر العملة' : 'Select currency'} />
                      </SelectTrigger>
                      <SelectContent>
                        {(currencies).map(currency => (
                          <SelectItem key={(currency).id} value={(currency).code}>
                            {(currency).code} - {(currency).name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>{language === 'ar' ? 'إلى' : 'To'}</Label>
                    <Select 
                      value={(converter).toCurrency} 
                      onValueChange={(value) => setConverter({...converter, toCurrency: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={language === 'ar' ? 'اختر العملة' : 'Select currency'} />
                      </SelectTrigger>
                      <SelectContent>
                        {(currencies).map(currency => (
                          <SelectItem key={(currency).id} value={(currency).code}>
                            {(currency).code} - {(currency).name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'المبلغ' : 'Amount'}</Label>
                  <Input
                    type="number"
                    value={(converter).amount}
                    onChange={(e: any) => setConverter({...converter, amount: (e).target.value})}
                    placeholder="(1).00"
                  />
                </div>
                <Button onClick={convertCurrency} className="w-full">
                  <ArrowRightLeft className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'تحويل' : 'Convert'}
                </Button>
                {(converter).convertedAmount && (<div className="p-4 bg-muted rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {(converter).amount} {(converter).fromCurrency} = {(converter).convertedAmount} {(converter).toCurrency}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>

          <Button onClick={fetchCurrentRates}>
            <RefreshCw className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </Button>
        </div>
      </div>

      {/* Exchange Rates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {(currentRates).map((rate, index) => {
          const change: any = getRateChange((rate).from_currency, (rate).to_currency);
          const isPositive: any = change > 0;
          
          return (<Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center justify-between">
                  <span>{(rate).from_currency}/{(rate).to_currency}</span>
                  {isPositive ? (
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-500" />
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-2xl font-bold">
                    {formatRate((rate).rate)}
                  </div>
                  <div className="flex items-center justify-between">
                    <Badge 
                      variant={isPositive ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {isPositive ? '+' : ''}{(change * 100).toFixed(2)}%
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {new Date((rate).date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    1 {(rate).from_currency} = {formatRate((rate).rate)} {(rate).to_currency}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Base Currency Info */}
      {baseCurrency && (<Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              {language === 'ar' ? 'معلومات العملة الأساسية' : 'Base Currency Information'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{(baseCurrency).code}</div>
                <div className="text-sm text-muted-foreground">{(baseCurrency).name}</div>
                <Badge className="mt-2">
                  {language === 'ar' ? 'العملة الأساسية' : 'Base Currency'}
                </Badge>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{(currentRates).length}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'أسعار الصرف النشطة' : 'Active Exchange Rates'}
                </div>
              </div>
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold">{(currencies).length}</div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'العملات المدعومة' : 'Supported Currencies'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Calculator className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'محول العملات' : 'Currency Converter'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <TrendingUp className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'تحليل الاتجاهات' : 'Trend Analysis'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <RefreshCw className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'تحديث تلقائي' : 'Auto Update'}</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Download className="h-6 w-6 mb-2" />
              <span>{language === 'ar' ? 'تصدير البيانات' : 'Export Data'}</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ExchangeRateDashboard;
