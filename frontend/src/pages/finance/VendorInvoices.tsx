/**
 * Vendor Invoices Page - Accounts Payable Invoice Management
 * Manages vendor invoices with approval workflow and payment tracking
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus, 
  FileText, 
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  Edit,
  Download,
  Filter
} from 'lucide-react'

interface VendorInvoicesProps {
  language: 'ar' | 'en'
}

interface VendorInvoice {
  id: number
  invoice_number: string
  vendor_invoice_number: string
  vendor: number
  vendor_name: string
  vendor_code: string
  invoice_date: string
  due_date: string
  description: string
  subtotal: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  paid_amount: number
  remaining_balance: number
  status: 'DRAFT' | 'PENDING' | 'APPROVED' | 'PAID' | 'CANCELLED' | 'OVERDUE'
  department_name?: string
  project_name?: string
  approved_by_name?: string
  created_by_name: string
  is_overdue: boolean
  days_overdue: number
  created_at: string
}

const translations = {
  ar: {
    vendorInvoices: 'فواتير الموردين',
    addInvoice: 'إضافة فاتورة',
    searchInvoices: 'البحث في الفواتير...',
    invoiceNumber: 'رقم الفاتورة',
    vendorInvoiceNumber: 'رقم فاتورة المورد',
    vendor: 'المورد',
    invoiceDate: 'تاريخ الفاتورة',
    dueDate: 'تاريخ الاستحقاق',
    totalAmount: 'المبلغ الإجمالي',
    paidAmount: 'المبلغ المدفوع',
    remainingBalance: 'الرصيد المتبقي',
    status: 'الحالة',
    actions: 'الإجراءات',
    view: 'عرض',
    edit: 'تعديل',
    approve: 'اعتماد',
    pay: 'دفع',
    download: 'تحميل',
    // Status translations
    draft: 'مسودة',
    pending: 'في الانتظار',
    approved: 'معتمد',
    paid: 'مدفوع',
    cancelled: 'ملغي',
    overdue: 'متأخر',
    // Summary cards
    totalInvoices: 'إجمالي الفواتير',
    pendingApproval: 'في انتظار الاعتماد',
    overdueInvoices: 'الفواتير المتأخرة',
    totalOutstanding: 'إجمالي المستحق',
    noInvoicesFound: 'لم يتم العثور على فواتير',
    allStatuses: 'جميع الحالات',
    daysOverdue: 'يوم متأخر'
  },
  en: {
    vendorInvoices: 'Vendor Invoices',
    addInvoice: 'Add Invoice',
    searchInvoices: 'Search invoices...',
    invoiceNumber: 'Invoice Number',
    vendorInvoiceNumber: 'Vendor Invoice #',
    vendor: 'Vendor',
    invoiceDate: 'Invoice Date',
    dueDate: 'Due Date',
    totalAmount: 'Total Amount',
    paidAmount: 'Paid Amount',
    remainingBalance: 'Remaining Balance',
    status: 'Status',
    actions: 'Actions',
    view: 'View',
    edit: 'Edit',
    approve: 'Approve',
    pay: 'Pay',
    download: 'Download',
    // Status translations
    draft: 'Draft',
    pending: 'Pending',
    approved: 'Approved',
    paid: 'Paid',
    cancelled: 'Cancelled',
    overdue: 'Overdue',
    // Summary cards
    totalInvoices: 'Total Invoices',
    pendingApproval: 'Pending Approval',
    overdueInvoices: 'Overdue Invoices',
    totalOutstanding: 'Total Outstanding',
    noInvoicesFound: 'No invoices found',
    allStatuses: 'All Statuses',
    daysOverdue: 'days overdue'
  }
}
const VendorInvoices: React.FC<VendorInvoicesProps> = ({ language }) => {
  const t = translations[language]
  const [invoices, setInvoices] = useState<VendorInvoice[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  useEffect(() => {
    fetchInvoices()
  }, [])
  const fetchInvoices = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      const response = await fetch('/api/vendor-invoices/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (response.ok) {
        const data = await response.json()
        setInvoices(data.results || data)
      }
    } catch (error) {
      console.error('Error fetching vendor invoices:', error)
    } finally {
      setLoading(false)
    }
  }
  const handleApprove = async (invoiceId: number) => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch(`/api/vendor-invoices/${invoiceId}/approve/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (response.ok) {
        fetchInvoices() // Refresh the list
      }
    } catch (error) {
      console.error('Error approving invoice:', error)
    }
  }
  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = 
      invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.vendor_invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.vendor_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter
    
    return matchesSearch && matchesStatus
  })
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }
  const formatDate = (dateString: string): string => {
    return new DatedateString.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')
  }
  const getStatusBadge = (status: string, isOverdue: boolean): void => {
    if (isOverdue && status !== 'PAID') {
      return <Badge variant="destructive">{t.overdue}</Badge>
    }
    
    switch (status) {
      case 'DRAFT':
        return <Badge variant="secondary">{t.draft}</Badge>
      case 'PENDING':
        return <Badge variant="outline">{t.pending}</Badge>
      case 'APPROVED':
        return <Badge variant="default">{t.approved}</Badge>
      case 'PAID':
        return <Badge variant="default" className="bg-green-600">{t.paid}</Badge>
      case 'CANCELLED':
        return <Badge variant="destructive">{t.cancelled}</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  // Calculate summary statistics
  const totalInvoices = invoices.length
  const pendingApproval = invoices.filter(inv => inv.status === 'PENDING').length
  const overdueInvoices = invoices.filter(inv => inv.is_overdue).length
  const totalOutstanding = invoices
    .filter(inv => inv.status !== 'PAID' && inv.status !== 'CANCELLED')
    .reduce((sum, inv) => sum + inv.remaining_balance, 0)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t.vendorInvoices}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة فواتير الموردين والحسابات الدائنة' : 'Vendor Invoice Management & Accounts Payable'}
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {t.addInvoice}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.totalInvoices}</p>
                <p className="text-2xl font-bold mt-1">{totalInvoices}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.pendingApproval}</p>
                <p className="text-2xl font-bold mt-1">{pendingApproval}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.overdueInvoices}</p>
                <p className="text-2xl font-bold mt-1">{overdueInvoices}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.totalOutstanding}</p>
                <p className="text-2xl font-bold mt-1">{formatCurrency(totalOutstanding)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={t.searchInvoices}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e: any) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{t.allStatuses}</option>
                <option value="DRAFT">{t.draft}</option>
                <option value="PENDING">{t.pending}</option>
                <option value="APPROVED">{t.approved}</option>
                <option value="PAID">{t.paid}</option>
                <option value="OVERDUE">{t.overdue}</option>
                <option value="CANCELLED">{t.cancelled}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Invoices Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t.vendorInvoices}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.invoiceNumber}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.vendor}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.invoiceDate}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.dueDate}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{t.totalAmount}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{t.remainingBalance}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{t.status}</th>
                  <th className="text-center py-3 px-4 font-medium text-gray-900">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredInvoices.map((invoice) => (<tr key={invoice.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {invoice.invoice_number}
                        </code>
                        <p className="text-xs text-gray-500 mt-1">
                          {invoice.vendor_invoice_number}
                        </p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        <span className="font-medium">{invoice.vendor_name}</span>
                        <p className="text-sm text-gray-500">{invoice.vendor_code}</p>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate(invoice.invoice_date)}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate(invoice.due_date)}
                        {invoice.is_overdue && (<span className="text-xs text-red-600">
                            ({invoice.days_overdue} {t.daysOverdue})
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency(invoice.total_amount)}
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      {formatCurrency(invoice.remaining_balance)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      {getStatusBadge(invoice.status, invoice.is_overdue)}
                    </td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        {invoice.status === 'PENDING' && (
                          <Button 
                            variant="default" 
                            size="sm"
                            onClick={() => handleApprove(invoice.id)}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        {invoice.status === 'APPROVED' && (
                          <Button variant="default" size="sm" className="bg-green-600">
                            <DollarSign className="h-4 w-4" />
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredInvoices.length === 0 && (<div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{t.noInvoicesFound}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
export default VendorInvoices