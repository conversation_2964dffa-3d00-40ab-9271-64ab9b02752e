/**
 * Payments Page - Payment Management for AP and AR
 * Manages both vendor payments and customer payments
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Plus, 
  DollarSign, 
  Calendar,
  CreditCard,
  Building,
  Users,
  ArrowUpRight,
  ArrowDownLeft,
  Banknote,
  Smartphone,
  CheckCircle
} from 'lucide-react'

interface PaymentsProps {
  language: 'ar' | 'en'
}

interface Payment {
  id: number
  payment_number: string
  payment_type: 'VENDOR_PAYMENT' | 'CUSTOMER_PAYMENT'
  payment_method: 'CASH' | 'CHECK' | 'BANK_TRANSFER' | 'CREDIT_CARD' | 'ONLINE'
  payment_date: string
  amount: number
  currency: string
  exchange_rate: number
  vendor_invoice?: number
  vendor_invoice_number?: string
  customer_invoice?: number
  customer_invoice_number?: string
  bank_account?: string
  check_number?: string
  reference_number?: string
  description?: string
  created_by_name: string
  created_at: string
}

const translations = {
  ar: {
    payments: 'المدفوعات',
    addPayment: 'إضافة دفعة',
    searchPayments: 'البحث في المدفوعات...',
    paymentNumber: 'رقم الدفعة',
    paymentType: 'نوع الدفعة',
    paymentMethod: 'طريقة الدفع',
    paymentDate: 'تاريخ الدفع',
    amount: 'المبلغ',
    reference: 'المرجع',
    description: 'الوصف',
    createdBy: 'أنشأ بواسطة',
    // Payment types
    vendorPayment: 'دفعة مورد',
    customerPayment: 'دفعة عميل',
    // Payment methods
    cash: 'نقد',
    check: 'شيك',
    bankTransfer: 'تحويل بنكي',
    creditCard: 'بطاقة ائتمان',
    online: 'دفع إلكتروني',
    // Summary cards
    totalPayments: 'إجمالي المدفوعات',
    vendorPayments: 'مدفوعات الموردين',
    customerPayments: 'مدفوعات العملاء',
    netCashFlow: 'صافي التدفق النقدي',
    noPaymentsFound: 'لم يتم العثور على مدفوعات',
    allTypes: 'جميع الأنواع',
    allMethods: 'جميع الطرق',
    todaysPayments: 'مدفوعات اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر'
  },
  en: {
    payments: 'Payments',
    addPayment: 'Add Payment',
    searchPayments: 'Search payments...',
    paymentNumber: 'Payment Number',
    paymentType: 'Payment Type',
    paymentMethod: 'Payment Method',
    paymentDate: 'Payment Date',
    amount: 'Amount',
    reference: 'Reference',
    description: 'Description',
    createdBy: 'Created By',
    // Payment types
    vendorPayment: 'Vendor Payment',
    customerPayment: 'Customer Payment',
    // Payment methods
    cash: 'Cash',
    check: 'Check',
    bankTransfer: 'Bank Transfer',
    creditCard: 'Credit Card',
    online: 'Online Payment',
    // Summary cards
    totalPayments: 'Total Payments',
    vendorPayments: 'Vendor Payments',
    customerPayments: 'Customer Payments',
    netCashFlow: 'Net Cash Flow',
    noPaymentsFound: 'No payments found',
    allTypes: 'All Types',
    allMethods: 'All Methods',
    todaysPayments: "Today's Payments",
    thisWeek: 'This Week',
    thisMonth: 'This Month'
  }
}
const Payments: React.FC<PaymentsProps> = ({ language }) => {
  const t = translations[language]
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState<string>('all')
  const [methodFilter, setMethodFilter] = useState<string>('all')
  useEffect(() => {
    fetchPayments()
  }, [])
  const fetchPayments = async () => {
    try {
      setLoading(true)
      const token = localStorage.getItem('token')
      const response = await fetch('/api/payments/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      })
      
      if (response.ok) {
        const data = await response.json()
        setPayments(data.results || data)
      }
    } catch (error) {
      console.error('Error fetching payments:', error)
    } finally {
      setLoading(false)
    }
  }
  const filteredPayments = payments.filter(payment => {
    const matchesSearch = 
      payment.payment_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.reference_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.created_by_name.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = typeFilter === 'all' || payment.payment_type === typeFilter
    const matchesMethod = methodFilter === 'all' || payment.payment_method === methodFilter
    
    return matchesSearch && matchesType && matchesMethod
  })
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR',
      minimumFractionDigits: 2,
    }).format(amount)
  }
  const formatDate = (dateString: string): string => {
    return new DatedateString.toLocaleDateString(language === 'ar' ? 'ar-SA' : 'en-US')
  }
  const getPaymentTypeIcon = (type: string): void => {
    return type === 'VENDOR_PAYMENT' ? 
      <ArrowUpRight className="h-4 w-4 text-red-600" /> : 
      <ArrowDownLeft className="h-4 w-4 text-green-600" />
  }
  const getPaymentMethodIcon = (method: string): void => {
    switch (method) {
      case 'CASH':
        return <Banknote className="h-4 w-4 text-green-600" />
      case 'CHECK':
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'BANK_TRANSFER':
        return <Building className="h-4 w-4 text-purple-600" />
      case 'CREDIT_CARD':
        return <CreditCard className="h-4 w-4 text-orange-600" />
      case 'ONLINE':
        return <Smartphone className="h-4 w-4 text-indigo-600" />
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />
    }
  }
  const getPaymentTypeBadge = (type: string): void => {
    return type === 'VENDOR_PAYMENT' ? 
      <Badge variant="outline" className="text-red-600 border-red-600">{t.vendorPayment}</Badge> :
      <Badge variant="outline" className="text-green-600 border-green-600">{t.customerPayment}</Badge>
  }
  const getPaymentMethodText = (method: string): void => {
    switch (method) {
      case 'CASH': return t.cash
      case 'CHECK': return t.check
      case 'BANK_TRANSFER': return t.bankTransfer
      case 'CREDIT_CARD': return t.creditCard
      case 'ONLINE': return t.online
      default: return method
    }
  }

  // Calculate summary statistics
  const totalPayments = payments.length
  const vendorPayments = payments
    .filter(p => p.payment_type === 'VENDOR_PAYMENT')
    .reduce((sum, p) => sum + p.amount, 0)
  const customerPayments = payments
    .filter(p => p.payment_type === 'CUSTOMER_PAYMENT')
    .reduce((sum, p) => sum + p.amount, 0)
  const netCashFlow = customerPayments - vendorPayments

  // Today's payments
  const today = new Date().toISOString().split('T')[0]
  const todaysPayments = payments
    .filter(p => p.payment_date === today)
    .reduce((sum, p) => sum + p.amount, 0)

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t.payments}</h1>
          <p className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة المدفوعات والتدفق النقدي' : 'Payment Management & Cash Flow'}
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {t.addPayment}
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.totalPayments}</p>
                <p className="text-2xl font-bold mt-1">{totalPayments}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.vendorPayments}</p>
                <p className="text-2xl font-bold mt-1 text-red-600">{formatCurrency(vendorPayments)}</p>
              </div>
              <ArrowUpRight className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.customerPayments}</p>
                <p className="text-2xl font-bold mt-1 text-green-600">{formatCurrency(customerPayments)}</p>
              </div>
              <ArrowDownLeft className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{t.netCashFlow}</p>
                <p className={`text-2xl font-bold mt-1 ${netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(netCashFlow)}
                </p>
              </div>
              <div className={`h-8 w-8 ${netCashFlow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {netCashFlow >= 0 ? <ArrowDownLeft /> : <ArrowUpRight />}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={t.searchPayments}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={typeFilter}
                onChange={(e: any) => setTypeFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{t.allTypes}</option>
                <option value="VENDOR_PAYMENT">{t.vendorPayment}</option>
                <option value="CUSTOMER_PAYMENT">{t.customerPayment}</option>
              </select>
            </div>
            <div className="sm:w-48">
              <select
                value={methodFilter}
                onChange={(e: any) => setMethodFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">{t.allMethods}</option>
                <option value="CASH">{t.cash}</option>
                <option value="CHECK">{t.check}</option>
                <option value="BANK_TRANSFER">{t.bankTransfer}</option>
                <option value="CREDIT_CARD">{t.creditCard}</option>
                <option value="ONLINE">{t.online}</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t.payments}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.paymentNumber}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.paymentType}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.paymentMethod}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.paymentDate}</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">{t.amount}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.reference}</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">{t.createdBy}</th>
                </tr>
              </thead>
              <tbody>
                {filteredPayments.map((payment) => (<tr key={payment.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {payment.payment_number}
                      </code>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        {getPaymentTypeIcon(payment.payment_type)}
                        {getPaymentTypeBadge(payment.payment_type)}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        {getPaymentMethodIcon(payment.payment_method)}
                        {getPaymentMethodText(payment.payment_method)}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate(payment.payment_date)}
                      </div>
                    </td>
                    <td className="py-3 px-4 text-right font-mono">
                      <span className={payment.payment_type === 'VENDOR_PAYMENT' ? 'text-red-600' : 'text-green-600'}>
                        {payment.payment_type === 'VENDOR_PAYMENT' ? '-' : '+'}
                        {formatCurrency(payment.amount)}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div>
                        {payment.reference_number && (<p className="text-sm font-medium">{payment.reference_number}</p>
                        )}
                        {payment.vendor_invoice_number && (<p className="text-xs text-gray-500">Invoice: {payment.vendor_invoice_number}</p>
                        )}
                        {payment.customer_invoice_number && (<p className="text-xs text-gray-500">Invoice: {payment.customer_invoice_number}</p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        {payment.created_by_name}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {filteredPayments.length === 0 && (<div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">{t.noPaymentsFound}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
export default Payments