import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Package,
  Edit,
  Trash2,
  Calendar,
  DollarSign,
  MapPin,
  User,
  Settings,
  TrendingDown,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCrud } from '@/hooks/useCrud';
import { assetService } from '@/services/crudService';
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable';
import CrudModal, { FormField } from '@/components/common/CrudModal';

interface Asset {
  id: number;
  asset_id: string;
  name: string;
  name_ar?: string;
  category: number;
  category_name?: string;
  description?: string;
  description_ar?: string;
  serial_number?: string;
  model?: string;
  manufacturer?: string;
  barcode?: string;
  qr_code?: string;
  purchase_date: string;
  purchase_price: number;
  current_value: number;
  salvage_value?: number;
  depreciation_method?: 'STRAIGHT_LINE' | 'DECLINING_BALANCE' | 'UNITS_OF_PRODUCTION' | 'SUM_OF_YEARS';
  useful_life_years?: number;
  useful_life_units?: number;
  depreciation_rate?: number;
  current_book_value?: number;
  accumulated_depreciation?: number;
  annual_depreciation?: number;
  status: 'AVAILABLE' | 'ASSIGNED' | 'MAINTENANCE' | 'DISPOSED' | 'LOST';
  condition: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'DAMAGED';
  assigned_to?: number;
  assigned_to_name?: string;
  department?: number;
  department_name?: string;
  location?: string;
  building?: string;
  floor?: string;
  room?: string;
  warranty_expiry?: string;
  warranty_provider?: string;
  maintenance_schedule?: 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY' | 'AS_NEEDED';
  last_maintenance_date?: string;
  next_maintenance_date?: string;
  is_under_warranty?: boolean;
  is_maintenance_due?: boolean;
  currency_code?: string;
  currency_symbol?: string;
  notes?: string;
  tags?: string;
  is_active: boolean;
  created_by?: number;
  created_by_name?: string;
  created_at: string;
  updated_at?: string;
}

interface AssetCategory {
  id: number;
  name: string;
  name_ar: string;
}

interface Department {
  id: number;
  name: string;
}

interface Employee {
  id: number;
  user: {
    first_name: string;
    last_name: string;
  };
}

interface AssetManagementProps {
  language: 'ar' | 'en';
}

const AssetManagement: React.FC<AssetManagementProps> = ({ language }) => {
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create');

  // Use the generic CRUD hook
  const {
    items: assets,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Asset>({
    service: assetService,
    autoLoad: true,
    pageSize: 20
  });

  // Translations
  const translations = {
    ar: {
      assetManagement: 'إدارة الأصول',
      addAsset: 'إضافة أصل',
      editAsset: 'تعديل الأصل',
      view: 'عرض',
      edit: 'تعديل',
      delete: 'حذف',
      confirmDelete: 'هل أنت متأكد من حذف هذا الأصل؟',
      search: 'بحث',
      assetId: 'رقم الأصل',
      name: 'الاسم',
      category: 'الفئة',
      status: 'الحالة',
      condition: 'الوضع',
      assignedTo: 'مخصص لـ',
      location: 'الموقع',
      purchaseDate: 'تاريخ الشراء',
      purchasePrice: 'سعر الشراء',
      currentValue: 'القيمة الحالية',
      serialNumber: 'الرقم التسلسلي',
      model: 'الطراز',
      manufacturer: 'الشركة المصنعة',
      description: 'الوصف',
      warrantyExpiry: 'انتهاء الضمان',
      available: 'متاح',
      assigned: 'مخصص',
      maintenance: 'صيانة',
      disposed: 'مستبعد',
      lost: 'مفقود',
      excellent: 'ممتاز',
      good: 'جيد',
      fair: 'مقبول',
      poor: 'ضعيف',
      damaged: 'تالف'
    },
    en: {
      assetManagement: 'Asset Management',
      addAsset: 'Add Asset',
      editAsset: 'Edit Asset',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      confirmDelete: 'Are you sure you want to delete this asset?',
      search: 'Search',
      assetId: 'Asset ID',
      name: 'Name',
      category: 'Category',
      status: 'Status',
      condition: 'Condition',
      assignedTo: 'Assigned To',
      location: 'Location',
      purchaseDate: 'Purchase Date',
      purchasePrice: 'Purchase Price',
      currentValue: 'Current Value',
      serialNumber: 'Serial Number',
      model: 'Model',
      manufacturer: 'Manufacturer',
      description: 'Description',
      warrantyExpiry: 'Warranty Expiry',
      available: 'Available',
      assigned: 'Assigned',
      maintenance: 'Maintenance',
      disposed: 'Disposed',
      lost: 'Lost',
      excellent: 'Excellent',
      good: 'Good',
      fair: 'Fair',
      poor: 'Poor',
      damaged: 'Damaged'
    }
  };

  const t = translations[language];
  const isRTL = language === 'ar';

  // Helper functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'ASSIGNED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'MAINTENANCE':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'DISPOSED':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'LOST':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getConditionColor = (condition: string): string => {
    switch (condition) {
      case 'EXCELLENT':
        return 'text-green-400';
      case 'GOOD':
        return 'text-blue-400';
      case 'FAIR':
        return 'text-yellow-400';
      case 'POOR':
        return 'text-orange-400';
      case 'DAMAGED':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string): React.ReactElement => {
    switch (status) {
      case 'AVAILABLE':
        return <CheckCircle className="h-3 w-3" />;
      case 'ASSIGNED':
        return <User className="h-3 w-3" />;
      case 'MAINTENANCE':
        return <Settings className="h-3 w-3" />;
      case 'DISPOSED':
        return <TrendingDown className="h-3 w-3" />;
      case 'LOST':
        return <AlertTriangle className="h-3 w-3" />;
      default:
        return <Clock className="h-3 w-3" />;
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  // Table columns configuration
  const columns: TableColumn<Asset>[] = [
    {
      key: 'asset_id',
      label: t.assetId,
      sortable: true,
      render: (item: Asset => (
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-blue-400" />
          <span className="text-white font-medium">{item.asset_id}</span>
        </div>
      )
    },
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Asset => (
        <div>
          <span className="text-white font-medium">{item.name}</span>
          {item.category_name && (
            <div className="text-white/60 text-sm">{item.category_name}</div>
          )}
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Asset => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{t[item.status.toLowerCase() as keyof typeof t] || item.status}</span>
        </Badge>
      )
    },
    {
      key: 'condition',
      label: t.condition,
      render: (item: Asset => (
        <span className={`font-medium ${getConditionColor(item.condition)}`}>
          {t[item.condition.toLowerCase() as keyof typeof t] || item.condition}
        </span>
      )
    },
    {
      key: 'assigned_to_name',
      label: t.assignedTo,
      render: (item: Asset => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.assigned_to_name || 'Unassigned'}</span>
        </div>
      )
    },
    {
      key: 'location',
      label: t.location,
      render: (item: Asset => (
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.location || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'purchase_price',
      label: t.purchasePrice,
      sortable: true,
      render: (item: Asset => (
        <div className="text-right">
          <div className="text-green-400 font-medium">{formatCurrency(item.purchase_price)}</div>
          {item.current_value && (
            <div className="text-white/60 text-sm">{formatCurrency(item.current_value)} current</div>
          )}
        </div>
      )
    },
    {
      key: 'purchase_date',
      label: t.purchaseDate,
      sortable: true,
      render: (item: Asset => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.purchase_date}</span>
        </div>
      )
    }
  ];

  // Table actions configuration
  const actions: TableAction<Asset>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Asset => {
        selectItem(item);
        setModalMode('view');
        setShowModal(true);
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Asset => {
        selectItem(item);
        setModalMode('edit');
        setShowModal(true);
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Asset => {
        if (confirm(t.confirmDelete)) {
          deleteItem(item.id);
        }
      },
      variant: 'ghost'
    }
  ];

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'asset_id',
      label: t.assetId,
      type: 'text',
      required: true
    },
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: t.name + ' (عربي)',
      type: 'text'
    },
    {
      name: 'category',
      label: t.category,
      type: 'number',
      required: true,
      placeholder: 'Category ID'
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea'
    },
    {
      name: 'description_ar',
      label: t.description + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'serial_number',
      label: t.serialNumber,
      type: 'text'
    },
    {
      name: 'model',
      label: t.model,
      type: 'text'
    },
    {
      name: 'manufacturer',
      label: t.manufacturer,
      type: 'text'
    },
    {
      name: 'purchase_date',
      label: t.purchaseDate,
      type: 'date',
      required: true
    },
    {
      name: 'purchase_price',
      label: t.purchasePrice,
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'current_value',
      label: t.currentValue,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'assigned_to',
      label: t.assignedTo,
      type: 'number',
      placeholder: 'Employee ID'
    },
    {
      name: 'department',
      label: 'Department',
      type: 'number',
      placeholder: 'Department ID'
    },
    {
      name: 'location',
      label: t.location,
      type: 'text'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.available, value: 'AVAILABLE' },
        { label: t.assigned, value: 'ASSIGNED' },
        { label: t.maintenance, value: 'MAINTENANCE' },
        { label: t.disposed, value: 'DISPOSED' },
        { label: t.lost, value: 'LOST' }
      ]
    },
    {
      name: 'condition',
      label: t.condition,
      type: 'select',
      required: true,
      options: [
        { label: t.excellent, value: 'EXCELLENT' },
        { label: t.good, value: 'GOOD' },
        { label: t.fair, value: 'FAIR' },
        { label: t.poor, value: 'POOR' },
        { label: t.damaged, value: 'DAMAGED' }
      ]
    },
    {
      name: 'warranty_expiry',
      label: t.warrantyExpiry,
      type: 'date'
    },
    {
      name: 'is_active',
      label: 'Active',
      type: 'checkbox'
    }
  ];

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false);
    selectItem(null);
    clearError();
  };

  const handleSave = async (data: Partial<Asset>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data);
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data);
      }
      setShowModal(false);
    } catch (error) {
      console.error('Save error:', error);
    }
  };

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.assetManagement}
        data={assets}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create');
          setShowModal(true);
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addAsset}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addAsset : modalMode === 'edit' ? t.editAsset : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  );
};

export default AssetManagement;
    name: '',
    name_ar: '',
    category: '',
    serial_number: '',
    model: '',
    manufacturer: '',
    purchase_date: '',
    purchase_price: '',
    salvage_value: '',
    useful_life_years: 5,
    depreciation_method: 'STRAIGHT_LINE',
    assigned_to: '',
    department: '',
    location: '',
    warranty_expiry: '',
    condition: 'GOOD',
    status: 'AVAILABLE',
    tags: '',
    notes: ''
  });
  useEffect(() => {
    fetchAssets();
    fetchCategories();
    fetchDepartments();
    fetchEmployees();
  }, []);

  const fetchAssets: any = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (statusFilter && statusFilter !== '__all__') params.append('status', statusFilter);
      if (categoryFilter && categoryFilter !== '__all__') params.append('category', categoryFilter);
      const response: any = await fetch(`/api/assets/?${params.toString()}`);
      if (response.ok) {
        const data: any = await response.json();
        setAssets(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching assets:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories: any = async () => {
    try {
      const response = await fetch('/api/asset-categories/');
      if (response.ok) {
        const data: any = await response.json();
        setCategories(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchDepartments: any = async () => {
    try {
      const response = await fetch('/api/departments/');
      if (response.ok) {
        const data: any = await response.json();
        setDepartments(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  const fetchEmployees: any = async () => {
    try {
      const response = await fetch('/api/employees/');
      if (response.ok) {
        const data: any = await response.json();
        setEmployees(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
    }
  };

  const handleAddAsset: any = async () => {
    try {
      const response = await fetch('/api/assets/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...assetForm,
          category: parseInt(assetForm.category),
          assigned_to: assetForm.assigned_to ? parseInt(assetForm.assigned_to) : null,
          department: assetForm.department ? parseInt(assetForm.department) : null,
          useful_life_years: parseInt(assetForm.useful_life_years.toString()),
        }),
      });

      if (response.ok) {
        fetchAssets();
        setIsAddDialogOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error adding asset:', error);
    }
  };

  const resetForm: any = (): void => {
    setAssetForm({
      name: '',
      name_ar: '',
      category: '',
      serial_number: '',
      model: '',
      manufacturer: '',
      purchase_date: '',
      purchase_price: '',
      salvage_value: '',
      useful_life_years: 5,
      depreciation_method: 'STRAIGHT_LINE',
      assigned_to: '',
      department: '',
      location: '',
      warranty_expiry: '',
      condition: 'GOOD',
      status: 'AVAILABLE',
      tags: '',
      notes: ''
    });
  };

  const getStatusColor: any = (status: string): void => {
    const colors = {
      'AVAILABLE': 'bg-green-100 text-green-800',
      'IN_USE': 'bg-blue-100 text-blue-800',
      'MAINTENANCE': 'bg-yellow-100 text-yellow-800',
      'RETIRED': 'bg-gray-100 text-gray-800',
      'LOST': 'bg-red-100 text-red-800',
      'DAMAGED': 'bg-red-100 text-red-800',
      'DISPOSED': 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getConditionColor: any = (condition: string): void => {
    const colors = {
      'EXCELLENT': 'text-green-600',
      'GOOD': 'text-blue-600',
      'FAIR': 'text-yellow-600',
      'POOR': 'text-orange-600',
      'DAMAGED': 'text-red-600'
    };
    return colors[condition as keyof typeof colors] || 'text-gray-600';
  };

  const formatCurrency: any = (amount: number, symbol: string = '﷼'): string => {
    return `${amount.toLocaleString()} ${symbol}`;
  };

  const filteredAssets: any = assets.filter(asset => {
    const matchesSearch = !searchTerm ||
      asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.asset_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      asset.serial_number.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus: any = !statusFilter || statusFilter === '__all__' || asset.status === statusFilter;
    const matchesCategory: any = !categoryFilter || categoryFilter === '__all__' || asset.category.toString() === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة الأصول' : 'Asset Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة وتتبع أصول الشركة والاستهلاك والصيانة'
              : 'Manage and track company assets, depreciation, and maintenance'
            }
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            {language === 'ar' ? 'تصدير' : 'Export'}
          </Button>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {language === 'ar' ? 'إضافة أصل' : 'Add Asset'}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {language === 'ar' ? 'إضافة أصل جديد' : 'Add New Asset'}
                </DialogTitle>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'اسم الأصل' : 'Asset Name'}</Label>
                  <Input
                    value={assetForm.name}
                    onChange={(e: any) => setAssetForm({...assetForm, name: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل اسم الأصل' : 'Enter asset name'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الاسم بالعربية' : 'Arabic Name'}</Label>
                  <Input
                    value={assetForm.name_ar}
                    onChange={(e: any) => setAssetForm({...assetForm, name_ar: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الاسم بالعربية' : 'Enter Arabic name'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الفئة' : 'Category'}</Label>
                  <Select value={assetForm.category} onValueChange={(value) => setAssetForm({...assetForm, category: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر الفئة' : 'Select category'} />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {language === 'ar' ? category.name_ar : category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الرقم التسلسلي' : 'Serial Number'}</Label>
                  <Input
                    value={assetForm.serial_number}
                    onChange={(e: any) => setAssetForm({...assetForm, serial_number: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الرقم التسلسلي' : 'Enter serial number'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الطراز' : 'Model'}</Label>
                  <Input
                    value={assetForm.model}
                    onChange={(e: any) => setAssetForm({...assetForm, model: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الطراز' : 'Enter model'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'الشركة المصنعة' : 'Manufacturer'}</Label>
                  <Input
                    value={assetForm.manufacturer}
                    onChange={(e: any) => setAssetForm({...assetForm, manufacturer: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الشركة المصنعة' : 'Enter manufacturer'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'تاريخ الشراء' : 'Purchase Date'}</Label>
                  <Input
                    type="date"
                    value={assetForm.purchase_date}
                    onChange={(e: any) => setAssetForm({...assetForm, purchase_date: e.target.value})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'سعر الشراء' : 'Purchase Price'}</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={assetForm.purchase_price}
                    onChange={(e: any) => setAssetForm({...assetForm, purchase_price: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل سعر الشراء' : 'Enter purchase price'}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'العمر الافتراضي (سنوات)' : 'Useful Life (Years)'}</Label>
                  <Input
                    type="number"
                    min="1"
                    value={assetForm.useful_life_years}
                    onChange={(e: any) => setAssetForm({...assetForm, useful_life_years: parseInt(e.target.value) || 5})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'طريقة الاستهلاك' : 'Depreciation Method'}</Label>
                  <Select value={assetForm.depreciation_method} onValueChange={(value) => setAssetForm({...assetForm, depreciation_method: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="STRAIGHT_LINE">
                        {language === 'ar' ? 'القسط الثابت' : 'Straight Line'}
                      </SelectItem>
                      <SelectItem value="DECLINING_BALANCE">
                        {language === 'ar' ? 'الرصيد المتناقص' : 'Declining Balance'}
                      </SelectItem>
                      <SelectItem value="NO_DEPRECIATION">
                        {language === 'ar' ? 'بدون استهلاك' : 'No Depreciation'}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'المخصص له' : 'Assigned To'}</Label>
                  <Select value={assetForm.assigned_to} onValueChange={(value) => setAssetForm({...assetForm, assigned_to: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر الموظف' : 'Select employee'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">
                        {language === 'ar' ? 'غير مخصص' : 'Unassigned'}
                      </SelectItem>
                      {employees.map(employee => (
                        <SelectItem key={employee.id} value={employee.id.toString()}>
                          {employee.user.first_name} {employee.user.last_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label>{language === 'ar' ? 'القسم' : 'Department'}</Label>
                  <Select value={assetForm.department} onValueChange={(value) => setAssetForm({...assetForm, department: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder={language === 'ar' ? 'اختر القسم' : 'Select department'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">
                        {language === 'ar' ? 'بدون قسم' : 'No department'}
                      </SelectItem>
                      {departments.map(department => (
                        <SelectItem key={department.id} value={department.id.toString()}>
                          {department.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="md:col-span-2">
                  <Label>{language === 'ar' ? 'الموقع' : 'Location'}</Label>
                  <Input
                    value={assetForm.location}
                    onChange={(e: any) => setAssetForm({...assetForm, location: e.target.value})}
                    placeholder={language === 'ar' ? 'أدخل الموقع' : 'Enter location'}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleAddAsset}>
                  {language === 'ar' ? 'إضافة الأصل' : 'Add Asset'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الأصول...' : 'Search assets...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="AVAILABLE">
                  {language === 'ar' ? 'متاح' : 'Available'}
                </SelectItem>
                <SelectItem value="IN_USE">
                  {language === 'ar' ? 'قيد الاستخدام' : 'In Use'}
                </SelectItem>
                <SelectItem value="MAINTENANCE">
                  {language === 'ar' ? 'تحت الصيانة' : 'Maintenance'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الفئات' : 'All categories'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الفئات' : 'All categories'}
                </SelectItem>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {language === 'ar' ? category.name_ar : category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Assets Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {language === 'ar' ? 'قائمة الأصول' : 'Assets List'}
            <Badge variant="secondary" className="ml-2">
              {filteredAssets.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'رقم الأصل' : 'Asset ID'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الاسم' : 'Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الفئة' : 'Category'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة الفنية' : 'Condition'}</TableHead>
                  <TableHead>{language === 'ar' ? 'القيمة الحالية' : 'Book Value'}</TableHead>
                  <TableHead>{language === 'ar' ? 'المخصص له' : 'Assigned To'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssets.map((asset) => (<TableRow key={asset.id}>
                    <TableCell className="font-medium">{asset.asset_id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{asset.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {asset.model} • {asset.manufacturer}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{asset.category_name}</TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(asset.status)}>
                        {language === 'ar' 
                          ? asset.status === 'AVAILABLE' ? 'متاح' 
                            : asset.status === 'IN_USE' ? 'قيد الاستخدام'
                            : asset.status === 'MAINTENANCE' ? 'تحت الصيانة'
                            : asset.status
                          : asset.status.replace('_', ' ')
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className={getConditionColor(asset.condition)}>
                        {language === 'ar' 
                          ? asset.condition === 'EXCELLENT' ? 'ممتاز' 
                            : asset.condition === 'GOOD' ? 'جيد'
                            : asset.condition === 'FAIR' ? 'مقبول'
                            : asset.condition
                          : asset.condition
                        }
                      </span>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {formatCurrency(asset.current_book_value, asset.currency_symbol)}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <TrendingDown className="h-3 w-3 mr-1" />
                          {formatCurrency(asset.accumulated_depreciation, asset.currency_symbol)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {asset.assigned_to_name || (language === 'ar' ? 'غير مخصص' : 'Unassigned')}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {asset.location}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Calendar className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AssetManagement;
