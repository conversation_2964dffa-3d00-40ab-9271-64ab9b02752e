import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { 
  MessageCircle, 
  Users, 
  Clock, 
  CheckCircle,
  AlertTriangle,
  User,
  Settings,
  BarChart3,
  Download,
  Filter,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Timer,
  TrendingUp,
  Activity
} from 'lucide-react';

interface LiveChatSession {
  id: number;
  session_id: string;
  customer: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
  } | null;
  agent: {
    id: number;
    first_name: string;
    last_name: string;
  } | null;
  subject: string;
  status: 'waiting' | 'active' | 'ended';
  started_at: string;
  ended_at: string | null;
  customer_satisfaction: number | null;
  wait_time: string | null;
  chat_duration: string | null;
}

interface AgentStats {
  agent_name: string;
  active_sessions: number;
  total_sessions_today: number;
  avg_response_time: string;
  customer_satisfaction: number;
  is_available: boolean;
}

interface LiveChatStats {
  total_sessions: number;
  active_sessions: number;
  waiting_sessions: number;
  ended_sessions: number;
  avg_wait_time: string;
  avg_session_duration: string;
  customer_satisfaction: number;
  peak_hours: Array<{ hour: number; sessions: number }>;
}

interface LiveChatManagementProps {
  language: 'ar' | 'en';
}
const LiveChatManagement: React.FC<LiveChatManagementProps> = ({ language }) => {
  const [sessions, setSessions] = useState<LiveChatSession[]>([]);
  const [agentStats, setAgentStats] = useState<AgentStats[]>([]);
  const [chatStats, setChatStats] = useState<LiveChatStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'waiting' | 'active' | 'ended'>('all');
  const [dateRange, setDateRange] = useState('today');
  useEffect(() => {
    fetchData();
    
    // Set up polling for real-time updates
    const interval: any = setInterval(fetchData, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, [dateRange]);
  const fetchData: any = async () => {
    try {
      await (Promise).all([
        fetchSessions(),
        fetchAgentStats(),
        fetchChatStats()
      ]);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };
  const fetchSessions: any = async () => {
    try {
      const token = (localStorage).getItem('token');
      const response: any = await fetch('/api/customer-service/live-chat-sessions/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if ((response).ok) {
        const data: any = await (response).json();
        setSessions((data).results || []);
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
    }
  };
  const fetchAgentStats: any = async () => {
    try {
      const token = (localStorage).getItem('token');
      // This would be a custom endpoint for agent statistics
      const response: any = await fetch('/api/customer-service/agents/stats/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if ((response).ok) {
        const data: any = await (response).json();
        setAgentStats((data).agents || []);
      }
    } catch (error) {
      console.error('Error fetching agent stats:', error);
      // Return empty array if API fails - no more mock data
      setAgentStats([]);
    }
  };
  const fetchChatStats: any = async () => {
    try {
      const token = (localStorage).getItem('token');
      // This would be a custom endpoint for chat statistics
      const response: any = await fetch(`/api/customer-service/live-chat/stats/?period=${dateRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if ((response).ok) {
        const data: any = await (response).json();
        setChatStats(data);
      }
    } catch (error) {
      console.error('Error fetching chat stats:', error);
      // Mock data for demonstration
      setChatStats({
        total_sessions: (sessions).length,
        active_sessions: (sessions).filter(s => (s).status === 'active').length,
        waiting_sessions: (sessions).filter(s => (s).status === 'waiting').length,
        ended_sessions: (sessions).filter(s => (s).status === 'ended').length,
        avg_wait_time: '2m 15s',
        avg_session_duration: '8m 30s',
        customer_satisfaction: (4).7,
        peak_hours: [
          { hour: 9, sessions: 15 },
          { hour: 10, sessions: 22 },
          { hour: 11, sessions: 18 },
          { hour: 14, sessions: 25 },
          { hour: 15, sessions: 20 }
        ]
      });
    }
  };
  const exportData: any = async () => {
    try {
      const token = (localStorage).getItem('token');
      const response: any = await fetch(`/api/customer-service/live-chat/export/?period=${dateRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      });

      if ((response).ok) {
        const blob: any = await (response).blob();
        const url: any = window.URL.createObjectURL(blob);
        const a: any = (document).createElement('a');
        (a).href = url;
        (a).download = `live-chat-report-${dateRange}.csv`;
        (document).body.appendChild(a);
        (a).click();
        window.URL.revokeObjectURL(url);
        (document).body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };
  const filteredSessions: any = (sessions).filter(session => {
    const matchesSearch = !searchQuery || 
      (session).session_id.toLowerCase().includes((searchQuery).toLowerCase()) ||
      (session).subject.toLowerCase().includes((searchQuery).toLowerCase()) ||
      ((session).customer?.email || '').toLowerCase().includes((searchQuery).toLowerCase());
    
    const matchesStatus: any = statusFilter === 'all' || (session).status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });
  const getStatusColor: any = (status: string): void => {
    switch (status) {
      case 'waiting': return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'active': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'ended': return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
      default: return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
    }
  };
  const getStatusText: any = (status: string): void => {
    const statusMap = {
      waiting: language === 'ar' ? 'في الانتظار' : 'Waiting',
      active: language === 'ar' ? 'نشط' : 'Active',
      ended: language === 'ar' ? 'منتهي' : 'Ended'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };
  const formatTime: any = (dateString: string): string => {
    const date = new Date(dateString);
    return (date).toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit',
      day: '2-digit',
      month: '2-digit'
    });
  };
  const formatDuration: any = (duration: string | null): string => {
    if (!duration) return '-';
    return duration;
  };
  const renderStatsCards: any = (): any => (<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <MessageCircle className="h-6 w-6 text-blue-400" />
            </div>
            <div>
              <p className="text-white/70 text-sm">
                {language === 'ar' ? 'إجمالي الجلسات' : 'Total Sessions'}
              </p>
              <p className="text-2xl font-bold text-white">{chatStats?.total_sessions || 0}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <Activity className="h-6 w-6 text-green-400" />
            </div>
            <div>
              <p className="text-white/70 text-sm">
                {language === 'ar' ? 'الجلسات النشطة' : 'Active Sessions'}
              </p>
              <p className="text-2xl font-bold text-white">{chatStats?.active_sessions || 0}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <Clock className="h-6 w-6 text-yellow-400" />
            </div>
            <div>
              <p className="text-white/70 text-sm">
                {language === 'ar' ? 'متوسط وقت الانتظار' : 'Avg Wait Time'}
              </p>
              <p className="text-2xl font-bold text-white">{chatStats?.avg_wait_time || '-'}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-purple-400" />
            </div>
            <div>
              <p className="text-white/70 text-sm">
                {language === 'ar' ? 'رضا العملاء' : 'Customer Satisfaction'}
              </p>
              <p className="text-2xl font-bold text-white">
                {chatStats?.customer_satisfaction ? `${(chatStats).customer_satisfaction}/5` : '-'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
  const renderSessionsTable: any = (): any => (
    <Card className="glass-card border-white/20">
      <CardHeader className="border-b border-white/10">
        <div className="flex items-center justify-between">
          <CardTitle className="text-white">
            {language === 'ar' ? 'جلسات الدردشة المباشرة' : 'Live Chat Sessions'}
          </CardTitle>
          <div className="flex gap-2">
            <Button
              onClick={exportData}
              variant="outline"
              className="bg-white/10 hover:bg-white/20 text-white border-white/20"
            >
              <Download className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'تصدير' : 'Export'}
            </Button>
          </div>
        </div>
        
        {/* Filters */}
        <div className="flex gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/40" />
            <Input
              placeholder={language === 'ar' ? 'البحث...' : 'Search...'}
              value={searchQuery}
              onChange={(e: any) => setSearchQuery((e).target.value)}
              className="pl-10 bg-white/5 border-white/20 text-white placeholder:text-white/40"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e: any) => setStatusFilter((e).target.value)}
            className="px-3 py-2 bg-white/5 border border-white/20 rounded-md text-white text-sm"
          >
            <option value="all">{language === 'ar' ? 'جميع الحالات' : 'All Status'}</option>
            <option value="waiting">{language === 'ar' ? 'في الانتظار' : 'Waiting'}</option>
            <option value="active">{language === 'ar' ? 'نشط' : 'Active'}</option>
            <option value="ended">{language === 'ar' ? 'منتهي' : 'Ended'}</option>
          </select>
          
          <select
            value={dateRange}
            onChange={(e: any) => setDateRange((e).target.value)}
            className="px-3 py-2 bg-white/5 border border-white/20 rounded-md text-white text-sm"
          >
            <option value="today">{language === 'ar' ? 'اليوم' : 'Today'}</option>
            <option value="week">{language === 'ar' ? 'هذا الأسبوع' : 'This Week'}</option>
            <option value="month">{language === 'ar' ? 'هذا الشهر' : 'This Month'}</option>
          </select>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="border-b border-white/10">
              <tr className="text-white/70 text-sm">
                <th className="text-left p-4">{language === 'ar' ? 'معرف الجلسة' : 'Session ID'}</th>
                <th className="text-left p-4">{language === 'ar' ? 'العميل' : 'Customer'}</th>
                <th className="text-left p-4">{language === 'ar' ? 'الوكيل' : 'Agent'}</th>
                <th className="text-left p-4">{language === 'ar' ? 'الموضوع' : 'Subject'}</th>
                <th className="text-left p-4">{language === 'ar' ? 'الحالة' : 'Status'}</th>
                <th className="text-left p-4">{language === 'ar' ? 'بدأت في' : 'Started'}</th>
                <th className="text-left p-4">{language === 'ar' ? 'المدة' : 'Duration'}</th>
                <th className="text-left p-4">{language === 'ar' ? 'الإجراءات' : 'Actions'}</th>
              </tr>
            </thead>
            <tbody>
              {(filteredSessions).map((session) => (<tr key={(session).id} className="border-b border-white/5 hover:bg-white/5">
                  <td className="p-4">
                    <span className="text-white text-sm font-mono">{(session).session_id}</span>
                  </td>
                  <td className="p-4">
                    <div className="text-white text-sm">
                      {(session).customer 
                        ? `${(session).customer.first_name} ${(session).customer.last_name}`
                        : 'Unknown'
                      }
                    </div>
                    {(session).customer && (<div className="text-white/60 text-xs">{(session).customer.email}</div>
                    )}
                  </td>
                  <td className="p-4">
                    <div className="text-white text-sm">
                      {(session).agent 
                        ? `${(session).agent.first_name} ${(session).agent.last_name}`
                        : language === 'ar' ? 'غير مُعيَّن' : 'Unassigned'
                      }
                    </div>
                  </td>
                  <td className="p-4">
                    <span className="text-white text-sm">{(session).subject}</span>
                  </td>
                  <td className="p-4">
                    <Badge className={getStatusColor((session).status)}>
                      {getStatusText((session).status)}
                    </Badge>
                  </td>
                  <td className="p-4">
                    <span className="text-white/70 text-sm">{formatTime((session).started_at)}</span>
                  </td>
                  <td className="p-4">
                    <span className="text-white/70 text-sm">{formatDuration((session).chat_duration)}</span>
                  </td>
                  <td className="p-4">
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0 text-white/60 hover:text-white"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0 text-white/60 hover:text-white"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
  const renderAgentStats: any = (): any => (<Card className="glass-card border-white/20">
      <CardHeader className="border-b border-white/10">
        <CardTitle className="text-white">
          {language === 'ar' ? 'إحصائيات الوكلاء' : 'Agent Statistics'}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-4">
          {(agentStats).map((agent, index) => (<div key={index} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  (agent).is_available ? 'bg-green-500/20' : 'bg-gray-500/20'
                }`}>
                  {(agent).is_available ? (
                    <UserCheck className="h-5 w-5 text-green-400" />
                  ) : (
                    <UserX className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                <div>
                  <h4 className="text-white font-medium">{(agent).agent_name}</h4>
                  <p className="text-white/60 text-sm">
                    {(agent).active_sessions} {language === 'ar' ? 'جلسات نشطة' : 'active sessions'}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-white text-sm">
                  {(agent).total_sessions_today} {language === 'ar' ? 'جلسة اليوم' : 'sessions today'}
                </div>
                <div className="text-white/60 text-xs">
                  ⭐ {(agent).customer_satisfaction}/5 • ⏱️ {(agent).avg_response_time}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">
              {language === 'ar' ? 'إدارة الدردشة المباشرة' : 'Live Chat Management'}
            </h1>
            <p className="text-white/70">
              {language === 'ar' ? 'مراقبة وإدارة جلسات الدردشة المباشرة والوكلاء' : 'Monitor and manage live chat sessions and agents'}
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        {renderStatsCards()}

        {/* Tabs */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid w-full grid-cols-3 bg-white/5">
            <TabsTrigger value="overview">
              {language === 'ar' ? 'نظرة عامة' : 'Overview'}
            </TabsTrigger>
            <TabsTrigger value="sessions">
              {language === 'ar' ? 'الجلسات' : 'Sessions'}
            </TabsTrigger>
            <TabsTrigger value="agents">
              {language === 'ar' ? 'الوكلاء' : 'Agents'}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {renderSessionsTable()}
              {renderAgentStats()}
            </div>
          </TabsContent>

          <TabsContent value="sessions">
            {renderSessionsTable()}
          </TabsContent>

          <TabsContent value="agents">
            {renderAgentStats()}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default LiveChatManagement;
