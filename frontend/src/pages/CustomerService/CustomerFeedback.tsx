/**
 * Customer Feedback Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Star,
  MessageSquare,
  ThumbsUp,
  User,
  Calendar,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { customerFeedbackService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface CustomerFeedbackProps {
  language: 'ar' | 'en'
}

interface FeedbackData {
  id: number
  customer: number
  customer_name?: string
  feedback_type: 'ticket_rating' | 'service_survey' | 'product_feedback' | 'general_feedback'
  overall_rating: number
  response_time_rating?: number
  solution_quality_rating?: number
  agent_professionalism_rating?: number
  comments: string
  suggestions: string
  related_ticket?: number
  related_ticket_title?: string
  is_anonymous: boolean
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    customerFeedback: 'تقييمات العملاء',
    addFeedback: 'إضافة تقييم',
    editFeedback: 'تعديل التقييم',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقييم؟',
    searchPlaceholder: 'البحث في تقييمات العملاء...',
    customerName: 'اسم العميل',
    feedbackType: 'نوع التقييم',
    overallRating: 'التقييم العام',
    responseTimeRating: 'تقييم وقت الاستجابة',
    solutionQualityRating: 'تقييم جودة الحل',
    agentProfessionalismRating: 'تقييم احترافية الوكيل',
    comments: 'التعليقات',
    suggestions: 'الاقتراحات',
    createdAt: 'تاريخ الإنشاء',
    relatedTicket: 'التذكرة المرتبطة',
    feedbackTypes: {
      ticketRating: 'تقييم التذكرة',
      serviceSurvey: 'استطلاع الخدمة',
      productFeedback: 'تقييم المنتج',
      generalFeedback: 'تقييم عام'
    }
  },
  en: {
    customerFeedback: 'Customer Feedback',
    addFeedback: 'Add Feedback',
    editFeedback: 'Edit Feedback',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this feedback?',
    searchPlaceholder: 'Search customer feedback...',
    customerName: 'Customer Name',
    feedbackType: 'Feedback Type',
    overallRating: 'Overall Rating',
    responseTimeRating: 'Response Time Rating',
    solutionQualityRating: 'Solution Quality Rating',
    agentProfessionalismRating: 'Agent Professionalism Rating',
    comments: 'Comments',
    suggestions: 'Suggestions',
    createdAt: 'Created At',
    relatedTicket: 'Related Ticket',
    feedbackTypes: {
      ticketRating: 'Ticket Rating',
      serviceSurvey: 'Service Survey',
      productFeedback: 'Product Feedback',
      generalFeedback: 'General Feedback'
    }
  }
}
export default function CustomerFeedback({ language }: CustomerFeedbackProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: feedback,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<FeedbackData>({
    service: customerFeedbackService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const renderStars = (rating: number): void => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ))
  }

  const getFeedbackTypeColor = (type: string): string => {
    switch (type) {
      case 'ticket_rating': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'service_survey': return 'bg-green-100 text-green-800 border-green-200'
      case 'product_feedback': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'general_feedback': return 'bg-orange-100 text-orange-800 border-orange-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<FeedbackData>[] = [
    {
      key: 'customer_name',
      label: t.customerName,
      sortable: true,
      render: (item: FeedbackData) => (<div className="flex items-center gap-2">
          <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
            {((item.customer_name || '').charAt(0) + ((item.customer_name || '').split(' ')[1]?.charAt(0) || '')).toUpperCase() || '??'}
          </div>
          <span className="text-white/80">
            {item.customer_name || 'N/A'}
          </span>
        </div>
      )
    },
    {
      key: 'feedback_type',
      label: t.feedbackType,
      render: (item: FeedbackData) => (<Badge className={getFeedbackTypeColor(item.feedback_type)}>
          {item.feedback_type}
        </Badge>
      )
    },
    {
      key: 'overall_rating',
      label: t.overallRating,
      sortable: true,
      render: (item: FeedbackData) => (<div className="flex items-center gap-2">
          <div className="flex">{renderStars(item.overall_rating)}</div>
          <span className="text-white font-medium">{item.overall_rating}/5</span>
        </div>
      )
    },
    {
      key: 'response_time_rating',
      label: t.responseTimeRating,
      render: (item: FeedbackData) => (item.response_time_rating ? (<div className="flex items-center gap-1">
            <div className="flex">{renderStars(item.response_time_rating)}</div>
            <span className="text-white/80 text-sm">{item.response_time_rating}/5</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'solution_quality_rating',
      label: t.solutionQualityRating,
      render: (item: FeedbackData) => (item.solution_quality_rating ? (<div className="flex items-center gap-1">
            <div className="flex">{renderStars(item.solution_quality_rating)}</div>
            <span className="text-white/80 text-sm">{item.solution_quality_rating}/5</span>
          </div>
        ) : (
          <span className="text-white/50">-</span>
        )
      )
    },
    {
      key: 'comments',
      label: t.comments,
      render: (item: FeedbackData) => (<div className="max-w-xs">
          <span className="text-white/80 text-sm">
            {item.comments?.substring(0, 50) + '...' || 'No comments'}
          </span>
        </div>
      )
    },
    {
      key: 'created_at',
      label: t.createdAt,
      sortable: true,
      render: (item: FeedbackData) => (<div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.created_at}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<FeedbackData>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: FeedbackData) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: FeedbackData) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: FeedbackData) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'feedbackType',
      label: t.feedbackType,
      options: [
        { label: t.feedbackTypes.ticketRating, value: 'ticketRating' },
        { label: t.feedbackTypes.serviceSurvey, value: 'serviceSurvey' },
        { label: t.feedbackTypes.productFeedback, value: 'productFeedback' },
        { label: t.feedbackTypes.generalFeedback, value: 'generalFeedback' }
      ]
    },
    {
      key: 'overallRating',
      label: t.overallRating,
      options: [
        { label: '5 Stars', value: '5' },
        { label: '4 Stars', value: '4' },
        { label: '3 Stars', value: '3' },
        { label: '2 Stars', value: '2' },
        { label: '1 Star', value: '1' }
      ]
    }
  ]

  // Form fields configuration - Updated to match backend CustomerFeedback model
  const formFields: FormField[] = [
    {
      name: 'customer',
      label: t.customerName,
      type: 'number',
      required: true,
      placeholder: 'Customer ID'
    },
    {
      name: 'feedback_type',
      label: t.feedbackType,
      type: 'select',
      required: true,
      options: [
        { label: 'Ticket Rating', value: 'ticket_rating' },
        { label: 'Service Survey', value: 'service_survey' },
        { label: 'Product Feedback', value: 'product_feedback' },
        { label: 'General Feedback', value: 'general_feedback' }
      ]
    },
    {
      name: 'overall_rating',
      label: t.overallRating,
      type: 'number',
      required: true,
      min: 1,
      max: 5
    },
    {
      name: 'response_time_rating',
      label: t.responseTimeRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'solution_quality_rating',
      label: t.solutionQualityRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'agent_professionalism_rating',
      label: t.agentProfessionalismRating,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'comments',
      label: t.comments,
      type: 'textarea'
    },
    {
      name: 'suggestions',
      label: t.suggestions,
      type: 'textarea'
    },
    {
      name: 'related_ticket',
      label: t.relatedTicket + ' ID',
      type: 'number',
      placeholder: 'Ticket ID'
    },
    {
      name: 'is_anonymous',
      label: 'Anonymous Feedback',
      type: 'checkbox'
    },
    {
      name: 'relatedTicketTitle',
      label: t.relatedTicket + ' Title',
      type: 'text'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<FeedbackData>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (<div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.customerFeedback}
        data={feedback}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addFeedback : modalMode === 'edit' ? t.editFeedback : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
