import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Bot, 
  Send, 
  ThumbsUp, 
  ThumbsDown, 
  User, 
  Lightbulb,
  ExternalLink,
  Loader2
} from 'lucide-react';
import { isObject, isArray, isString, isNumber } from '../../utils/typeGuards';

// AI Assistant interface
interface AIAssistant {
  id: number;
  assistant_type: string;
  name: string;
  description?: string;
  is_active: boolean;
}

// API response interface
interface AssistantApiResponse {
  results?: AIAssistant[];
  count?: number;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  confidence?: number;
  suggestions?: Array<{
    id: number;
    title: string;
    summary: string;
    relevance_score: number;
  }>;
  helpful?: boolean;
}

interface AIChatAssistantProps {
  language: 'ar' | 'en';
}

const AIChatAssistant: React.FC<AIChatAssistantProps> = ({ language }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your AI support assistant. How can I help you today?',
      timestamp: new Date(),
      confidence: 0.95
    }
  ]);
  const [inputMessage, setInputMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [assistantId, setAssistantId] = useState<number | null>(null);
  const messagesEndRef: any = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchAIAssistant();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchAIAssistant: any = async () => {
    try {
      // SECURITY FIX: Use httpOnly cookies instead of localStorage
      const response = await fetch('/api/customer-service/ai-assistants/', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      const data: any = await response.json() as AssistantApiResponse;
      if (isObject(data) && isArray(data.results)) {
        const chatbot: any = data.results.find((assistant: AIAssistant) => assistant.assistant_type === 'chatbot');
        if (chatbot && isNumber((chatbot as AIAssistant).id)) {
          setAssistantId((chatbot as AIAssistant).id);
        }
      }
    } catch (error) {
      console.error('Error fetching AI assistant:', error);
    }
  };

  const scrollToBottom: any = (): void => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const sendMessage: any = async () => {
    if (!inputMessage.trim() || !assistantId) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const token: any = localStorage.getItem('token');
      const response: any = await fetch(`/api/customer-service/ai-assistants/${assistantId}/chat/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: inputMessage,
          context: {
            language: language,
            session_id: 'web-chat-' + Date.now()
          }
        })
      });

      const aiResponse: any = await response.json();

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.response || 'I apologize, but I encountered an error. Please try again.',
        timestamp: new Date(),
        confidence: aiResponse.confidence,
        suggestions: aiResponse.suggestions
      };

      setMessages(prev => [...prev, aiMessage]);

      // If AI suggests escalation, show system message
      if (aiResponse.escalate_to_human) {
        const systemMessage: ChatMessage = {
          id: (Date.now() + 2).toString(),
          type: 'system',
          content: 'This conversation has been escalated to a human agent. You will be connected shortly.',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, systemMessage]);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'system',
        content: 'Sorry, I encountered an error. Please try again or contact support.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const provideFeedback: any = async (messageId: string, helpful: boolean) => {
    // Update local state
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, helpful } : msg
    ));

    // Send feedback to backend (implementation would depend on your API)
    try {
      const token: any = localStorage.getItem('token');
      // This would be the actual feedback endpoint
      await fetch('/api/customer-service/ai-interactions/feedback/', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message_id: messageId,
          was_helpful: helpful
        })
      });
    } catch (error) {
      console.error('Error providing feedback:', error);
    }
  };

  const handleKeyPress: any = (e: React.KeyboardEvent): void => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime: any = (date: Date): string => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <Bot className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-white">AI Support Assistant</h1>
            <p className="text-white/70">Get instant help with your questions</p>
          </div>
        </div>

        {/* Chat Container */}
        <Card className="glass-card border-white/20 h-[600px] flex flex-col">
          <CardHeader className="border-b border-white/10">
            <CardTitle className="text-white flex items-center gap-2">
              <Bot className="h-5 w-5" />
              Chat Assistant
              <Badge className="bg-green-500/20 text-green-300 border-green-500/30 ml-auto">
                Online
              </Badge>
            </CardTitle>
          </CardHeader>

          {/* Messages */}
          <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.type !== 'user' && (
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.type === 'ai' ? 'bg-blue-500' : 'bg-gray-500'
                   }`}>
                    {message.type === 'ai' ? (
                      <Bot className="h-4 w-4 text-white" />
                    ) : (
                      <div className="w-2 h-2 bg-white rounded-full" />
                    )}
                  </div>
                )}

                <div className={`max-w-[70%] ${message.type === 'user' ? 'order-first' : ''}`}>
                  <div
                    className={`p-3 rounded-lg ${
                      message.type === 'user'
                        ? 'bg-blue-500 text-white ml-auto'
                        : message.type === 'ai'
                        ? 'bg-white/10 text-white'
                        : 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30'
                     }`}
                  >
                    <p className="text-sm">{message.content}</p>
                    
                    {/* AI Confidence Score */}
                    {message.type === 'ai' && message.confidence && (
                      <div className="mt-2 text-xs text-white/60">
                        Confidence: {(message.confidence * 100).toFixed(0)}%
                      </div>
                    )}

                    {/* Knowledge Base Suggestions */}
                    {message.suggestions && message.suggestions.length > 0 && (
                      <div className="mt-3 space-y-2">
                        <div className="text-xs text-white/80 flex items-center gap-1">
                          <Lightbulb className="h-3 w-3" />
                          Related articles:
                        </div>
                        {message.suggestions.slice(0, 2).map((suggestion) => (
                          <div
                            key={suggestion.id}
                            className="p-2 bg-white/5 rounded border border-white/10 cursor-pointer hover:bg-white/10 transition-colors"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="text-xs font-medium text-white">{suggestion.title}</h4>
                                <p className="text-xs text-white/60 mt-1 line-clamp-2">{suggestion.summary}</p>
                              </div>
                              <ExternalLink className="h-3 w-3 text-white/40 ml-2 flex-shrink-0" />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Feedback Buttons for AI Messages */}
                  {message.type === 'ai' && (
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-xs text-white/60">{formatTime(message.timestamp)}</span>
                      <div className="flex gap-1 ml-auto">
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`h-6 w-6 p-0 ${
                            message.helpful === true ? 'text-green-400' : 'text-white/40 hover:text-green-400'
                           }`}
                          onClick={() => provideFeedback(message.id, true)}
                        >
                          <ThumbsUp className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`h-6 w-6 p-0 ${
                            message.helpful === false ? 'text-red-400' : 'text-white/40 hover:text-red-400'
                           }`}
                          onClick={() => provideFeedback(message.id, false)}
                        >
                          <ThumbsDown className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Timestamp for user messages */}
                  {message.type === 'user' && (
                    <div className="text-xs text-white/60 mt-1 text-right">
                      {formatTime(message.timestamp)}
                    </div>
                  )}
                </div>
                {message.type === 'user' && (
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <User className="h-4 w-4 text-white" />
                  </div>
                )}
              </div>
            ))}

            {/* Loading indicator */}
            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <Bot className="h-4 w-4 text-white" />
                </div>
                <div className="bg-white/10 text-white p-3 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm">AI is thinking...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </CardContent>

          {/* Input */}
          <div className="border-t border-white/10 p-4">
            <div className="flex gap-2">
              <Input
                value={inputMessage}
                onChange={(e: any) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="flex-1 bg-white/5 border-white/20 text-white placeholder:text-white/40"
                disabled={isLoading}
              />
              <Button
                onClick={sendMessage}
                disabled={!inputMessage.trim() || isLoading}
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            <div className="text-xs text-white/60 mt-2">
              Press Enter to send • AI responses are generated automatically
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AIChatAssistant;
