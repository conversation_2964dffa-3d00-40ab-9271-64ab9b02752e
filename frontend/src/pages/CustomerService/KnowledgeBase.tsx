import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  BookOpen, 
  Plus, 
  Eye, 
  ThumbsUp, 
  ThumbsDown,
  Star,
  Clock,
  User,
  HelpCircle,
  Settings,
  FileText,
  Lightbulb
} from 'lucide-react';

interface KnowledgeBaseCategory {
  id: number;
  name: string;
  name_ar: string;
  description: string;
  icon: string;
  color: string;
  is_active: boolean;
}

interface KnowledgeBaseArticle {
  id: number;
  title: string;
  title_ar: string;
  content: string;
  summary: string;
  category: KnowledgeBaseCategory;
  article_type: string;
  author: {
    first_name: string;
    last_name: string;
  };
  is_published: boolean;
  is_featured: boolean;
  view_count: number;
  helpful_votes: number;
  not_helpful_votes: number;
  created_at: string;
  updated_at: string;
}
const KnowledgeBase: React.FC = () => {
  const [categories, setCategories] = useState<KnowledgeBaseCategory[]>([]);
  const [articles, setArticles] = useState<KnowledgeBaseArticle[]>([]);
  const [featuredArticles, setFeaturedArticles] = useState<KnowledgeBaseArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  useEffect(() => {
    fetchKnowledgeBaseData();
  }, []);

  const fetchKnowledgeBaseData: any = async () => {
    try {
      const token = (localStorage).getItem('token');
      const headers: any = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      // Fetch categories
      const categoriesResponse: any = await fetch('/api/customer-service/kb-categories/', {
        headers
      });
      const categoriesData: any = await (categoriesResponse).json();
      setCategories((categoriesData).results || []);

      // Fetch articles
      const articlesResponse: any = await fetch('/api/customer-service/kb-articles/', {
        headers
      });
      const articlesData: any = await (articlesResponse).json();
      const allArticles: any = (articlesData).results || [];
      setArticles(allArticles);
      setFeaturedArticles((allArticles).filter((article: KnowledgeBaseArticle) => (article).is_featured));

      setLoading(false);
    } catch (error) {
      console.error('Error fetching knowledge base data:', error);
      setLoading(false);
    }
  };

  const getIconComponent: any = (iconName: string): void => {
    switch (iconName) {
      case 'HelpCircle': return <HelpCircle className="h-6 w-6" />;
      case 'Settings': return <Settings className="h-6 w-6" />;
      case 'FileText': return <FileText className="h-6 w-6" />;
      case 'Lightbulb': return <Lightbulb className="h-6 w-6" />;
      default: return <BookOpen className="h-6 w-6" />;
    }
  };

  const getArticleTypeIcon: any = (type: string): void => {
    switch (type) {
      case 'faq': return <HelpCircle className="h-4 w-4" />;
      case 'tutorial': return <BookOpen className="h-4 w-4" />;
      case 'troubleshooting': return <Settings className="h-4 w-4" />;
      case 'policy': return <FileText className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  const getArticleTypeColor: any = (type: string): void => {
    switch (type) {
      case 'faq': return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      case 'tutorial': return 'bg-green-500/20 text-green-300 border-green-500/30';
      case 'troubleshooting': return 'bg-orange-500/20 text-orange-300 border-orange-500/30';
      case 'policy': return 'bg-purple-500/20 text-purple-300 border-purple-500/30';
      default: return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };

  const formatDate: any = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const filteredArticles: any = (articles).filter(article => {
    const matchesSearch = (article).title.toLowerCase().includes((searchTerm).toLowerCase()) ||
                         (article).content.toLowerCase().includes((searchTerm).toLowerCase());
    const matchesCategory: any = selectedCategory === null || (article).category.id === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  if (loading) {
    return (<div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-white/10 rounded w-64"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-32 bg-white/10 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">Knowledge Base</h1>
            <p className="text-white/70">Find answers and helpful resources</p>
          </div>
          <Button className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
            <Plus className="h-4 w-4 mr-2" />
            Add Article
          </Button>
        </div>

        {/* Search */}
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/40 h-5 w-5" />
              <Input
                placeholder="Search articles, FAQs, and guides..."
                value={searchTerm}
                onChange={(e: any) => setSearchTerm((e).target.value)}
                className="pl-12 py-3 text-lg bg-white/5 border-white/20 text-white placeholder:text-white/40"
              />
            </div>
          </CardContent>
        </Card>

        {/* Categories */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">Browse by Category</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card 
              className={`glass-card border-white/20 hover:border-white/40 transition-all duration-300 cursor-pointer ${
                selectedCategory === null ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedCategory(null)}
            >
              <CardContent className="p-6 text-center">
                <BookOpen className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                <h3 className="font-semibold text-white mb-1">All Articles</h3>
                <p className="text-sm text-white/60">{(articles).length} articles</p>
              </CardContent>
            </Card>
            {(categories).map((category) => (<Card 
                key={(category).id}
                className={`glass-card border-white/20 hover:border-white/40 transition-all duration-300 cursor-pointer ${
                  selectedCategory === (category).id ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedCategory((category).id)}
              >
                <CardContent className="p-6 text-center">
                  <div 
                    className="mx-auto mb-3 p-2 rounded-lg w-fit"
                    style={{ backgroundColor: `${(category).color}20` }}
                  >
                    {getIconComponent((category).icon)}
                  </div>
                  <h3 className="font-semibold text-white mb-1">{(category).name}</h3>
                  <p className="text-sm text-white/60">
                    {(articles).filter(a => (a).category.id === (category).id).length} articles
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Featured Articles */}
        {(featuredArticles).length > 0 && selectedCategory === null && (<div>
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-400" />
              Featured Articles
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {(featuredArticles).slice(0, 3).map((article) => (<Card key={(article).id} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <Badge className={getArticleTypeColor((article).article_type)}>
                        {getArticleTypeIcon((article).article_type)}
                        <span className="ml-1 capitalize">{(article).article_type}</span>
                      </Badge>
                      <Star className="h-4 w-4 text-yellow-400" />
                    </div>
                    <h3 className="font-semibold text-white mb-2 line-clamp-2">{(article).title}</h3>
                    <p className="text-white/70 text-sm mb-4 line-clamp-3">{(article).summary}</p>
                    <div className="flex items-center justify-between text-xs text-white/60">
                      <div className="flex items-center gap-1">
                        <Eye className="h-3 w-3" />
                        <span>{(article).view_count} views</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <ThumbsUp className="h-3 w-3" />
                          <span>{(article).helpful_votes}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <ThumbsDown className="h-3 w-3" />
                          <span>{(article).not_helpful_votes}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Articles List */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-white">
              {selectedCategory 
                ? `${(categories).find(c => (c).id === selectedCategory)?.name} Articles`
                : 'All Articles'
              }
            </h2>
            <span className="text-white/60">{(filteredArticles).length} articles</span>
          </div>
          
          <div className="space-y-4">
            {(filteredArticles).length === 0 ? (
              <Card className="glass-card border-white/20">
                <CardContent className="p-12 text-center">
                  <BookOpen className="h-12 w-12 text-white/40 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">No articles found</h3>
                  <p className="text-white/60">Try adjusting your search or browse different categories</p>
                </CardContent>
              </Card>
            ) : (
              (filteredArticles).map((article) => (<Card key={(article).id} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-white">{(article).title}</h3>
                          <Badge className={getArticleTypeColor((article).article_type)}>
                            {getArticleTypeIcon((article).article_type)}
                            <span className="ml-1 capitalize">{(article).article_type}</span>
                          </Badge>
                          {(article).is_featured && (
                            <Star className="h-4 w-4 text-yellow-400" />
                          )}
                        </div>
                        
                        <p className="text-white/70 mb-3 line-clamp-2">{(article).summary}</p>
                        
                        <div className="flex items-center gap-6 text-sm text-white/60">
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            <span>{(article).author.first_name} {(article).author.last_name}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>{formatDate((article).created_at)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Eye className="h-4 w-4" />
                            <span>{(article).view_count} views</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              <ThumbsUp className="h-4 w-4" />
                              <span>{(article).helpful_votes}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <ThumbsDown className="h-4 w-4" />
                              <span>{(article).not_helpful_votes}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <Button variant="outline" size="sm" className="border-white/20 text-white hover:bg-white/10 ml-4">
                        <Eye className="h-4 w-4 mr-2" />
                        Read
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBase;
