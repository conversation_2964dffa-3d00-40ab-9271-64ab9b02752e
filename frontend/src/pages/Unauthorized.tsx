import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Shield, ArrowLeft, Home } from 'lucide-react'
import type { RootState } from '../store'

interface UnauthorizedProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'غير مصرح للوصول',
    message: 'ليس لديك صلاحية للوصول إلى هذه الصفحة',
    roleMessage: 'دورك الحالي',
    backToDashboard: 'العودة إلى لوحة التحكم',
    goBack: 'العودة للخلف',
    contactAdmin: 'إذا كنت تعتقد أن هذا خطأ، يرجى الاتصال بمدير النظام'
  },
  en: {
    title: 'Access Denied',
    message: 'You do not have permission to access this page',
    roleMessage: 'Your current role',
    backToDashboard: 'Back to Dashboard',
    goBack: 'Go Back',
    contactAdmin: 'If you believe this is an error, please contact your system administrator'
  }
}

const roleNames = {
  ar: {
    super_admin: 'مدير النظام الرئيسي',
    hr_manager: 'مدير الموارد البشرية',
    finance_manager: 'مدير المالية',
    sales_manager: 'مدير المبيعات',
    department_manager: 'مدير القسم',
    employee: 'موظف'
  },
  en: {
    super_admin: 'Super Administrator',
    hr_manager: 'HR Manager',
    finance_manager: 'Finance Manager',
    sales_manager: 'Sales Manager',
    department_manager: 'Department Manager',
    employee: 'Employee'
  }
}

export default function Unauthorized({ language }: UnauthorizedProps) {
  const navigate = useNavigate()
  const { user } = useSelector((state: RootState => state.auth)
  const t = translations[language]
  const roles = roleNames[language]

  const handleBackToDashboard = () => {
    navigate('/dashboard')
  }

  const handleGoBack = () => {
    navigate(-1)
  }

  return (
    <div className="min-h-screen gradient-bg flex items-center justify-center p-4">
      <Card className="glass-card border-red-500/30 max-w-md w-full">
        <CardContent className="p-8 text-center">
          {/* Icon */}
          <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-red-500/20 flex items-center justify-center">
            <Shield className="w-10 h-10 text-red-400" />
          </div>

          {/* Title */}
          <h1 className="text-2xl font-bold text-white mb-4">
            {t.title}
          </h1>

          {/* Message */}
          <p className="text-white/80 mb-6">
            {t.message}
          </p>

          {/* User Role Info */}
          {user && (
            <div className="glass-card border-white/10 p-4 mb-6 rounded-lg">
              <p className="text-white/70 text-sm mb-1">
                {t.roleMessage}:
              </p>
              <p className="text-white font-medium">
                {roles[user.role.id as keyof typeof roles] || user.role.name}
              </p>
            </div>
          )}

          {/* Contact Admin Message */}
          <p className="text-white/60 text-sm mb-8">
            {t.contactAdmin}
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={handleBackToDashboard}
              className="gradient-bg-blue hover:scale-105 transform transition-all duration-300 glow-hover text-white font-semibold flex-1"
            >
              <Home className="w-4 h-4 mr-2" />
              {t.backToDashboard}
            </Button>
            
            <Button
              onClick={handleGoBack}
              variant="outline"
              className="glass text-white border-white/30 hover:bg-white/20 flex-1"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              {t.goBack}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
