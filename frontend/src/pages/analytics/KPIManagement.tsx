import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { apiClient } from '@/services/api';
import {
  Target,
  Plus,
  Edit,
  TrendingUp,
  TrendingDown,
  Search,
  BarChart3,
  Activity,
  Calculator,
  AlertTriangle
} from 'lucide-react';
// Language is passed as prop, not from context

interface KPIMetric {
  id: number;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  metric_type: string;
  calculation_method: string;
  target_value: number | null;
  warning_threshold: number | null;
  critical_threshold: number | null;
  unit: string;
  frequency: string;
  is_active: boolean;
  current_value: number | null;
  status: string;
  trend: string;
  created_by_name: string;
  created_at: string;
}

interface KPIMetricValue {
  id: number;
  kpi_metric: number;
  kpi_name: string;
  kpi_unit: string;
  period_start: string;
  period_end: string;
  value: number;
  status: string;
  notes: string;
  data_source: string;
  calculated_by_name: string;
  calculated_at: string;
  is_manual: boolean;
}

interface KPIManagementProps {
  language: 'ar' | 'en';
}
const KPIManagement: React.FC<KPIManagementProps> = ({ language }) => {
  // Get authentication state from Redux
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetric[]>([]);
  const [kpiValues, setKpiValues] = useState<KPIMetricValue[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isValueDialogOpen, setIsValueDialogOpen] = useState(false);
  const [selectedKPI, setSelectedKPI] = useState<KPIMetric | null>(null);

  // Form state
  const [kpiForm, setKpiForm] = useState({
    name: '',
    name_ar: '',
    description: '',
    description_ar: '',
    metric_type: 'FINANCIAL',
    calculation_method: 'SUM',
    target_value: '',
    warning_threshold: '',
    critical_threshold: '',
    unit: '',
    frequency: 'MONTHLY',
    is_higher_better: true
  });

  const [valueForm, setValueForm] = useState({
    period_start: '',
    period_end: '',
    value: '',
    notes: '',
    data_source: ''
  });
  useEffect(() => {
    // Only fetch data if user is authenticated
    if (isAuthenticated && user) {
      console.log('🔐 User authenticated, fetching KPI data...', { user: user.username });
      fetchKPIMetrics();
      fetchKPIValues();
    } else {
      console.log('🔐 User not authenticated, skipping KPI data fetch');
      setLoading(false);
    }
  }, [searchTerm, typeFilter, statusFilter, isAuthenticated, user]);
  const fetchKPIMetrics: any = async () => {
    try {
      setError(null);
      console.log('🔄 Fetching KPI metrics...', { isAuthenticated, user: user?.username });
      const params: Record<string, string> = {};
      if (searchTerm) params.search = searchTerm;
      if (typeFilter && typeFilter !== '__all__') params.metric_type = typeFilter;
      if (statusFilter === 'active') params.is_active = 'true';
      if (statusFilter === 'inactive') params.is_active = 'false';

      const response: any = await apiClient.get('/kpi-metrics/', { params });
      const data: any = response.data;
      setKpiMetrics(data.results || data);
      console.log('✅ KPI metrics fetched successfully:', data.results?.length || data.length);
    } catch (error) {
      console.error('❌ Error fetching KPI metrics:', error);
      const errorMessage: any = error instanceof Error ? error.message : 'Unknown error occurred';
      if (errorMessage.includes('401')) {
        setError('Authentication failed. Please log in again.');
      } else if (errorMessage.includes('403')) {
        setError('You do not have permission to view KPI data.');
      } else if (errorMessage.includes('Network Error')) {
        setError('Network error. Please check your connection and try again.');
      } else {
        setError(`Failed to load KPI data: ${errorMessage}`);
      }
    } finally {
      setLoading(false);
    }
  };
  const fetchKPIValues: any = async () => {
    try {
      const response = await apiClient.get('/kpi-metric-values/', {
        params: { ordering: '-calculated_at' }
      });
      const data: any = response.data;
      setKpiValues(data.results || data);
    } catch (error) {
      console.error('Error fetching KPI values:', error);
    }
  };
  const handleAddKPI: any = async () => {
    try {
      await apiClient.post('/kpi-metrics/', {
        ...kpiForm,
        target_value: kpiForm.target_value ? parseFloat(kpiForm.target_value) : null,
        warning_threshold: kpiForm.warning_threshold ? parseFloat(kpiForm.warning_threshold) : null,
        critical_threshold: kpiForm.critical_threshold ? parseFloat(kpiForm.critical_threshold) : null,
      });

      fetchKPIMetrics();
      setIsAddDialogOpen(false);
      resetKPIForm();
    } catch (error) {
      console.error('Error adding KPI:', error);
    }
  };
  const handleAddValue: any = async () => {
    if (!selectedKPI) return;

    try {
      await apiClient.post('/kpi-metric-values/', {
        ...valueForm,
        kpi_metric: selectedKPI.id,
        value: parseFloat(valueForm.value),
      });

      fetchKPIValues();
      fetchKPIMetrics(); // Refresh to get updated current values
      setIsValueDialogOpen(false);
      resetValueForm();
      setSelectedKPI(null);
    } catch (error) {
      console.error('Error adding KPI value:', error);
    }
  };
  const resetKPIForm: any = (): void => {
    setKpiForm({
      name: '',
      name_ar: '',
      description: '',
      description_ar: '',
      metric_type: 'FINANCIAL',
      calculation_method: 'SUM',
      target_value: '',
      warning_threshold: '',
      critical_threshold: '',
      unit: '',
      frequency: 'MONTHLY',
      is_higher_better: true
    });
  };
  const resetValueForm: any = (): void => {
    setValueForm({
      period_start: '',
      period_end: '',
      value: '',
      notes: '',
      data_source: ''
    });
  };
  const getStatusColor: any = (status: string): void => {
    const colors = {
      'GOOD': 'bg-green-100 text-green-800',
      'WARNING': 'bg-yellow-100 text-yellow-800',
      'CRITICAL': 'bg-red-100 text-red-800',
      'NO_DATA': 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || colors.NO_DATA;
  };
  const getTrendIcon: any = (trend: string): void => {
    switch (trend) {
      case 'INCREASING':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'DECREASING':
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };
  const formatValue: any = (value: number | null, unit: string): string => {
    if (value === null || typeof value !== 'number') return '-';

    if (unit === 'SAR') {
      return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(value);
    } else if (unit === '%') {
      return `${value.toFixed(1)}%`;
    } else {
      return `${value.toLocaleString()} ${unit}`;
    }
  };
  const filteredMetrics: any = kpiMetrics.filter(metric => {
    const matchesSearch = !searchTerm ||
      metric.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      metric.name_ar.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType: any = !typeFilter || typeFilter === '__all__' || metric.metric_type === typeFilter;
    const matchesStatus: any = !statusFilter || statusFilter === '__all__' ||
      (statusFilter === 'active' && metric.is_active) ||
      (statusFilter === 'inactive' && !metric.is_active);
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  // Show authentication required message
  if (!isAuthenticated || !user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-lg text-red-600 mb-2">
            {language === 'ar' ? 'مطلوب تسجيل الدخول' : 'Authentication Required'}
          </div>
          <div className="text-sm text-gray-600">
            {language === 'ar'
              ? 'يرجى تسجيل الدخول للوصول إلى إدارة مؤشرات الأداء'
              : 'Please log in to access KPI Management'}
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center max-w-md">
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6">
            <div className="flex items-center justify-center space-x-2 text-red-300 mb-3">
              <AlertTriangle className="h-6 w-6" />
              <span className="font-semibold">
                {language === 'ar' ? 'خطأ في تحميل البيانات' : 'Data Loading Error'}
              </span>
            </div>
            <p className="text-red-200 text-sm mb-4">{error}</p>
            <button
              onClick={() => {
                setError(null);
                fetchKPIMetrics();
              }}
              className="px-4 py-2 bg-red-500/20 text-red-300 rounded-lg hover:bg-red-500/30 transition-colors"
            >
              {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة مؤشرات الأداء الرئيسية' : 'KPI Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إنشاء وإدارة ومراقبة مؤشرات الأداء الرئيسية'
              : 'Create, manage, and monitor key performance indicators'
            }
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إضافة مؤشر' : 'Add KPI'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'إضافة مؤشر أداء جديد' : 'Add New KPI'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>{language === 'ar' ? 'اسم المؤشر' : 'KPI Name'}</Label>
                <Input
                  value={kpiForm.name}
                  onChange={(e: any) => setKpiForm({...kpiForm, name: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل اسم المؤشر' : 'Enter KPI name'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الاسم بالعربية' : 'Arabic Name'}</Label>
                <Input
                  value={kpiForm.name_ar}
                  onChange={(e: any) => setKpiForm({...kpiForm, name_ar: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل الاسم بالعربية' : 'Enter Arabic name'}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'الوصف' : 'Description'}</Label>
                <Textarea
                  value={kpiForm.description}
                  onChange={(e: any) => setKpiForm({...kpiForm, description: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف المؤشر' : 'Enter KPI description'}
                  rows={3}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع المؤشر' : 'Metric Type'}</Label>
                <Select value={kpiForm.metric_type} onValueChange={(value) => setKpiForm({...kpiForm, metric_type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="FINANCIAL">
                      {language === 'ar' ? 'مالي' : 'Financial'}
                    </SelectItem>
                    <SelectItem value="OPERATIONAL">
                      {language === 'ar' ? 'تشغيلي' : 'Operational'}
                    </SelectItem>
                    <SelectItem value="CUSTOMER">
                      {language === 'ar' ? 'عملاء' : 'Customer'}
                    </SelectItem>
                    <SelectItem value="EMPLOYEE">
                      {language === 'ar' ? 'موظفين' : 'Employee'}
                    </SelectItem>
                    <SelectItem value="ASSET">
                      {language === 'ar' ? 'أصول' : 'Asset'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'طريقة الحساب' : 'Calculation Method'}</Label>
                <Select value={kpiForm.calculation_method} onValueChange={(value) => setKpiForm({...kpiForm, calculation_method: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SUM">
                      {language === 'ar' ? 'مجموع' : 'Sum'}
                    </SelectItem>
                    <SelectItem value="AVERAGE">
                      {language === 'ar' ? 'متوسط' : 'Average'}
                    </SelectItem>
                    <SelectItem value="COUNT">
                      {language === 'ar' ? 'عدد' : 'Count'}
                    </SelectItem>
                    <SelectItem value="PERCENTAGE">
                      {language === 'ar' ? 'نسبة مئوية' : 'Percentage'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'القيمة المستهدفة' : 'Target Value'}</Label>
                <Input
                  type="number"
                  step="(0).01"
                  value={kpiForm.target_value}
                  onChange={(e: any) => setKpiForm({...kpiForm, target_value: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل القيمة المستهدفة' : 'Enter target value'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'الوحدة' : 'Unit'}</Label>
                <Input
                  value={kpiForm.unit}
                  onChange={(e: any) => setKpiForm({...kpiForm, unit: e.target.value})}
                  placeholder={language === 'ar' ? 'مثل: %, SAR, أيام' : 'e.g., %, SAR, days'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'التكرار' : 'Frequency'}</Label>
                <Select value={kpiForm.frequency} onValueChange={(value) => setKpiForm({...kpiForm, frequency: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DAILY">
                      {language === 'ar' ? 'يومي' : 'Daily'}
                    </SelectItem>
                    <SelectItem value="WEEKLY">
                      {language === 'ar' ? 'أسبوعي' : 'Weekly'}
                    </SelectItem>
                    <SelectItem value="MONTHLY">
                      {language === 'ar' ? 'شهري' : 'Monthly'}
                    </SelectItem>
                    <SelectItem value="QUARTERLY">
                      {language === 'ar' ? 'ربع سنوي' : 'Quarterly'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleAddKPI}>
                {language === 'ar' ? 'إضافة المؤشر' : 'Add KPI'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في المؤشرات...' : 'Search KPIs...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                <SelectItem value="FINANCIAL">
                  {language === 'ar' ? 'مالي' : 'Financial'}
                </SelectItem>
                <SelectItem value="OPERATIONAL">
                  {language === 'ar' ? 'تشغيلي' : 'Operational'}
                </SelectItem>
                <SelectItem value="ASSET">
                  {language === 'ar' ? 'أصول' : 'Asset'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="active">
                  {language === 'ar' ? 'نشط' : 'Active'}
                </SelectItem>
                <SelectItem value="inactive">
                  {language === 'ar' ? 'غير نشط' : 'Inactive'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* KPI Metrics Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            {language === 'ar' ? 'مؤشرات الأداء الرئيسية' : 'Key Performance Indicators'}
            <Badge variant="secondary" className="ml-2">
              {filteredMetrics.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم المؤشر' : 'KPI Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'القيمة الحالية' : 'Current Value'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الهدف' : 'Target'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الاتجاه' : 'Trend'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMetrics.map((metric) => (<TableRow key={metric.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {language === 'ar' ? metric.name_ar : metric.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {metric.frequency} • {metric.created_by_name}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {language === 'ar' 
                          ? metric.metric_type === 'FINANCIAL' ? 'مالي' 
                            : metric.metric_type === 'OPERATIONAL' ? 'تشغيلي'
                            : metric.metric_type === 'ASSET' ? 'أصول'
                            : metric.metric_type
                          : metric.metric_type
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {formatValue(metric.current_value, metric.unit)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {formatValue(metric.target_value, metric.unit)}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(metric.status)}>
                        {language === 'ar' 
                          ? metric.status === 'GOOD' ? 'جيد' 
                            : metric.status === 'WARNING' ? 'تحذير'
                            : metric.status === 'CRITICAL' ? 'حرج'
                            : 'لا توجد بيانات'
                          : metric.status.replace('_', ' ')
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(metric.trend)}
                        <span className="text-sm">
                          {language === 'ar' 
                            ? metric.trend === 'INCREASING' ? 'متزايد' 
                              : metric.trend === 'DECREASING' ? 'متناقص'
                              : 'مستقر'
                            : metric.trend.replace('_', ' ')
                          }
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedKPI(metric);
                            setIsValueDialogOpen(true);
                          }}
                        >
                          <Calculator className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <BarChart3 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Value Dialog */}
      <Dialog open={isValueDialogOpen} onOpenChange={setIsValueDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'إضافة قيمة للمؤشر' : 'Add KPI Value'}
            </DialogTitle>
          </DialogHeader>
          {selectedKPI && (<div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="font-medium">
                  {language === 'ar' ? selectedKPI.name_ar : selectedKPI.name}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? 'الهدف:' : 'Target:'} {formatValue(selectedKPI.target_value, selectedKPI.unit)}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'بداية الفترة' : 'Period Start'}</Label>
                  <Input
                    type="date"
                    value={valueForm.period_start}
                    onChange={(e: any) => setValueForm({...valueForm, period_start: e.target.value})}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'نهاية الفترة' : 'Period End'}</Label>
                  <Input
                    type="date"
                    value={valueForm.period_end}
                    onChange={(e: any) => setValueForm({...valueForm, period_end: e.target.value})}
                  />
                </div>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'القيمة' : 'Value'}</Label>
                <Input
                  type="number"
                  step="(0).01"
                  value={valueForm.value}
                  onChange={(e: any) => setValueForm({...valueForm, value: e.target.value})}
                  placeholder={`${language === 'ar' ? 'أدخل القيمة بـ' : 'Enter value in'} ${selectedKPI.unit}`}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'مصدر البيانات' : 'Data Source'}</Label>
                <Input
                  value={valueForm.data_source}
                  onChange={(e: any) => setValueForm({...valueForm, data_source: e.target.value})}
                  placeholder={language === 'ar' ? 'مصدر البيانات' : 'Data source'}
                />
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'ملاحظات' : 'Notes'}</Label>
                <Textarea
                  value={valueForm.notes}
                  onChange={(e: any) => setValueForm({...valueForm, notes: e.target.value})}
                  placeholder={language === 'ar' ? 'ملاحظات إضافية' : 'Additional notes'}
                  rows={3}
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsValueDialogOpen(false)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleAddValue}>
                  {language === 'ar' ? 'إضافة القيمة' : 'Add Value'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default KPIManagement;
