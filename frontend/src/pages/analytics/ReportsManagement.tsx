import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  FileText, 
  Plus, 
  Play, 
  Download,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  Calendar,
  Settings,
  Eye
} from 'lucide-react';
// Language is passed as prop, not from context

interface ReportTemplate {
  id: number;
  name: string;
  name_ar: string;
  description: string;
  description_ar: string;
  report_type: string;
  output_formats: string[];
  is_public: boolean;
  created_by_name: string;
  execution_count: number;
  last_executed: string | null;
  created_at: string;
  is_active: boolean;
}

interface ReportExecution {
  id: number;
  template: number;
  template_name: string;
  output_format: string;
  status: string;
  started_at: string | null;
  completed_at: string | null;
  duration_seconds: number | null;
  file_size: number | null;
  error_message: string;
  requested_by_name: string;
  created_at: string;
}

interface ReportsManagementProps {
  language?: 'ar' | 'en';
}
const ReportsManagement: React.FC<ReportsManagementProps> = ({ language = 'en' }) => {
  const [reportTemplates, setReportTemplates] = useState<ReportTemplate[]>([]);
  const [reportExecutions, setReportExecutions] = useState<ReportExecution[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isExecuteDialogOpen, setIsExecuteDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);

  // Execution form state
  const [executionForm, setExecutionForm] = useState({
    output_format: 'PDF',
    parameters: {
      date_from: '',
      date_to: '',
      department: '',
      include_details: true
    }
  });
  useEffect(() => {
    fetchReportTemplates();
    fetchReportExecutions();
  }, []);
  const fetchReportTemplates: any = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (typeFilter) params.append('report_type', typeFilter);
      if (statusFilter === 'active') params.append('is_active', 'true');
      if (statusFilter === 'inactive') params.append('is_active', 'false');
      const response: any = await fetch(`/api/report-templates/?${params.toString()}`);
      if (response.ok) {
        const data: any = await response.json();
        setReportTemplates(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching report templates:', error);
    } finally {
      setLoading(false);
    }
  };
  const fetchReportExecutions: any = async () => {
    try {
      const response = await fetch('/api/report-executions/?ordering=-created_at&page_size=20');
      if (response.ok) {
        const data: any = await response.json();
        setReportExecutions(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching report executions:', error);
    }
  };
  const handleExecuteReport: any = async () => {
    if (!selectedTemplate) return;

    try {
      const response: any = await fetch(`/api/report-templates/${selectedTemplate.id}/execute/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(executionForm),
      });

      if (response.ok) {
        const data: any = await response.json();
        fetchReportExecutions();
        setIsExecuteDialogOpen(false);
        setSelectedTemplate(null);
        resetExecutionForm();
        
        // Show success message
        alert(language === 'ar' ? 'تم بدء تنفيذ التقرير' : 'Report execution started');
      }
    } catch (error) {
      console.error('Error executing report:', error);
    }
  };
  const resetExecutionForm: any = (): void => {
    setExecutionForm({
      output_format: 'PDF',
      parameters: {
        date_from: '',
        date_to: '',
        department: '',
        include_details: true
      }
    });
  };
  const getStatusColor: any = (status: string): void => {
    const colors = {
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'RUNNING': 'bg-blue-100 text-blue-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'FAILED': 'bg-red-100 text-red-800',
      'CANCELLED': 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || colors.PENDING;
  };
  const getStatusIcon: any = (status: string): void => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'RUNNING':
        return <Clock className="h-4 w-4 text-blue-600 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };
  const formatFileSize: any = (bytes: number | null): string => {
    if (!bytes) return '-';
    const sizes: any = ['Bytes', 'KB', 'MB', 'GB'];
    const i: any = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`;
  };
  const formatDuration: any = (seconds: number | null): string => {
    if (!seconds) return '-';
    const minutes: any = Math.floor(seconds / 60);
    const remainingSeconds: any = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  const filteredTemplates: any = reportTemplates.filter(template => {
    const matchesSearch = !searchTerm || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.name_ar.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType: any = !typeFilter || template.report_type === typeFilter;
    const matchesStatus: any = !statusFilter || 
      (statusFilter === 'active' && template.is_active) ||
      (statusFilter === 'inactive' && !template.is_active);
    
    return matchesSearch && matchesType && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'إدارة التقارير' : 'Reports Management'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إنشاء وتنفيذ وإدارة التقارير التحليلية'
              : 'Create, execute, and manage analytical reports'
            }
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          {language === 'ar' ? 'إنشاء قالب' : 'Create Template'}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في التقارير...' : 'Search reports...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                <SelectItem value="EXECUTIVE">
                  {language === 'ar' ? 'تنفيذي' : 'Executive'}
                </SelectItem>
                <SelectItem value="FINANCIAL">
                  {language === 'ar' ? 'مالي' : 'Financial'}
                </SelectItem>
                <SelectItem value="OPERATIONAL">
                  {language === 'ar' ? 'تشغيلي' : 'Operational'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Report Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {language === 'ar' ? 'قوالب التقارير' : 'Report Templates'}
            <Badge variant="secondary" className="ml-2">
              {filteredTemplates.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'اسم التقرير' : 'Report Name'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التنسيقات' : 'Formats'}</TableHead>
                  <TableHead>{language === 'ar' ? 'عدد التنفيذ' : 'Executions'}</TableHead>
                  <TableHead>{language === 'ar' ? 'آخر تنفيذ' : 'Last Executed'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTemplates.map((template) => (<TableRow key={template.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {language === 'ar' ? template.name_ar : template.name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {language === 'ar' ? template.description_ar : template.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {language === 'ar' 
                          ? template.report_type === 'EXECUTIVE' ? 'تنفيذي' 
                            : template.report_type === 'FINANCIAL' ? 'مالي'
                            : template.report_type === 'OPERATIONAL' ? 'تشغيلي'
                            : template.report_type
                          : template.report_type
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {template.output_formats.map((format) => (
                          <Badge key={format} variant="secondary" className="text-xs">
                            {format}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{template.execution_count}</div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {template.last_executed
                          ? new Date(template.last_executed).toLocaleDateString()
                          : (language === 'ar' ? 'لم ينفذ بعد' : 'Never')
                        }
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedTemplate(template);
                            setIsExecuteDialogOpen(true);
                          }}
                        >
                          <Play className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Settings className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Recent Executions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {language === 'ar' ? 'التنفيذات الأخيرة' : 'Recent Executions'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'التقرير' : 'Report'}</TableHead>
                  <TableHead>{language === 'ar' ? 'التنسيق' : 'Format'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'المدة' : 'Duration'}</TableHead>
                  <TableHead>{language === 'ar' ? 'حجم الملف' : 'File Size'}</TableHead>
                  <TableHead>{language === 'ar' ? 'طلب بواسطة' : 'Requested By'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reportExecutions.map((execution) => (<TableRow key={execution.id}>
                    <TableCell>
                      <div className="font-medium">{execution.template_name}</div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(execution.created_at).toLocaleString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">{execution.output_format}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(execution.status)}
                        <Badge className={getStatusColor(execution.status)}>
                          {language === 'ar' 
                            ? execution.status === 'COMPLETED' ? 'مكتمل' 
                              : execution.status === 'RUNNING' ? 'قيد التنفيذ'
                              : execution.status === 'FAILED' ? 'فشل'
                              : execution.status === 'PENDING' ? 'في الانتظار'
                              : execution.status
                            : execution.status
                          }
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {formatDuration(execution.duration_seconds)}
                    </TableCell>
                    <TableCell>
                      {formatFileSize(execution.file_size)}
                    </TableCell>
                    <TableCell>
                      {execution.requested_by_name}
                    </TableCell>
                    <TableCell>
                      {execution.status === 'COMPLETED' && (
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Execute Report Dialog */}
      <Dialog open={isExecuteDialogOpen} onOpenChange={setIsExecuteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'تنفيذ التقرير' : 'Execute Report'}
            </DialogTitle>
          </DialogHeader>
          {selectedTemplate && (<div className="space-y-4">
              <div className="p-4 bg-muted rounded-lg">
                <div className="font-medium">
                  {language === 'ar' ? selectedTemplate.name_ar : selectedTemplate.name}
                </div>
                <div className="text-sm text-muted-foreground">
                  {language === 'ar' ? selectedTemplate.description_ar : selectedTemplate.description}
                </div>
              </div>
              
              <div>
                <Label>{language === 'ar' ? 'تنسيق الإخراج' : 'Output Format'}</Label>
                <Select 
                  value={executionForm.output_format} 
                  onValueChange={(value) => setExecutionForm({...executionForm, output_format: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedTemplate.output_formats.map((format) => (
                      <SelectItem key={format} value={format}>
                        {format}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>{language === 'ar' ? 'من تاريخ' : 'From Date'}</Label>
                  <Input
                    type="date"
                    value={executionForm.parameters.date_from}
                    onChange={(e: any) => setExecutionForm({
                      ...executionForm,
                      parameters: {...executionForm.parameters, date_from: e.target.value}
                    })}
                  />
                </div>
                <div>
                  <Label>{language === 'ar' ? 'إلى تاريخ' : 'To Date'}</Label>
                  <Input
                    type="date"
                    value={executionForm.parameters.date_to}
                    onChange={(e: any) => setExecutionForm({
                      ...executionForm,
                      parameters: {...executionForm.parameters, date_to: e.target.value}
                    })}
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsExecuteDialogOpen(false)}>
                  {language === 'ar' ? 'إلغاء' : 'Cancel'}
                </Button>
                <Button onClick={handleExecuteReport}>
                  <Play className="h-4 w-4 mr-2" />
                  {language === 'ar' ? 'تنفيذ التقرير' : 'Execute Report'}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ReportsManagement;
