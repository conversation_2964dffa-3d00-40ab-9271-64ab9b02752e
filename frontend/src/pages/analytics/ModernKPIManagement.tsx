/**
 * Modern KPI Management Page with Full CRUD Implementation
 * Uses standardized CRUD components for consistency and enhanced functionality
 */

import React, { useState, useMemo } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Target,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Activity,
  Calculator,
  AlertTriangle,
  Eye,
  Edit,
  Trash2,
  Calendar,
  User,
  Building,
  DollarSign,
  Percent,
  Clock
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { kpiService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'
import { formatKPIValue, formatTargetDisplay, getKPIStatusColor } from '@/utils/kpiFormatting'
import { transformKPIWithArabicSupport } from '@/utils/kpiFilters'

interface ModernKPIManagementProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


interface KPIManagementProps {
  language: 'ar' | 'en'
}

interface KPIMetric {
  id: string
  name: string
  name_ar: string
  description: string
  description_ar: string
  metric_type: string
  calculation_method: string
  target_value: number | string | null  // API returns strings, we convert to numbers
  warning_threshold: number | string | null
  critical_threshold: number | string | null
  unit: string
  frequency: string
  is_active: boolean
  current_value: number | string | null
  status: string
  trend: string
  created_by_name: string
  created_at: string
  last_updated: string
  is_higher_better: boolean
}

const translations = {
  ar: {
    title: 'إدارة مؤشرات الأداء الرئيسية',
    subtitle: 'إنشاء وإدارة ومراقبة مؤشرات الأداء الرئيسية',
    addKPI: 'إضافة مؤشر أداء',
    editKPI: 'تعديل مؤشر الأداء',
    viewKPI: 'عرض مؤشر الأداء',
    deleteKPI: 'حذف مؤشر الأداء',
    name: 'اسم المؤشر',
    nameAr: 'الاسم بالعربية',
    description: 'الوصف',
    descriptionAr: 'الوصف بالعربية',
    metricType: 'نوع المؤشر',
    calculationMethod: 'طريقة الحساب',
    targetValue: 'القيمة المستهدفة',
    warningThreshold: 'عتبة التحذير',
    criticalThreshold: 'العتبة الحرجة',
    unit: 'الوحدة',
    frequency: 'التكرار',
    isActive: 'نشط',
    currentValue: 'القيمة الحالية',
    status: 'الحالة',
    trend: 'الاتجاه',
    createdBy: 'أنشأ بواسطة',
    createdAt: 'تاريخ الإنشاء',
    lastUpdated: 'آخر تحديث',
    isHigherBetter: 'الأعلى أفضل',
    actions: 'الإجراءات',
    search: 'البحث في مؤشرات الأداء...',
    filterByType: 'تصفية حسب النوع',
    filterByStatus: 'تصفية حسب الحالة',
    filterByFrequency: 'تصفية حسب التكرار',
    allTypes: 'جميع الأنواع',
    allStatuses: 'جميع الحالات',
    allFrequencies: 'جميع التكرارات',
    active: 'نشط',
    inactive: 'غير نشط',
    good: 'جيد',
    warning: 'تحذير',
    critical: 'حرج',
    noData: 'لا توجد بيانات',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    financial: 'مالي',
    operational: 'تشغيلي',
    hr: 'موارد بشرية',
    customer: 'عملاء',
    strategic: 'استراتيجي'
  },
  en: {
    title: 'Key Performance Indicators Management',
    subtitle: 'Create, manage, and monitor key performance indicators',
    addKPI: 'Add KPI',
    editKPI: 'Edit KPI',
    viewKPI: 'View KPI',
    deleteKPI: 'Delete KPI',
    name: 'Name',
    nameAr: 'Arabic Name',
    description: 'Description',
    descriptionAr: 'Arabic Description',
    metricType: 'Metric Type',
    calculationMethod: 'Calculation Method',
    targetValue: 'Target Value',
    warningThreshold: 'Warning Threshold',
    criticalThreshold: 'Critical Threshold',
    unit: 'Unit',
    frequency: 'Frequency',
    isActive: 'Active',
    currentValue: 'Current Value',
    status: 'Status',
    trend: 'Trend',
    createdBy: 'Created By',
    createdAt: 'Created At',
    lastUpdated: 'Last Updated',
    isHigherBetter: 'Higher is Better',
    actions: 'Actions',
    search: 'Search KPIs...',
    filterByType: 'Filter by Type',
    filterByStatus: 'Filter by Status',
    filterByFrequency: 'Filter by Frequency',
    allTypes: 'All Types',
    allStatuses: 'All Statuses',
    allFrequencies: 'All Frequencies',
    active: 'Active',
    inactive: 'Inactive',
    good: 'Good',
    warning: 'Warning',
    critical: 'Critical',
    noData: 'No Data',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    financial: 'Financial',
    operational: 'Operational',
    hr: 'HR',
    customer: 'Customer',
    strategic: 'Strategic'
  }
}

// Status badge component
const StatusBadge = ({ status, language }: { status: string, language: 'ar' | 'en' }): React.ReactElement => {
  const getStatusColor = (status: string): void => {
    switch (status.toLowerCase()) {
      case 'good':
      case 'جيد':
        return 'bg-green-100 text-green-800'
      case 'warning':
      case 'تحذير':
        return 'bg-yellow-100 text-yellow-800'
      case 'critical':
      case 'حرج':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string): void => {
    if (language === 'ar') {
      switch (status.toLowerCase()) {
        case 'good': return 'جيد'
        case 'warning': return 'تحذير'
        case 'critical': return 'حرج'
        default: return 'لا توجد بيانات'
      }
    }
    return status
  }

  return (<Badge className={getStatusColor(status)}>
      {getStatusText(status)}
    </Badge>
  )
}

// Trend icon component
const TrendIcon = ({ trend }: { trend: string }): React.ReactElement => {
  switch (trend?.toLowerCase()) {
    case 'up':
    case 'increasing':
      return <TrendingUp className="h-4 w-4 text-green-500" />
    case 'down':
    case 'decreasing':
      return <TrendingDown className="h-4 w-4 text-red-500" />
    default:
      return <Activity className="h-4 w-4 text-gray-500" />
  }
}

// Metric type icon component
const MetricTypeIcon = ({ type }: { type: string }): React.ReactElement => {
  switch (type?.toLowerCase()) {
    case 'financial':
    case 'مالي':
      return <DollarSign className="h-4 w-4 text-green-500" />
    case 'percentage':
    case 'نسبة':
      return <Percent className="h-4 w-4 text-blue-500" />
    case 'time':
    case 'وقت':
      return <Clock className="h-4 w-4 text-purple-500" />
    case 'hr':
    case 'موارد بشرية':
      return <User className="h-4 w-4 text-orange-500" />
    case 'operational':
    case 'تشغيلي':
      return <Building className="h-4 w-4 text-indigo-500" />
    default:
      return <BarChart3 className="h-4 w-4 text-gray-500" />
  }
}
export default function ModernKPIManagement({ language }: KPIManagementProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: rawKpis,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<KPIMetric>({
    service: kpiService,
    autoLoad: true,
    pageSize: 20,
    entityType: 'kpi',
    enableInvalidation: true
  })

  // Transform KPI data to handle string-to-number conversion for target values
  const kpis = useMemo(() => {
    return rawKpis.map(kpi => {
      // Convert string target_value to number if needed
      const transformedKpi = {
        ...kpi,
        target_value: kpi.target_value !== null && kpi.target_value !== undefined ?
          (typeof kpi.target_value === 'string' ? parseFloat(kpi.target_value) : kpi.target_value) :
          null,
        warning_threshold: kpi.warning_threshold !== null && kpi.warning_threshold !== undefined ?
          (typeof kpi.warning_threshold === 'string' ? parseFloat(kpi.warning_threshold) : kpi.warning_threshold) :
          null,
        critical_threshold: kpi.critical_threshold !== null && kpi.critical_threshold !== undefined ?
          (typeof kpi.critical_threshold === 'string' ? parseFloat(kpi.critical_threshold) : kpi.critical_threshold) :
          null,
        current_value: kpi.current_value !== null && kpi.current_value !== undefined ?
          (typeof kpi.current_value === 'string' ? parseFloat(kpi.current_value) : kpi.current_value) :
          null
      }

      console.log('🔧 KPI Data Transformation:', {
        id: kpi.id,
        name: kpi.name,
        original_target: kpi.target_value,
        transformed_target: transformedKpi.target_value,
        target_type: typeof transformedKpi.target_value
      })

      return transformedKpi
    })
  }, [rawKpis])

  // Table columns configuration
  const columns: TableColumn<KPIMetric>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (kpi) => (<div className="flex items-center space-x-2 rtl:space-x-reverse">
          <MetricTypeIcon type={kpi.metric_type} />
          <div>
            <div className="font-medium">
              {language === 'ar' ? (kpi.name_ar || kpi.name) : kpi.name}
            </div>
            <div className="text-sm text-gray-500">
              {kpi.frequency} • {kpi.created_by_name}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'metric_type',
      label: t.metricType,
      sortable: true,
      render: (kpi) => (<Badge variant="outline">
          {language === 'ar' ? 
            (kpi.metric_type === 'FINANCIAL' ? 'مالي' : 
             kpi.metric_type === 'EMPLOYEE' ? 'موظفين' : 
             kpi.metric_type === 'OPERATIONAL' ? 'تشغيلي' : kpi.metric_type) 
            : kpi.metric_type}
        </Badge>
      )
    },
    {
      key: 'current_value',
      label: t.currentValue,
      sortable: true,
      render: (kpi) => (<div className="text-right rtl:text-left">
          {kpi.current_value !== null ? 
            formatKPIValue(kpi.current_value, kpi.unit, { language, precision: 2 }) : 
            <span className="text-gray-400">-</span>
          }
        </div>
      )
    },
    {
      key: 'target_value',
      label: t.targetValue,
      sortable: true,
      render: (kpi) => {
        // Check if target value exists and is not zero (handle both string and number types)
        const hasValidTarget = kpi.target_value !== null &&
                              kpi.target_value !== undefined &&
                              kpi.target_value !== 0 &&
                              kpi.target_value !== '0' &&
                              kpi.target_value !== '0.00'

        return (<div className="text-right rtl:text-left text-sm text-gray-600">
            {hasValidTarget ?
              formatKPIValue(kpi.target_value, kpi.unit, { language, precision: 1 }) :
              <span className="text-gray-400">--</span>
            }
          </div>
        )
      }
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (kpi) => <StatusBadge status={kpi.status || 'no data'} language={language} />
    },
    {
      key: 'trend',
      label: t.trend,
      render: (kpi) => (<div className="flex items-center justify-center">
          <TrendIcon trend={kpi.trend || 'stable'} />
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<KPIMetric>[] = [
    {
      label: t.viewKPI,
      icon: Eye,
      onClick: (kpi) => {
        console.log('👁️ Viewing KPI:', kpi.id, kpi.name)
        selectItem(kpi)
        setModalMode('view')
        setShowModal(true)
      }
    },
    {
      label: t.editKPI,
      icon: Edit,
      onClick: (kpi) => {
        console.log('✏️ Editing KPI:', kpi.id, kpi.name)
        selectItem(kpi)
        setModalMode('edit')
        setShowModal(true)
      }
    },
    {
      label: t.deleteKPI,
      icon: Trash2,
      onClick: async (kpi) => {
        const confirmMessage = language === 'ar'
          ? `هل أنت متأكد من حذف المؤشر "${kpi.name_ar || kpi.name}"؟`
          : `Are you sure you want to delete "${kpi.name}"?`

        if (confirm(confirmMessage)) {
          try {
            console.log('🗑️ Deleting KPI:', kpi.id, kpi.name)
            await deleteItem(kpi.id)
            console.log('✅ KPI deleted successfully')
          } catch (error) {
            console.error('❌ Error deleting KPI:', error)
          }
        }
      },
      variant: 'destructive'
    }
  ]

  // Filter options configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'metric_type',
      label: t.filterByType,
      options: [
        { label: t.allTypes, value: '' },
        { label: t.financial, value: 'FINANCIAL' },
        { label: t.hr, value: 'EMPLOYEE' },
        { label: t.operational, value: 'OPERATIONAL' },
        { label: t.customer, value: 'CUSTOMER' },
        { label: t.strategic, value: 'STRATEGIC' }
      ]
    },
    {
      key: 'status',
      label: t.filterByStatus,
      options: [
        { label: t.allStatuses, value: '' },
        { label: t.good, value: 'good' },
        { label: t.warning, value: 'warning' },
        { label: t.critical, value: 'critical' },
        { label: t.noData, value: 'no_data' }
      ]
    },
    {
      key: 'frequency',
      label: t.filterByFrequency,
      options: [
        { label: t.allFrequencies, value: '' },
        { label: t.daily, value: 'DAILY' },
        { label: t.weekly, value: 'WEEKLY' },
        { label: t.monthly, value: 'MONTHLY' },
        { label: t.quarterly, value: 'QUARTERLY' },
        { label: t.yearly, value: 'YEARLY' }
      ]
    },
    {
      key: 'is_active',
      label: 'Status',
      options: [
        { label: language === 'ar' ? 'الكل' : 'All', value: '' },
        { label: t.active, value: 'true' },
        { label: t.inactive, value: 'false' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل اسم المؤشر' : 'Enter KPI name'
    },
    {
      name: 'name_ar',
      label: t.nameAr,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل الاسم بالعربية' : 'Enter Arabic name'
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      placeholder: language === 'ar' ? 'أدخل وصف المؤشر' : 'Enter KPI description'
    },
    {
      name: 'description_ar',
      label: t.descriptionAr,
      type: 'textarea',
      placeholder: language === 'ar' ? 'أدخل الوصف بالعربية' : 'Enter Arabic description'
    },
    {
      name: 'metric_type',
      label: t.metricType,
      type: 'select',
      required: true,
      options: [
        { label: t.financial, value: 'FINANCIAL' },
        { label: t.hr, value: 'EMPLOYEE' },
        { label: t.operational, value: 'OPERATIONAL' },
        { label: t.customer, value: 'CUSTOMER' },
        { label: language === 'ar' ? 'أصول' : 'Asset', value: 'ASSET' },
        { label: language === 'ar' ? 'مخصص' : 'Custom', value: 'CUSTOM' }
      ]
    },
    {
      name: 'calculation_method',
      label: t.calculationMethod,
      type: 'select',
      required: true,
      options: [
        { label: 'SUM', value: 'SUM' },
        { label: 'AVERAGE', value: 'AVERAGE' },
        { label: 'COUNT', value: 'COUNT' },
        { label: 'PERCENTAGE', value: 'PERCENTAGE' },
        { label: 'RATIO', value: 'RATIO' },
        { label: 'CUSTOM_FORMULA', value: 'CUSTOM_FORMULA' }
      ]
    },
    {
      name: 'target_value',
      label: t.targetValue,
      type: 'number',
      step: 0.01,
      placeholder: language === 'ar' ? 'أدخل القيمة المستهدفة' : 'Enter target value'
    },
    {
      name: 'warning_threshold',
      label: t.warningThreshold,
      type: 'number',
      step: 0.01,
      placeholder: language === 'ar' ? 'أدخل عتبة التحذير' : 'Enter warning threshold'
    },
    {
      name: 'critical_threshold',
      label: t.criticalThreshold,
      type: 'number',
      step: 0.01,
      placeholder: language === 'ar' ? 'أدخل العتبة الحرجة' : 'Enter critical threshold'
    },
    {
      name: 'unit',
      label: t.unit,
      type: 'select',
      options: [
        { label: '%', value: '%' },
        { label: 'SAR', value: 'SAR' },
        { label: 'USD', value: 'USD' },
        { label: language === 'ar' ? 'ساعات' : 'Hours', value: 'hours' },
        { label: language === 'ar' ? 'أيام' : 'Days', value: 'days' },
        { label: '/5', value: '/5' },
        { label: '/10', value: '/10' },
        { label: language === 'ar' ? 'عدد' : 'Count', value: 'count' }
      ]
    },
    {
      name: 'frequency',
      label: t.frequency,
      type: 'select',
      required: true,
      options: [
        { label: t.daily, value: 'DAILY' },
        { label: t.weekly, value: 'WEEKLY' },
        { label: t.monthly, value: 'MONTHLY' },
        { label: t.quarterly, value: 'QUARTERLY' },
        { label: t.yearly, value: 'YEARLY' }
      ]
    },
    {
      name: 'is_higher_better',
      label: t.isHigherBetter,
      type: 'checkbox',
      defaultValue: true
    },
    {
      name: 'is_active',
      label: t.isActive,
      type: 'checkbox',
      defaultValue: true
    }
  ]

  // Handle modal close
  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  // Handle form submit
  const handleSubmit = async (data: Partial<KPIMetric>) => {
    try {
      console.log('🔧 KPI Management: Form submission started', { modalMode, data })

      // Validate required fields
      if (!data.name || !data.name_ar || !data.metric_type || !data.frequency) {
        throw new Error('Required fields are missing')
      }

      // Process numeric fields
      const processedData = {
        ...data,
        target_value: data.target_value ? Number(data.target_value) : null,
        warning_threshold: data.warning_threshold ? Number(data.warning_threshold) : null,
        critical_threshold: data.critical_threshold ? Number(data.critical_threshold) : null,
        is_active: data.is_active !== false, // Default to true
        is_higher_better: data.is_higher_better !== false // Default to true
      }

      if (modalMode === 'create') {
        console.log('📝 Creating new KPI with processed data:', processedData)
        const result = await createItem(processedData)
        console.log('✅ KPI created successfully:', result)
      } else if (modalMode === 'edit' && selectedItem) {
        console.log('✏️ Updating KPI:', selectedItem.id, 'with processed data:', processedData)
        const result = await updateItem(selectedItem.id, processedData)
        console.log('✅ KPI updated successfully:', result)
      }
      handleModalClose()
      console.log('🎉 KPI operation completed successfully')
    } catch (error) {
      console.error('❌ Error saving KPI:', error)
      // Error will be displayed by CrudModal, don't close modal
      throw error // Re-throw to let CrudModal handle the error display
    }
  }

  return (<div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t.title}</h1>
          <p className="text-gray-600 mt-2">{t.subtitle}</p>
        </div>
      </div>

      {/* CRUD Table */}
      <CrudTable
        title={t.title}
        data={kpis}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addKPI}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        mode={modalMode}
        title={
          modalMode === 'create' ? t.addKPI :
          modalMode === 'edit' ? t.editKPI : t.viewKPI
        }
        fields={formFields}
        initialData={selectedItem}
        onSave={handleSubmit}
        loading={creating || updating}
        language={language}
      />
    </div>
  )
}
