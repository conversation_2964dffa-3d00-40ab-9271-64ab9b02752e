/**
 * Employee Performance Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  Award,
  Target,
  BarChart3,
  Eye,
  Edit,
  Trash2,
  Star,
  Calendar,
  User,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { performanceService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeePerformanceProps {
  language: 'ar' | 'en'
}

interface PerformanceReview {
  id: number
  reviewPeriod: string
  reviewPeriodAr: string
  reviewDate: string
  reviewer: string
  reviewerAr: string
  overallScore: number
  goals: string
  goalsAr: string
  achievements: string
  achievementsAr: string
  areasForImprovement: string
  areasForImprovementAr: string
  status: 'draft' | 'submitted' | 'approved' | 'completed'
  nextReviewDate?: string
  comments?: string
  commentsAr?: string
}

const translations = {
  ar: {
    employeePerformance: 'تقييم الأداء - الموظف',
    addReview: 'إضافة تقييم',
    editReview: 'تعديل التقييم',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقييم؟',
    searchPlaceholder: 'البحث في تقييمات الأداء...',
    reviewPeriod: 'فترة التقييم',
    reviewDate: 'تاريخ التقييم',
    reviewer: 'المقيم',
    overallScore: 'النتيجة الإجمالية',
    goals: 'الأهداف',
    achievements: 'الإنجازات',
    areasForImprovement: 'مجالات التحسين',
    status: 'الحالة',
    nextReviewDate: 'تاريخ التقييم التالي',
    comments: 'التعليقات',
    draft: 'مسودة',
    submitted: 'مُرسل',
    approved: 'موافق عليه',
    completed: 'مكتمل',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    needsImprovement: 'يحتاج تحسين'
  },
  en: {
    employeePerformance: 'Performance Reviews - Employee',
    addReview: 'Add Review',
    editReview: 'Edit Review',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this performance review?',
    searchPlaceholder: 'Search performance reviews...',
    reviewPeriod: 'Review Period',
    reviewDate: 'Review Date',
    reviewer: 'Reviewer',
    overallScore: 'Overall Score',
    goals: 'Goals',
    achievements: 'Achievements',
    areasForImprovement: 'Areas for Improvement',
    status: 'Status',
    nextReviewDate: 'Next Review Date',
    comments: 'Comments',
    draft: 'Draft',
    submitted: 'Submitted',
    approved: 'Approved',
    completed: 'Completed',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    needsImprovement: 'Needs Improvement'
  }
}
export default function EmployeePerformance({ language }: EmployeePerformanceProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: reviews,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<PerformanceReview>({
    service: performanceService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'approved':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'submitted':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-3 w-3" />
      case 'approved':
        return <Award className="h-3 w-3" />
      case 'submitted':
        return <TrendingUp className="h-3 w-3" />
      case 'draft':
        return <Edit className="h-3 w-3" />
      default:
        return <Edit className="h-3 w-3" />
    }
  }

  const getScoreColor = (score: number): void => {
    if (score >= 90) return 'text-green-400'
    if (score >= 80) return 'text-blue-400'
    if (score >= 70) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getScoreLabel = (score: number): void => {
    if (score >= 90) return t.excellent
    if (score >= 80) return t.good
    if (score >= 70) return t.average
    return t.needsImprovement
  }

  // Table columns configuration
  const columns: TableColumn<PerformanceReview>[] = [
    {
      key: 'reviewPeriod',
      label: t.reviewPeriod,
      sortable: true,
      render: (item: PerformanceReview => (<div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            <Calendar className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.reviewPeriodAr : item.reviewPeriod}
            </div>
            <div className="text-sm text-white/60">
              {item.reviewDate}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'overallScore',
      label: t.overallScore,
      sortable: true,
      render: (item: PerformanceReview => (<div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Star className={`h-4 w-4 ${getScoreColor(item.overallScore)}`} />
            <span className={`text-lg font-bold ${getScoreColor(item.overallScore)}`}>
              {item.overallScore}%
            </span>
          </div>
          <div className="text-xs text-white/60">
            {getScoreLabel(item.overallScore)}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: PerformanceReview => (<div className="flex items-center gap-1">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'reviewer',
      label: t.reviewer,
      render: (item: PerformanceReview => (<div className="flex items-center gap-1">
          <User className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.reviewerAr : item.reviewer}
          </span>
        </div>
      )
    },
    {
      key: 'nextReviewDate',
      label: t.nextReviewDate,
      sortable: true,
      render: (item: PerformanceReview => (<div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-green-400" />
          <span className="text-white/80">
            {item.nextReviewDate || '-'}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<PerformanceReview>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: PerformanceReview => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: PerformanceReview => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: PerformanceReview => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t.submitted, value: 'submitted' },
        { label: t.approved, value: 'approved' },
        { label: t.completed, value: 'completed' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'reviewPeriod',
      label: t.reviewPeriod,
      type: 'text',
      required: true
    },
    {
      name: 'reviewPeriodAr',
      label: t.reviewPeriod + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'reviewDate',
      label: t.reviewDate,
      type: 'date',
      required: true
    },
    {
      name: 'reviewer',
      label: t.reviewer,
      type: 'text',
      required: true
    },
    {
      name: 'reviewerAr',
      label: t.reviewer + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'overallScore',
      label: t.overallScore,
      type: 'number',
      required: true,
      min: 0,
      max: 100
    },
    {
      name: 'goals',
      label: t.goals,
      type: 'textarea',
      required: true
    },
    {
      name: 'goalsAr',
      label: t.goals + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'achievements',
      label: t.achievements,
      type: 'textarea',
      required: true
    },
    {
      name: 'achievementsAr',
      label: t.achievements + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'areasForImprovement',
      label: t.areasForImprovement,
      type: 'textarea'
    },
    {
      name: 'areasForImprovementAr',
      label: t.areasForImprovement + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t.submitted, value: 'submitted' },
        { label: t.approved, value: 'approved' },
        { label: t.completed, value: 'completed' }
      ]
    },
    {
      name: 'nextReviewDate',
      label: t.nextReviewDate,
      type: 'date'
    },
    {
      name: 'comments',
      label: t.comments,
      type: 'textarea'
    },
    {
      name: 'commentsAr',
      label: t.comments + ' (عربي)',
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<PerformanceReview>) => {
    try {
      if (modalMode === 'create') {
        await createItem({
          ...data,
          reviewDate: new Date().toISOString().split('T')[0],
          status: 'draft'
        })
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (<div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.employeePerformance}
        data={reviews}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addReview : modalMode === 'edit' ? t.editReview : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
