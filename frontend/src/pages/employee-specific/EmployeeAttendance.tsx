/**
 * Employee Attendance Page - Check In/Out and View History
 * Employees can check in/out and view their attendance history
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  User,
  MapPin,
  Timer,
  TrendingUp,
  LogIn,
  LogOut,
  History,
  BarChart3
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { attendanceService } from '@/services/crudService'

interface EmployeeAttendanceProps {
  language: 'ar' | 'en'
}

interface AttendanceRecord {
  id: number
  date: string
  checkIn: string
  checkOut?: string
  totalHours?: number
  status: 'present' | 'absent' | 'late' | 'halfDay'
  location: string
  locationAr: string
  notes?: string
  notesAr?: string
  isLate: boolean
  lateMinutes?: number
  overtimeHours?: number
}

interface TodayAttendance {
  hasCheckedIn: boolean
  hasCheckedOut: boolean
  checkInTime?: string
  checkOutTime?: string
  totalHours?: number
  status: 'not_started' | 'checked_in' | 'checked_out'
}

const translations = {
  ar: {
    employeeAttendance: 'الحضور والانصراف',
    todayAttendance: 'حضور اليوم',
    attendanceHistory: 'سجل الحضور',
    checkInNow: 'تسجيل الدخول',
    checkOutNow: 'تسجيل الخروج',
    alreadyCheckedIn: 'تم تسجيل الدخول بالفعل',
    alreadyCheckedOut: 'تم تسجيل الخروج بالفعل',
    checkInSuccess: 'تم تسجيل الدخول بنجاح',
    checkOutSuccess: 'تم تسجيل الخروج بنجاح',
    currentTime: 'الوقت الحالي',
    workingHours: 'ساعات العمل',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    view: 'عرض',
    date: 'التاريخ',
    checkIn: 'وقت الدخول',
    checkOut: 'وقت الخروج',
    totalHours: 'إجمالي الساعات',
    status: 'الحالة',
    location: 'الموقع',
    present: 'حاضر',
    absent: 'غائب',
    late: 'متأخر',
    halfDay: 'نصف يوم',
    notStarted: 'لم يبدأ',
    checkedIn: 'مسجل دخول',
    checkedOut: 'مسجل خروج',
    hours: 'ساعات',
    minutes: 'دقائق'
  },
  en: {
    employeeAttendance: 'Attendance & Time Tracking',
    todayAttendance: "Today's Attendance",
    attendanceHistory: 'Attendance History',
    checkInNow: 'Check In',
    checkOutNow: 'Check Out',
    alreadyCheckedIn: 'Already checked in',
    alreadyCheckedOut: 'Already checked out',
    checkInSuccess: 'Checked in successfully',
    checkOutSuccess: 'Checked out successfully',
    currentTime: 'Current Time',
    workingHours: 'Working Hours',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    view: 'View',
    date: 'Date',
    checkIn: 'Check In',
    checkOut: 'Check Out',
    totalHours: 'Total Hours',
    status: 'Status',
    location: 'Location',
    present: 'Present',
    absent: 'Absent',
    late: 'Late',
    halfDay: 'Half Day',
    notStarted: 'Not Started',
    checkedIn: 'Checked In',
    checkedOut: 'Checked Out',
    hours: 'hours',
    minutes: 'minutes'
  }
}
export default function EmployeeAttendance({ language }: EmployeeAttendanceProps): React.ReactElement {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [todayAttendance, setTodayAttendance] = useState<TodayAttendance>({
    hasCheckedIn: false,
    hasCheckedOut: false,
    status: 'not_started'
  })
  const [showHistory, setShowHistory] = useState(false)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook for attendance history
  const {
    items: attendanceRecords,
    loading,
    error,
    createItem,
    refresh,
    exportData,
    clearError
  } = useCrud<AttendanceRecord>({
    service: attendanceService,
    autoLoad: true,
    pageSize: 20
  })

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Load today's attendance status
  useEffect(() => {
    loadTodayAttendance()
  }, [])

  const loadTodayAttendance = async () => {
    try {
      const today = new Date().toISOString().split('T')[0]
      const todayRecord = (attendanceRecords).find(record => (record).date === today)

      if (todayRecord) {
        setTodayAttendance({
          hasCheckedIn: !!(todayRecord).checkIn,
          hasCheckedOut: !!(todayRecord).checkOut,
          checkInTime: (todayRecord).checkIn,
          checkOutTime: (todayRecord).checkOut,
          totalHours: (todayRecord).totalHours,
          status: (todayRecord).checkOut ? 'checked_out' : 'checked_in'
        })
      }
    } catch (error) {
      console.error('Error loading today attendance:', error)
    }
  }

  // Check-in/Check-out functions
  const handleCheckIn = async () => {
    try {
      const now = new Date()
      const timeString = (now).toTimeString().split(' ')[0].substring(0, 5) // HH:MM format
      const today = (now).toISOString().split('T')[0]

      const attendanceData = {
        date: today,
        checkIn: timeString,
        status: 'present',
        location: 'Office',
        locationAr: 'المكتب',
        isLate: false,
        lateMinutes: 0
      }

      await createItem(attendanceData)

      setTodayAttendance({
        hasCheckedIn: true,
        hasCheckedOut: false,
        checkInTime: timeString,
        status: 'checked_in'
      })

      alert((t).checkInSuccess)
    } catch (error) {
      console.error('Check-in error:', error)
      alert('Check-in failed. Please try again.')
    }
  }

  const handleCheckOut = async () => {
    try {
      const now = new Date()
      const timeString = (now).toTimeString().split(' ')[0].substring(0, 5) // HH:MM format
      const today = (now).toISOString().split('T')[0]

      // Find today's record
      const todayRecord = (attendanceRecords).find(record => (record).date === today)

      if (todayRecord && (todayAttendance).checkInTime) {
        const totalHours = calculateTotalHours((todayAttendance).checkInTime, timeString)

        const updatedData = {
          ...todayRecord,
          checkOut: timeString,
          totalHours: totalHours
        }

        // Note: This would typically be an update operation
        // For now, we'll create a new record with checkout info
        await createItem(updatedData)

        setTodayAttendance({
          ...todayAttendance,
          hasCheckedOut: true,
          checkOutTime: timeString,
          totalHours: totalHours,
          status: 'checked_out'
        })

        alert((t).checkOutSuccess)
      }
    } catch (error) {
      console.error('Check-out error:', error)
      alert('Check-out failed. Please try again.')
    }
  }

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'halfDay':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }



  const calculateTotalHours = (checkIn: string, checkOut: string): void => {
    if (!checkIn || !checkOut) return 0
    const start = new Date(`2000-01-01T${checkIn}`)
    const end = new Date(`2000-01-01T${checkOut}`)
    const diffMs = (end).getTime() - (start).getTime()
    return (Math).round((diffMs / (1000 * 60 * 60)) * 100) / 100
  }

  const formatTime = (timeString?: string): string => {
    if (!timeString) return '-'
    return timeString
  }

  const formatDuration = (hours?: number): string => {
    if (!hours) return '-'
    const h = (Math).floor(hours)
    const m = (Math).round((hours - h) * 60)
    return `${h}${(t).hours} ${m}${(t).minutes}`
  }

  return (<div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">{(t).employeeAttendance}</h1>
        <div className="text-white/70">
          {(t).currentTime}: {(currentTime).toLocaleTimeString()}
        </div>
      </div>

      {/* Today's Attendance Card */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {(t).todayAttendance}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Display */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 glass-card border-white/10 rounded-lg">
              <div className="text-white/70 text-sm mb-1">{(t).status}</div>
              <Badge className={
                (todayAttendance).status === 'checked_out' ? 'bg-green-100 text-green-800' :
                (todayAttendance).status === 'checked_in' ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }>
                {t[(todayAttendance).status as keyof typeof t]}
              </Badge>
            </div>

            <div className="text-center p-4 glass-card border-white/10 rounded-lg">
              <div className="text-white/70 text-sm mb-1">{(t).checkIn}</div>
              <div className="text-white font-medium">
                {formatTime((todayAttendance).checkInTime)}
              </div>
            </div>

            <div className="text-center p-4 glass-card border-white/10 rounded-lg">
              <div className="text-white/70 text-sm mb-1">{(t).checkOut}</div>
              <div className="text-white font-medium">
                {formatTime((todayAttendance).checkOutTime)}
              </div>
            </div>
          </div>

          {/* Working Hours */}
          {(todayAttendance).totalHours && (<div className="text-center p-4 glass-card border-white/10 rounded-lg">
              <div className="text-white/70 text-sm mb-1">{(t).workingHours}</div>
              <div className="text-2xl font-bold text-blue-400">
                {formatDuration((todayAttendance).totalHours)}
              </div>
            </div>
          )}

          {/* Check In/Out Buttons */}
          <div className="flex justify-center gap-4">
            {!(todayAttendance).hasCheckedIn ? (<Button
                onClick={handleCheckIn}
                className="glass-button bg-green-500/20 hover:bg-green-500/30 border-green-500/50"
                size="lg"
              >
                <LogIn className="h-5 w-5 mr-2" />
                {(t).checkInNow}
              </Button>
            ) : !(todayAttendance).hasCheckedOut ? (<Button
                onClick={handleCheckOut}
                className="glass-button bg-red-500/20 hover:bg-red-500/30 border-red-500/50"
                size="lg"
              >
                <LogOut className="h-5 w-5 mr-2" />
                {(t).checkOutNow}
              </Button>
            ) : (<div className="text-center">
                <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-2" />
                <p className="text-white/70">{(t).alreadyCheckedOut}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Attendance History */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <History className="h-5 w-5" />
              {(t).attendanceHistory}
            </CardTitle>
            <Button
              onClick={() => setShowHistory(!showHistory)}
              variant="outline"
              className="glass-button"
            >
              <Eye className="h-4 w-4 mr-2" />
              {showHistory ? 'Hide' : (t).view}
            </Button>
          </div>
        </CardHeader>

        {showHistory && (<CardContent>
            <div className="space-y-3">
              {(attendanceRecords).slice(0, 10).map((record) => (<div key={(record).id} className="flex items-center justify-between p-3 glass-card border-white/10 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-blue-400" />
                    <div>
                      <div className="text-white font-medium">{(record).date}</div>
                      <div className="text-white/60 text-sm">
                        {language === 'ar' ? (record).locationAr : (record).location}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <div className="text-white/70 text-xs">{(t).checkIn}</div>
                      <div className="text-white text-sm">{formatTime((record).checkIn)}</div>
                    </div>

                    <div className="text-center">
                      <div className="text-white/70 text-xs">{(t).checkOut}</div>
                      <div className="text-white text-sm">{formatTime((record).checkOut)}</div>
                    </div>

                    <div className="text-center">
                      <div className="text-white/70 text-xs">{(t).totalHours}</div>
                      <div className="text-white text-sm">{formatDuration((record).totalHours)}</div>
                    </div>

                    <Badge className={getStatusColor((record).status)}>
                      {t[(record).status as keyof typeof t]}
                    </Badge>
                  </div>
                </div>
              ))}

              {(attendanceRecords).length === 0 && (
                <div className="text-center py-8 text-white/60">
                  No attendance records found
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  )
}
