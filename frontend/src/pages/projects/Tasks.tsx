import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  CheckSquare,
  Calendar,
  User,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  Flag,
  FolderOpen,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { taskService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface TasksProps {
  language: 'ar' | 'en'
}

interface Task {
  id: number
  project: number
  project_name?: string
  title: string
  title_ar?: string
  description: string
  description_ar?: string
  assigned_to?: number
  assigned_to_name?: string
  created_by: number
  created_by_name?: string
  due_date: string
  estimated_hours: number
  actual_hours: number
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED'
  priority: 'low' | 'medium' | 'high'
  completion_percentage: number
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    tasks: 'المهام',
    createTask: 'إنشاء مهمة',
    taskOverview: 'نظرة عامة على المهام',
    search: 'بحث',
    filter: 'تصفية',
    taskTitle: 'عنوان المهمة',
    project: 'المشروع',
    assignedTo: 'مكلف إلى',
    dueDate: 'تاريخ الاستحقاق',
    priority: 'الأولوية',
    status: 'الحالة',
    progress: 'التقدم',
    actions: 'الإجراءات',
    todo: 'للقيام',
    inProgress: 'قيد التنفيذ',
    review: 'مراجعة',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    urgent: 'عاجل',
    viewDetails: 'عرض التفاصيل',
    editTask: 'تعديل المهمة',
    totalTasks: 'إجمالي المهام',
    completedTasks: 'المهام المكتملة',
    inProgressTasks: 'المهام قيد التنفيذ',
    overdueTasks: 'المهام المتأخرة',
    estimatedHours: 'الساعات المقدرة',
    actualHours: 'الساعات الفعلية',
    description: 'الوصف',
    dependencies: 'التبعيات',
    tags: 'العلامات',
    createdBy: 'أنشأ بواسطة',
    startDate: 'تاريخ البداية',
    completionDate: 'تاريخ الإكمال',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه المهمة؟',
    addTask: 'إضافة مهمة'
  },
  en: {
    tasks: 'Tasks',
    createTask: 'Create Task',
    taskOverview: 'Task Overview',
    search: 'Search',
    filter: 'Filter',
    taskTitle: 'Task Title',
    project: 'Project',
    assignedTo: 'Assigned To',
    dueDate: 'Due Date',
    priority: 'Priority',
    status: 'Status',
    progress: 'Progress',
    actions: 'Actions',
    todo: 'To Do',
    inProgress: 'In Progress',
    review: 'Review',
    completed: 'Completed',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    viewDetails: 'View Details',
    editTask: 'Edit Task',
    totalTasks: 'Total Tasks',
    completedTasks: 'Completed Tasks',
    inProgressTasks: 'In Progress Tasks',
    overdueTasks: 'Overdue Tasks',
    estimatedHours: 'Estimated Hours',
    actualHours: 'Actual Hours',
    description: 'Description',
    dependencies: 'Dependencies',
    tags: 'Tags',
    createdBy: 'Created By',
    startDate: 'Start Date',
    completionDate: 'Completion Date',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this task?',
    addTask: 'Add Task'
  }
}

export default function Tasks({ language }: TasksProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: tasks,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Task>({
    service: taskService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'TODO':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'REVIEW':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): React.ReactElement => {
    switch (status) {
      case 'TODO':
        return <CheckSquare className="h-3 w-3" />
      case 'IN_PROGRESS':
        return <BarChart3 className="h-3 w-3" />
      case 'REVIEW':
        return <AlertTriangle className="h-3 w-3" />
      case 'COMPLETED':
        return <CheckCircle className="h-3 w-3" />
      case 'CANCELLED':
        return <XCircle className="h-3 w-3" />
      default:
        return <CheckSquare className="h-3 w-3" />
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'TODO': return t.todo
      case 'IN_PROGRESS': return t.inProgress
      case 'REVIEW': return t.review
      case 'COMPLETED': return t.completed
      case 'CANCELLED': return t.cancelled
      default: return status
    }
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getPriorityIcon = (priority: string): React.ReactElement => {
    return <Flag className={`h-3 w-3 ${getPriorityColor(priority)}`} />
  }

  // Table columns configuration
  const columns: TableColumn<Task>[] = [
    {
      key: 'title',
      label: t.taskTitle,
      sortable: true,
      render: (item: Task => (
        <div className="flex items-center gap-2">
          <CheckSquare className="h-4 w-4 text-blue-400" />
          <div>
            <span className="text-white font-medium">{item.title}</span>
            {item.project_name && (
              <div className="text-white/60 text-sm flex items-center gap-1">
                <FolderOpen className="h-3 w-3" />
                {item.project_name}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'assigned_to_name',
      label: t.assignedTo,
      render: (item: Task => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.assigned_to_name || 'Unassigned'}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Task => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{getStatusText(item.status)}</span>
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: Task => (
        <div className="flex items-center gap-1">
          {getPriorityIcon(item.priority)}
          <span className={`font-medium ${getPriorityColor(item.priority)}`}>
            {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}
          </span>
        </div>
      )
    },
    {
      key: 'due_date',
      label: t.dueDate,
      sortable: true,
      render: (item: Task => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.due_date}</span>
        </div>
      )
    },
    {
      key: 'completion_percentage',
      label: t.progress,
      sortable: true,
      render: (item: Task => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
              style={{ width: `${item.completion_percentage}%` }}
            ></div>
          </div>
          <span className="text-white/80 text-sm">{item.completion_percentage}%</span>
        </div>
      )
    },
    {
      key: 'estimated_hours',
      label: t.estimatedHours,
      render: (item: Task => (
        <div className="text-right">
          <div className="text-blue-400 font-medium">{item.estimated_hours}h</div>
          {item.actual_hours > 0 && (
            <div className="text-white/60 text-sm">{item.actual_hours}h actual</div>
          )}
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Task>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Task => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Task => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Task => {
        if (confirm(t.confirmDelete)) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'project',
      label: t.project,
      type: 'number',
      required: true,
      placeholder: 'Project ID'
    },
    {
      name: 'title',
      label: t.taskTitle,
      type: 'text',
      required: true
    },
    {
      name: 'title_ar',
      label: t.taskTitle + ' (عربي)',
      type: 'text'
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'description_ar',
      label: t.description + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'assigned_to',
      label: t.assignedTo,
      type: 'number',
      placeholder: 'Employee ID'
    },
    {
      name: 'created_by',
      label: t.createdBy,
      type: 'number',
      required: true,
      placeholder: 'Creator Employee ID'
    },
    {
      name: 'due_date',
      label: t.dueDate,
      type: 'date',
      required: true
    },
    {
      name: 'estimated_hours',
      label: t.estimatedHours,
      type: 'number',
      min: 0,
      step: 0.25
    },
    {
      name: 'actual_hours',
      label: t.actualHours,
      type: 'number',
      min: 0,
      step: 0.25
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.todo, value: 'TODO' },
        { label: t.inProgress, value: 'IN_PROGRESS' },
        { label: t.review, value: 'REVIEW' },
        { label: t.completed, value: 'COMPLETED' },
        { label: t.cancelled, value: 'CANCELLED' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    },
    {
      name: 'completion_percentage',
      label: t.progress,
      type: 'number',
      min: 0,
      max: 100
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Task>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.tasks}
        data={tasks}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addTask}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addTask : modalMode === 'edit' ? t.editTask : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
