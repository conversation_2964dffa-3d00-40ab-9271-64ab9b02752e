import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  FolderOpen,
  Calendar,
  Users,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  DollarSign,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { projectService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ProjectsProps {
  language: 'ar' | 'en'
}

interface Project {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar?: string
  project_manager: number
  project_manager_name?: string
  department?: number
  department_name?: string
  client?: string
  budget: number
  actual_cost: number
  start_date: string
  end_date: string
  status: 'PLANNING' | 'IN_PROGRESS' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority: 'low' | 'medium' | 'high'
  progress_percentage: number
  is_active: boolean
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    projects: 'المشاريع',
    createProject: 'إنشاء مشروع',
    projectOverview: 'نظرة عامة على المشاريع',
    search: 'بحث',
    filter: 'تصفية',
    projectName: 'اسم المشروع',
    projectManager: 'مدير المشروع',
    department: 'القسم',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    progress: 'التقدم',
    status: 'الحالة',
    budget: 'الميزانية',
    teamMembers: 'أعضاء الفريق',
    actions: 'الإجراءات',
    planning: 'التخطيط',
    inProgress: 'قيد التنفيذ',
    onHold: 'معلق',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    viewDetails: 'عرض التفاصيل',
    editProject: 'تعديل المشروع',
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    overdueTasks: 'المهام المتأخرة',
    client: 'العميل',
    priority: 'الأولوية',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    critical: 'حرجة',
    tasks: 'المهام',
    daysLeft: 'أيام متبقية',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المشروع؟',
    addProject: 'إضافة مشروع',
    description: 'الوصف',
    actualCost: 'التكلفة الفعلية'
  },
  en: {
    projects: 'Projects',
    createProject: 'Create Project',
    projectOverview: 'Project Overview',
    search: 'Search',
    filter: 'Filter',
    projectName: 'Project Name',
    projectManager: 'Project Manager',
    department: 'Department',
    startDate: 'Start Date',
    endDate: 'End Date',
    progress: 'Progress',
    status: 'Status',
    budget: 'Budget',
    teamMembers: 'Team Members',
    actions: 'Actions',
    planning: 'Planning',
    inProgress: 'In Progress',
    onHold: 'On Hold',
    completed: 'Completed',
    cancelled: 'Cancelled',
    viewDetails: 'View Details',
    editProject: 'Edit Project',
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    overdueTasks: 'Overdue Tasks',
    client: 'Client',
    priority: 'Priority',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    tasks: 'Tasks',
    daysLeft: 'Days Left',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this project?',
    addProject: 'Add Project',
    description: 'Description',
    actualCost: 'Actual Cost'
  }
}

export default function Projects({ language }: ProjectsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: projects,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Project>({
    service: projectService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'PLANNING':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'IN_PROGRESS':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'ON_HOLD':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): React.ReactElement => {
    switch (status) {
      case 'PLANNING':
        return <Target className="h-3 w-3" />
      case 'IN_PROGRESS':
        return <TrendingUp className="h-3 w-3" />
      case 'ON_HOLD':
        return <Clock className="h-3 w-3" />
      case 'COMPLETED':
        return <CheckCircle className="h-3 w-3" />
      case 'CANCELLED':
        return <AlertTriangle className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'PLANNING': return t.planning
      case 'IN_PROGRESS': return t.inProgress
      case 'ON_HOLD': return t.onHold
      case 'COMPLETED': return t.completed
      case 'CANCELLED': return t.cancelled
      default: return status
    }
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<Project>[] = [
    {
      key: 'name',
      label: t.projectName,
      sortable: true,
      render: (item: Project => (
        <div className="flex items-center gap-2">
          <FolderOpen className="h-4 w-4 text-blue-400" />
          <div>
            <span className="text-white font-medium">{item.name}</span>
            {item.client && (
              <div className="text-white/60 text-sm">{item.client}</div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'project_manager_name',
      label: t.projectManager,
      render: (item: Project => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.project_manager_name || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'department_name',
      label: t.department,
      render: (item: Project => (
        <span className="text-white/80">{item.department_name || 'N/A'}</span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Project => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{getStatusText(item.status)}</span>
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      render: (item: Project => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}
        </span>
      )
    },
    {
      key: 'progress_percentage',
      label: t.progress,
      sortable: true,
      render: (item: Project => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
              style={{ width: `${item.progress_percentage}%` }}
            ></div>
          </div>
          <span className="text-white/80 text-sm">{item.progress_percentage}%</span>
        </div>
      )
    },
    {
      key: 'budget',
      label: t.budget,
      sortable: true,
      render: (item: Project => (
        <div className="text-right">
          <div className="text-green-400 font-medium">{formatCurrency(item.budget)}</div>
          {item.actual_cost > 0 && (
            <div className="text-white/60 text-sm">{formatCurrency(item.actual_cost)} spent</div>
          )}
        </div>
      )
    },
    {
      key: 'end_date',
      label: t.endDate,
      sortable: true,
      render: (item: Project => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.end_date}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Project>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Project => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Project => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Project => {
        if (confirm(t.confirmDelete)) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.projectName,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: t.projectName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'description_ar',
      label: t.description + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'project_manager',
      label: t.projectManager,
      type: 'number',
      required: true,
      placeholder: 'Project Manager ID'
    },
    {
      name: 'department',
      label: t.department,
      type: 'number',
      placeholder: 'Department ID'
    },
    {
      name: 'client',
      label: t.client,
      type: 'text'
    },
    {
      name: 'budget',
      label: t.budget,
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'actual_cost',
      label: t.actualCost,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'start_date',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'end_date',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.planning, value: 'PLANNING' },
        { label: t.inProgress, value: 'IN_PROGRESS' },
        { label: t.onHold, value: 'ON_HOLD' },
        { label: t.completed, value: 'COMPLETED' },
        { label: t.cancelled, value: 'CANCELLED' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    },
    {
      name: 'progress_percentage',
      label: t.progress,
      type: 'number',
      min: 0,
      max: 100
    },
    {
      name: 'is_active',
      label: 'Active',
      type: 'checkbox'
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Project>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.projects}
        data={projects}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addProject}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addProject : modalMode === 'edit' ? t.editProject : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
