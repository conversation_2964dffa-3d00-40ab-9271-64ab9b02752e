import { useSelector } from 'react-redux'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { usePermissions } from '../components/RoleBasedRoute'
import type { RootState } from '../store'
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  User, 
  Users, 
  Building, 
  DollarSign, 
  Briefcase, 
  Package, 
  BarChart3, 
  Settings,
  MessageSquare
} from 'lucide-react'

interface AccessTestProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'اختبار صلاحيات الوصول',
    subtitle: 'اختبار الصلاحيات لكل دور مستخدم',
    currentUser: 'المستخدم الحالي',
    role: 'الدور',
    permissions: 'الصلاحيات',
    accessTest: 'اختبار الوصول',
    allowed: 'مسموح',
    denied: 'مرفوض',
    modules: 'الوحدات',
    employees: 'الموظفين',
    departments: 'الأقسام',
    hr: 'الموارد البشرية',
    finance: 'المالية',
    projects: 'المشاريع',
    assets: 'الأصول',
    analytics: 'التحليلات',
    reports: 'التقارير',
    settings: 'الإعدادات',
    communication: 'التواصل'
  },
  en: {
    title: 'Access Permissions Test',
    subtitle: 'Test permissions for each user role',
    currentUser: 'Current User',
    role: 'Role',
    permissions: 'Permissions',
    accessTest: 'Access Test',
    allowed: 'Allowed',
    denied: 'Denied',
    modules: 'Modules',
    employees: 'Employees',
    departments: 'Departments',
    hr: 'HR Management',
    finance: 'Finance',
    projects: 'Projects',
    assets: 'Assets',
    analytics: 'Analytics',
    reports: 'Reports',
    settings: 'Settings',
    communication: 'Communication'
  }
}

export default function AccessTest({ language }: AccessTestProps) {
  const { user } = useSelector((state: RootState => state.auth)
  const { hasPermission, canAccess } = usePermissions()
  const t = translations[language]
  const isRTL = language === 'ar'

  const moduleTests = [
    {
      name: t.employees,
      icon: Users,
      permission: 'employees.read',
      route: '/employees',
      description: 'Employee management access'
    },
    {
      name: t.departments,
      icon: Building,
      permission: 'departments.read',
      route: '/departments',
      description: 'Department management access'
    },
    {
      name: t.hr,
      icon: User,
      permission: 'hr.read',
      route: '/hr/leave',
      description: 'HR management access'
    },
    {
      name: t.finance,
      icon: DollarSign,
      permission: 'finance.read',
      route: '/finance/budgets',
      description: 'Financial management access'
    },
    {
      name: t.projects,
      icon: Briefcase,
      permission: 'projects.read',
      route: '/projects',
      description: 'Project management access'
    },
    {
      name: t.assets,
      icon: Package,
      permission: 'assets.read',
      route: '/assets',
      description: 'Asset management access'
    },
    {
      name: t.analytics,
      icon: BarChart3,
      permission: 'analytics.read',
      route: '/analytics',
      description: 'Analytics access'
    },
    {
      name: t.reports,
      icon: BarChart3,
      permission: 'reports.read',
      route: '/reports',
      description: 'Reports access'
    },
    {
      name: t.settings,
      icon: Settings,
      permission: 'settings.read',
      route: '/settings',
      description: 'Settings access (Super Admin only)'
    }
  ]

  const getAccessStatus = (permission: string) => {
    return hasPermission(permission)
  }

  const getStatusIcon = (hasAccess: boolean) => {
    return hasAccess ? (
      <CheckCircle className="h-5 w-5 text-green-400" />
    ) : (
      <XCircle className="h-5 w-5 text-red-400" />
    )
  }

  const getStatusText = (hasAccess: boolean) => {
    return hasAccess ? t.allowed : t.denied
  }

  const getStatusColor = (hasAccess: boolean) => {
    return hasAccess ? 'text-green-400' : 'text-red-400'
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Shield className="h-8 w-8 text-blue-400" />
          <h1 className="text-3xl font-bold text-white">{t.title}</h1>
        </div>
        <p className="text-white/70 text-lg">{t.subtitle}</p>
      </div>

      {/* Current User Info */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <User className="h-5 w-5" />
            {t.currentUser}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold text-xl mx-auto mb-3">
                {user?.avatar || user?.name?.charAt(0)}
              </div>
              <h3 className="text-white font-semibold text-lg">{user?.name}</h3>
              <p className="text-white/60">{user?.email}</p>
            </div>
            
            <div className="text-center">
              <h4 className="text-white font-medium mb-2">{t.role}</h4>
              <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-500/20 text-blue-400 font-medium">
                {language === 'ar' ? user?.role.nameAr : user?.role.name}
              </div>
              <p className="text-white/60 text-sm mt-2">Level: {user?.role.level}</p>
            </div>
            
            <div className="text-center">
              <h4 className="text-white font-medium mb-2">{t.permissions}</h4>
              <div className="text-white/80 text-sm">
                {user?.permissions.length} modules
              </div>
              <p className="text-white/60 text-xs mt-1">
                {user?.permissions.map(p => p.module).join(', ')}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Access Test Results */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {t.accessTest}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {moduleTests.map((module, index) => {
              const hasAccess = getAccessStatus(module.permission)
              
              return (
                <div
                  key={index}
                  className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all ${
                    hasAccess ? 'border-l-4 border-l-green-500' : 'border-l-4 border-l-red-500'
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        hasAccess ? 'bg-green-500/20' : 'bg-red-500/20'
                      }`}>
                        <module.icon className={`h-5 w-5 ${
                          hasAccess ? 'text-green-400' : 'text-red-400'
                        }`} />
                      </div>
                      <div>
                        <h4 className="text-white font-medium">{module.name}</h4>
                        <p className="text-white/60 text-xs">{module.route}</p>
                      </div>
                    </div>
                    {getStatusIcon(hasAccess)}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className={`text-sm font-medium ${getStatusColor(hasAccess)}`}>
                      {getStatusText(hasAccess)}
                    </span>
                    <span className="text-xs text-white/60">
                      {module.permission}
                    </span>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Role-Specific Summary */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">
            {language === 'ar' ? 'ملخص الصلاحيات' : 'Permissions Summary'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-green-400 font-medium mb-3 flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                {language === 'ar' ? 'الوحدات المسموحة' : 'Allowed Modules'}
              </h4>
              <div className="space-y-2">
                {moduleTests
                  .filter(module => getAccessStatus(module.permission))
                  .map((module, index) => (
                    <div key={index} className="flex items-center gap-2 text-white/80 text-sm">
                      <module.icon className="h-4 w-4 text-green-400" />
                      {module.name}
                    </div>
                  ))}
              </div>
            </div>
            
            <div>
              <h4 className="text-red-400 font-medium mb-3 flex items-center gap-2">
                <XCircle className="h-4 w-4" />
                {language === 'ar' ? 'الوحدات المحظورة' : 'Restricted Modules'}
              </h4>
              <div className="space-y-2">
                {moduleTests
                  .filter(module => !getAccessStatus(module.permission))
                  .map((module, index) => (
                    <div key={index} className="flex items-center gap-2 text-white/60 text-sm">
                      <module.icon className="h-4 w-4 text-red-400" />
                      {module.name}
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Navigation */}
      <div className="text-center">
        <p className="text-white/70 mb-4">
          {language === 'ar' 
            ? 'جرب الوصول إلى الصفحات المختلفة لاختبار نظام الصلاحيات'
            : 'Try accessing different pages to test the permission system'
          }
        </p>
        <Button
          onClick={() => window.history.back()}
          className="glass-button"
        >
          {language === 'ar' ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
        </Button>
      </div>
    </div>
  )
}
