import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  TrendingUp,
  Star,
  Target,
  Award,
  User,
  Calendar,
  BarChart3,
  CheckCircle,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { performanceService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface PerformanceProps {
  language: 'ar' | 'en'
}

interface PerformanceReview {
  id: number
  employee: number
  employee_name?: string
  reviewer: number
  reviewer_name?: string
  review_period_start: string
  review_period_end: string
  review_type: 'annual' | 'quarterly' | 'probation' | 'project'
  overall_rating: number
  goals_achievement: number
  communication_skills: number
  teamwork: number
  leadership?: number
  technical_skills: number
  strengths: string
  areas_for_improvement: string
  goals_for_next_period: string
  reviewer_comments: string
  employee_comments?: string
  hr_comments?: string
  is_final: boolean
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    performance: 'تقييم الأداء',
    performanceReviews: 'مراجعات الأداء',
    goals: 'الأهداف',
    achievements: 'الإنجازات',
    createReview: 'إنشاء مراجعة',
    search: 'بحث',
    filter: 'تصفية',
    employee: 'الموظف',
    department: 'القسم',
    reviewPeriod: 'فترة المراجعة',
    overallRating: 'التقييم العام',
    status: 'الحالة',
    actions: 'الإجراءات',
    pending: 'معلق',
    completed: 'مكتمل',
    inProgress: 'قيد التنفيذ',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    needsImprovement: 'يحتاج تحسين',
    viewDetails: 'عرض التفاصيل',
    edit: 'تعديل',
    totalReviews: 'إجمالي المراجعات',
    completedReviews: 'المراجعات المكتملة',
    pendingReviews: 'المراجعات المعلقة',
    averageRating: 'متوسط التقييم',
    goalTitle: 'عنوان الهدف',
    progress: 'التقدم',
    dueDate: 'تاريخ الاستحقاق',
    achievementTitle: 'عنوان الإنجاز',
    achievementDate: 'تاريخ الإنجاز',
    view: 'عرض',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف مراجعة الأداء هذه؟',
    addReview: 'إضافة مراجعة أداء',
    editReview: 'تعديل مراجعة الأداء',
    reviewer: 'المراجع',
    reviewType: 'نوع المراجعة',
    annual: 'سنوي',
    quarterly: 'ربع سنوي',
    probation: 'فترة تجريبية',
    project: 'مشروع',
    goalsAchievement: 'تحقيق الأهداف',
    communicationSkills: 'مهارات التواصل',
    teamwork: 'العمل الجماعي',
    leadership: 'القيادة',
    technicalSkills: 'المهارات التقنية',
    strengths: 'نقاط القوة',
    areasForImprovement: 'مجالات التحسين',
    goalsForNextPeriod: 'أهداف الفترة القادمة',
    reviewerComments: 'تعليقات المراجع',
    employeeComments: 'تعليقات الموظف',
    hrComments: 'تعليقات الموارد البشرية',
    isFinal: 'نهائي',
    reviewPeriodStart: 'بداية فترة المراجعة',
    reviewPeriodEnd: 'نهاية فترة المراجعة'
  },
  en: {
    performance: 'Performance',
    performanceReviews: 'Performance Reviews',
    goals: 'Goals',
    achievements: 'Achievements',
    createReview: 'Create Review',
    search: 'Search',
    filter: 'Filter',
    employee: 'Employee',
    department: 'Department',
    reviewPeriod: 'Review Period',
    overallRating: 'Overall Rating',
    status: 'Status',
    actions: 'Actions',
    pending: 'Pending',
    completed: 'Completed',
    inProgress: 'In Progress',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    needsImprovement: 'Needs Improvement',
    viewDetails: 'View Details',
    edit: 'Edit',
    totalReviews: 'Total Reviews',
    completedReviews: 'Completed Reviews',
    pendingReviews: 'Pending Reviews',
    averageRating: 'Average Rating',
    goalTitle: 'Goal Title',
    progress: 'Progress',
    dueDate: 'Due Date',
    achievementTitle: 'Achievement Title',
    achievementDate: 'Achievement Date',
    view: 'View',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this performance review?',
    addReview: 'Add Performance Review',
    editReview: 'Edit Performance Review',
    reviewer: 'Reviewer',
    reviewType: 'Review Type',
    annual: 'Annual',
    quarterly: 'Quarterly',
    probation: 'Probation',
    project: 'Project',
    goalsAchievement: 'Goals Achievement',
    communicationSkills: 'Communication Skills',
    teamwork: 'Teamwork',
    leadership: 'Leadership',
    technicalSkills: 'Technical Skills',
    strengths: 'Strengths',
    areasForImprovement: 'Areas for Improvement',
    goalsForNextPeriod: 'Goals for Next Period',
    reviewerComments: 'Reviewer Comments',
    employeeComments: 'Employee Comments',
    hrComments: 'HR Comments',
    isFinal: 'Final',
    reviewPeriodStart: 'Review Period Start',
    reviewPeriodEnd: 'Review Period End'
  }
}

export default function Performance({ language }: PerformanceProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: performanceReviews,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<PerformanceReview>({
    service: performanceService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getRatingColor = (rating: number): string => {
    if (rating >= 5) return 'text-green-400'
    if (rating >= 4) return 'text-blue-400'
    if (rating >= 3) return 'text-yellow-400'
    if (rating >= 2) return 'text-orange-400'
    return 'text-red-400'
  }

  const getRatingText = (rating: number): string => {
    if (rating >= 5) return t.excellent
    if (rating >= 4) return t.good
    if (rating >= 3) return t.average
    if (rating >= 2) return t.needsImprovement
    return 'Poor'
  }

  const getReviewTypeText = (type: string): string => {
    switch (type) {
      case 'annual': return t.annual
      case 'quarterly': return t.quarterly
      case 'probation': return t.probation
      case 'project': return t.project
      default: return type
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ))
  }

  // Table columns configuration
  const columns: TableColumn<PerformanceReview>[] = [
    {
      key: 'employee_name',
      label: t.employee,
      sortable: true,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-white">{item.employee_name || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'reviewer_name',
      label: t.reviewer,
      render: (item: PerformanceReview) => (
        <span className="text-white/80">{item.reviewer_name || 'N/A'}</span>
      )
    },
    {
      key: 'review_type',
      label: t.reviewType,
      render: (item: PerformanceReview) => (
        <Badge variant="outline" className="text-white border-white/20">
          {getReviewTypeText(item.review_type)}
        </Badge>
      )
    },
    {
      key: 'review_period',
      label: t.reviewPeriod,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-purple-400" />
          <span className="text-white/80 text-sm">
            {item.review_period_start} - {item.review_period_end}
          </span>
        </div>
      )
    },
    {
      key: 'overall_rating',
      label: t.overallRating,
      sortable: true,
      render: (item: PerformanceReview) => (
        <div className="flex items-center gap-2">
          <div className="flex">{renderStars(item.overall_rating)}</div>
          <span className={`font-medium ${getRatingColor(item.overall_rating)}`}>
            {item.overall_rating}/5
          </span>
        </div>
      )
    },
    {
      key: 'is_final',
      label: t.status,
      render: (item: PerformanceReview) => (
        <Badge className={item.is_final ? 'bg-green-100 text-green-800 border-green-200' : 'bg-yellow-100 text-yellow-800 border-yellow-200'}>
          {item.is_final ? t.completed : t.pending}
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<PerformanceReview>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: PerformanceReview) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: PerformanceReview) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: PerformanceReview) => {
        if (confirm(t.confirmDelete)) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'employee',
      label: t.employee,
      type: 'number',
      required: true,
      placeholder: 'Employee ID'
    },
    {
      name: 'reviewer',
      label: t.reviewer,
      type: 'number',
      required: true,
      placeholder: 'Reviewer ID'
    },
    {
      name: 'review_period_start',
      label: t.reviewPeriodStart,
      type: 'date',
      required: true
    },
    {
      name: 'review_period_end',
      label: t.reviewPeriodEnd,
      type: 'date',
      required: true
    },
    {
      name: 'review_type',
      label: t.reviewType,
      type: 'select',
      required: true,
      options: [
        { label: t.annual, value: 'annual' },
        { label: t.quarterly, value: 'quarterly' },
        { label: t.probation, value: 'probation' },
        { label: t.project, value: 'project' }
      ]
    },
    {
      name: 'overall_rating',
      label: t.overallRating,
      type: 'number',
      required: true,
      min: 1,
      max: 5
    },
    {
      name: 'goals_achievement',
      label: t.goalsAchievement,
      type: 'number',
      required: true,
      min: 1,
      max: 5
    },
    {
      name: 'communication_skills',
      label: t.communicationSkills,
      type: 'number',
      required: true,
      min: 1,
      max: 5
    },
    {
      name: 'teamwork',
      label: t.teamwork,
      type: 'number',
      required: true,
      min: 1,
      max: 5
    },
    {
      name: 'leadership',
      label: t.leadership,
      type: 'number',
      min: 1,
      max: 5
    },
    {
      name: 'technical_skills',
      label: t.technicalSkills,
      type: 'number',
      required: true,
      min: 1,
      max: 5
    },
    {
      name: 'strengths',
      label: t.strengths,
      type: 'textarea',
      required: true
    },
    {
      name: 'areas_for_improvement',
      label: t.areasForImprovement,
      type: 'textarea',
      required: true
    },
    {
      name: 'goals_for_next_period',
      label: t.goalsForNextPeriod,
      type: 'textarea',
      required: true
    },
    {
      name: 'reviewer_comments',
      label: t.reviewerComments,
      type: 'textarea',
      required: true
    },
    {
      name: 'employee_comments',
      label: t.employeeComments,
      type: 'textarea'
    },
    {
      name: 'hr_comments',
      label: t.hrComments,
      type: 'textarea'
    },
    {
      name: 'is_final',
      label: t.isFinal,
      type: 'checkbox'
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<PerformanceReview>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.performance}
        data={performanceReviews}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addReview}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addReview : modalMode === 'edit' ? t.editReview : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
