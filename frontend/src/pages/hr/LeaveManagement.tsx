import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { leaveService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface LeaveManagementProps {
  language: 'ar' | 'en'
}

interface LeaveRequest {
  id: number
  employee: number
  employee_name?: string
  leave_type: number
  leave_type_name?: string
  start_date: string
  end_date: string
  days_requested: number
  reason: string
  reason_ar?: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED'
  approved_by?: number
  approved_by_name?: string
  approval_date?: string
  rejection_reason?: string
  rejection_reason_ar?: string
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    leaveManagement: 'إدارة الإجازات',
    requestLeave: 'طلب إجازة',
    leaveRequests: 'طلبات الإجازات',
    leaveBalance: 'رصيد الإجازات',
    leaveTypes: 'أنواع الإجازات',
    pendingRequests: 'الطلبات المعلقة',
    approvedRequests: 'الطلبات المعتمدة',
    rejectedRequests: 'الطلبات المرفوضة',
    search: 'بحث',
    filter: 'تصفية',
    addNew: 'إضافة جديد',
    employee: 'الموظف',
    leaveType: 'نوع الإجازة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    duration: 'المدة',
    status: 'الحالة',
    reason: 'السبب',
    actions: 'الإجراءات',
    approve: 'موافقة',
    reject: 'رفض',
    pending: 'معلق',
    approved: 'معتمد',
    rejected: 'مرفوض',
    days: 'أيام',
    annualLeave: 'إجازة سنوية',
    sickLeave: 'إجازة مرضية',
    personalLeave: 'إجازة شخصية',
    maternityLeave: 'إجازة أمومة',
    totalDays: 'إجمالي الأيام',
    usedDays: 'الأيام المستخدمة',
    remainingDays: 'الأيام المتبقية',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف طلب الإجازة هذا؟',
    editLeave: 'تعديل طلب الإجازة'
  },
  en: {
    leaveManagement: 'Leave Management',
    requestLeave: 'Request Leave',
    leaveRequests: 'Leave Requests',
    leaveBalance: 'Leave Balance',
    leaveTypes: 'Leave Types',
    pendingRequests: 'Pending Requests',
    approvedRequests: 'Approved Requests',
    rejectedRequests: 'Rejected Requests',
    search: 'Search',
    filter: 'Filter',
    addNew: 'Add New',
    employee: 'Employee',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    duration: 'Duration',
    status: 'Status',
    reason: 'Reason',
    actions: 'Actions',
    approve: 'Approve',
    reject: 'Reject',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    days: 'Days',
    annualLeave: 'Annual Leave',
    sickLeave: 'Sick Leave',
    personalLeave: 'Personal Leave',
    maternityLeave: 'Maternity Leave',
    totalDays: 'Total Days',
    usedDays: 'Used Days',
    remainingDays: 'Remaining Days',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this leave request?',
    editLeave: 'Edit Leave Request'
  }
}

export default function LeaveManagement({ language }: LeaveManagementProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: leaveRequests,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<LeaveRequest>({
    service: leaveService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'APPROVED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'CANCELLED':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): React.ReactElement => {
    switch (status) {
      case 'PENDING':
        return <Clock className="h-3 w-3" />
      case 'APPROVED':
        return <CheckCircle className="h-3 w-3" />
      case 'REJECTED':
        return <XCircle className="h-3 w-3" />
      case 'CANCELLED':
        return <AlertCircle className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<LeaveRequest>[] = [
    {
      key: 'employee_name',
      label: t.employee,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-white">{item.employee_name || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'leave_type_name',
      label: t.leaveType,
      render: (item: LeaveRequest) => (
        <span className="text-white/80">{item.leave_type_name || 'N/A'}</span>
      )
    },
    {
      key: 'start_date',
      label: t.startDate,
      sortable: true,
      render: (item: LeaveRequest) => (
        <span className="text-white/80">{item.start_date}</span>
      )
    },
    {
      key: 'end_date',
      label: t.endDate,
      sortable: true,
      render: (item: LeaveRequest) => (
        <span className="text-white/80">{item.end_date}</span>
      )
    },
    {
      key: 'days_requested',
      label: t.days,
      sortable: true,
      render: (item: LeaveRequest) => (
        <span className="text-white font-medium">{item.days_requested}</span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: LeaveRequest) => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{item.status}</span>
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<LeaveRequest>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: LeaveRequest) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: LeaveRequest) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: LeaveRequest) => {
        if (confirm(t.confirmDelete)) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'employee',
      label: t.employee,
      type: 'number',
      required: true,
      placeholder: 'Employee ID'
    },
    {
      name: 'leave_type',
      label: t.leaveType,
      type: 'number',
      required: true,
      placeholder: 'Leave Type ID'
    },
    {
      name: 'start_date',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'end_date',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'days_requested',
      label: t.days,
      type: 'number',
      required: true,
      min: 1
    },
    {
      name: 'reason',
      label: t.reason,
      type: 'textarea',
      required: true
    },
    {
      name: 'reason_ar',
      label: t.reason + ' (عربي)',
      type: 'textarea'
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<LeaveRequest>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.leaveManagement}
        data={leaveRequests}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.requestLeave}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.requestLeave : modalMode === 'edit' ? t.editLeave : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
