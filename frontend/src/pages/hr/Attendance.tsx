import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  CheckCircle,
  XCircle,
  Al<PERSON><PERSON>riangle,
  User,
  Timer,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { attendanceService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface AttendanceProps {
  language: 'ar' | 'en'
}

interface Attendance {
  id: number
  employee: number
  employee_name?: string
  date: string
  check_in?: string
  check_out?: string
  break_start?: string
  break_end?: string
  total_hours?: number
  overtime_hours: number
  is_present: boolean
  is_late: boolean
  notes?: string
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    attendance: 'الحضور والانصراف',
    todayAttendance: 'حضور اليوم',
    attendanceReport: 'تقرير الحضور',
    checkIn: 'تسجيل الحضور',
    checkOut: 'تسجيل الانصراف',
    present: 'حاضر',
    absent: 'غائب',
    late: 'متأخر',
    overtime: 'وقت إضافي',
    search: 'بحث',
    filter: 'تصفية',
    export: 'تصدير',
    employee: 'الموظف',
    date: 'التاريخ',
    checkInTime: 'وقت الحضور',
    checkOutTime: 'وقت الانصراف',
    totalHours: 'إجمالي الساعات',
    overtimeHours: 'ساعات إضافية',
    status: 'الحالة',
    department: 'القسم',
    workingHours: 'ساعات العمل',
    breakTime: 'وقت الاستراحة',
    totalEmployees: 'إجمالي الموظفين',
    presentToday: 'الحاضرون اليوم',
    absentToday: 'الغائبون اليوم',
    lateToday: 'المتأخرون اليوم',
    averageWorkingHours: 'متوسط ساعات العمل',
    thisMonth: 'هذا الشهر',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف سجل الحضور هذا؟',
    addAttendance: 'إضافة سجل حضور',
    editAttendance: 'تعديل سجل الحضور',
    notes: 'ملاحظات',
    breakStart: 'بداية الاستراحة',
    breakEnd: 'نهاية الاستراحة'
  },
  en: {
    attendance: 'Attendance',
    todayAttendance: "Today's Attendance",
    attendanceReport: 'Attendance Report',
    checkIn: 'Check In',
    checkOut: 'Check Out',
    present: 'Present',
    absent: 'Absent',
    late: 'Late',
    overtime: 'Overtime',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    employee: 'Employee',
    date: 'Date',
    checkInTime: 'Check In Time',
    checkOutTime: 'Check Out Time',
    totalHours: 'Total Hours',
    overtimeHours: 'Overtime Hours',
    status: 'Status',
    department: 'Department',
    workingHours: 'Working Hours',
    breakTime: 'Break Time',
    totalEmployees: 'Total Employees',
    presentToday: 'Present Today',
    absentToday: 'Absent Today',
    lateToday: 'Late Today',
    averageWorkingHours: 'Average Working Hours',
    thisMonth: 'This Month',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this attendance record?',
    addAttendance: 'Add Attendance',
    editAttendance: 'Edit Attendance',
    notes: 'Notes',
    breakStart: 'Break Start',
    breakEnd: 'Break End'
  }
}

export default function Attendance({ language }: AttendanceProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: attendanceRecords,
    selectedItem,
    loading,
    creating,
    updating,

    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Attendance>({
    service: attendanceService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (attendance: Attendance): string => {
    if (!attendance.is_present) {
      return 'bg-red-100 text-red-800 border-red-200'
    } else if (attendance.is_late) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    } else {
      return 'bg-green-100 text-green-800 border-green-200'
    }
  }

  const getStatusIcon = (attendance: Attendance): React.ReactElement => {
    if (!attendance.is_present) {
      return <XCircle className="h-3 w-3" />
    } else if (attendance.is_late) {
      return <AlertTriangle className="h-3 w-3" />
    } else {
      return <CheckCircle className="h-3 w-3" />
    }
  }

  const getStatusText = (attendance: Attendance): string => {
    if (!attendance.is_present) {
      return t.absent
    } else if (attendance.is_late) {
      return t.late
    } else {
      return t.present
    }
  }

  const formatTime = (timeString?: string): string => {
    if (!timeString) return '-'
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Table columns configuration
  const columns: TableColumn<Attendance>[] = [
    {
      key: 'employee_name',
      label: t.employee,
      sortable: true,
      render: (item: Attendance) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-white">{item.employee_name || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'date',
      label: t.date,
      sortable: true,
      render: (item: Attendance) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.date}</span>
        </div>
      )
    },
    {
      key: 'check_in',
      label: t.checkInTime,
      render: (item: Attendance) => (
        <span className="text-white/80">{formatTime(item.check_in)}</span>
      )
    },
    {
      key: 'check_out',
      label: t.checkOutTime,
      render: (item: Attendance) => (
        <span className="text-white/80">{formatTime(item.check_out)}</span>
      )
    },
    {
      key: 'total_hours',
      label: t.totalHours,
      sortable: true,
      render: (item: Attendance) => (
        <div className="flex items-center gap-1">
          <Timer className="h-3 w-3 text-green-400" />
          <span className="text-white font-medium">{item.total_hours?.toFixed(2) || '0.00'}h</span>
        </div>
      )
    },
    {
      key: 'overtime_hours',
      label: t.overtimeHours,
      sortable: true,
      render: (item: Attendance) => (
        <span className="text-orange-400 font-medium">{item.overtime_hours.toFixed(2)}h</span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Attendance) => (
        <Badge className={getStatusColor(item)}>
          {getStatusIcon(item)}
          <span className="ml-1">{getStatusText(item)}</span>
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Attendance>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Attendance) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Attendance) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Attendance) => {
        if (confirm(t.confirmDelete)) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'employee',
      label: t.employee,
      type: 'number',
      required: true,
      placeholder: 'Employee ID'
    },
    {
      name: 'date',
      label: t.date,
      type: 'date',
      required: true
    },
    {
      name: 'check_in',
      label: t.checkInTime,
      type: 'text',
      required: false,
      placeholder: 'HH:MM:SS'
    },
    {
      name: 'check_out',
      label: t.checkOutTime,
      type: 'text',
      required: false,
      placeholder: 'HH:MM:SS'
    },
    {
      name: 'break_start',
      label: t.breakStart,
      type: 'text',
      required: false,
      placeholder: 'HH:MM:SS'
    },
    {
      name: 'break_end',
      label: t.breakEnd,
      type: 'text',
      required: false,
      placeholder: 'HH:MM:SS'
    },
    {
      name: 'overtime_hours',
      label: t.overtimeHours,
      type: 'number',
      required: true,
      min: 0,
      step: 0.25
    },
    {
      name: 'is_present',
      label: t.present,
      type: 'checkbox'
    },
    {
      name: 'is_late',
      label: t.late,
      type: 'checkbox'
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Attendance>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.attendance}
        data={attendanceRecords}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addAttendance}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addAttendance : modalMode === 'edit' ? t.editAttendance : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
