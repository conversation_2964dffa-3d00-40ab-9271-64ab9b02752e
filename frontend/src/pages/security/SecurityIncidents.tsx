import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  AlertTriangle, 
  Plus, 
  Eye, 
  UserCheck,
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Filter,
  TrendingUp
} from 'lucide-react';
// Language is passed as prop, not from context

interface SecurityIncident {
  id: number;
  incident_id: string;
  title: string;
  description: string;
  incident_type: string;
  severity: string;
  status: string;
  affected_user_count: number;
  affected_systems: string[];
  data_categories_affected: string[];
  detected_at: string;
  reported_at: string;
  contained_at: string | null;
  resolved_at: string | null;
  duration_hours: number | null;
  root_cause: string;
  impact_assessment: string;
  remediation_actions: string[];
  assigned_to: number | null;
  assigned_to_name: string;
  reported_by_name: string;
  created_at: string;
}

interface IncidentStats {
  total_incidents: number;
  open_incidents: number;
  critical_incidents: number;
  resolved_incidents: number;
  recent_incidents_30d: number;
  average_resolution_hours: number;
  incident_type_breakdown: Array<{
    incident_type: string;
    count: number;
  }>;
}

interface SecurityIncidentsProps {
  language: 'ar' | 'en';
}
const SecurityIncidents: React.FC<SecurityIncidentsProps> = ({ language }) => {
  const [incidents, setIncidents] = useState<SecurityIncident[]>([]);
  const [incidentStats, setIncidentStats] = useState<IncidentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [severityFilter, setSeverityFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<SecurityIncident | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  // Form state
  const [incidentForm, setIncidentForm] = useState({
    title: '',
    description: '',
    incident_type: 'UNAUTHORIZED_ACCESS',
    severity: 'MEDIUM',
    affected_systems: [] as string[],
    data_categories_affected: [] as string[],
    impact_assessment: ''
  });

  const incidentTypes: any = [
    'UNAUTHORIZED_ACCESS',
    'DATA_BREACH',
    'MALWARE',
    'PHISHING',
    'BRUTE_FORCE',
    'PRIVILEGE_ESCALATION',
    'DATA_LOSS',
    'SYSTEM_COMPROMISE',
    'POLICY_VIOLATION',
    'OTHER'
  ];
  useEffect(() => {
    fetchIncidents();
    fetchIncidentStats();
  }, []);
  const fetchIncidents: any = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (typeFilter && typeFilter !== '__all__') params.append('incident_type', typeFilter);
      if (severityFilter && severityFilter !== '__all__') params.append('severity', severityFilter);
      if (statusFilter) params.append('status', statusFilter);
      const response: any = await fetch(`/api/security-incidents/?${params.toString()}`);
      if (response.ok) {
        const data: any = await response.json();
        setIncidents(data.results || data);
      }
    } catch (error) {
      console.error('Error fetching incidents:', error);
    } finally {
      setLoading(false);
    }
  };
  const fetchIncidentStats: any = async () => {
    try {
      const response = await fetch('/api/security-incidents/incident_stats/');
      if (response.ok) {
        const data: any = await response.json();
        setIncidentStats(data);
      }
    } catch (error) {
      console.error('Error fetching incident stats:', error);
    }
  };
  const handleCreateIncident: any = async () => {
    try {
      const response = await fetch('/api/security-incidents/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...incidentForm,
          detected_at: new Date().toISOString()
        }),
      });

      if (response.ok) {
        fetchIncidents();
        fetchIncidentStats();
        setIsCreateDialogOpen(false);
        resetForm();
      }
    } catch (error) {
      console.error('Error creating incident:', error);
    }
  };
  const handleUpdateStatus: any = async (incidentId: number, newStatus: string) => {
    try {
      const response = await fetch(`/api/security-incidents/${incidentId}/update_status/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          notes: `Status updated to ${newStatus}`
        }),
      });

      if (response.ok) {
        fetchIncidents();
        fetchIncidentStats();
        alert(language === 'ar' ? 'تم تحديث الحالة بنجاح' : 'Status updated successfully');
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };
  const resetForm: any = (): void => {
    setIncidentForm({
      title: '',
      description: '',
      incident_type: 'UNAUTHORIZED_ACCESS',
      severity: 'MEDIUM',
      affected_systems: [],
      data_categories_affected: [],
      impact_assessment: ''
    });
  };
  const getSeverityColor: any = (severity: string): void => {
    const colors = {
      'CRITICAL': 'bg-red-100 text-red-800',
      'HIGH': 'bg-orange-100 text-orange-800',
      'MEDIUM': 'bg-yellow-100 text-yellow-800',
      'LOW': 'bg-blue-100 text-blue-800'
    };
    return colors[severity as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };
  const getStatusColor: any = (status: string): void => {
    const colors = {
      'OPEN': 'bg-red-100 text-red-800',
      'INVESTIGATING': 'bg-yellow-100 text-yellow-800',
      'CONTAINED': 'bg-blue-100 text-blue-800',
      'RESOLVED': 'bg-green-100 text-green-800',
      'CLOSED': 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };
  const getStatusIcon: any = (status: string): void => {
    switch (status) {
      case 'OPEN':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'INVESTIGATING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'CONTAINED':
        return <AlertTriangle className="h-4 w-4 text-blue-500" />;
      case 'RESOLVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'CLOSED':
        return <CheckCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };
  const formatDate: any = (dateString: string | null): string => {
    if (!dateString) return '-';
    return new DatedateString.toLocaleDateString();
  };
  const filteredIncidents: any = incidents.filter(incident => {
    const matchesSearch = !searchTerm || 
      incident.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      incident.incident_id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType: any = !typeFilter || typeFilter === '__all__' || incident.incident_type === typeFilter;
    const matchesSeverity: any = !severityFilter || severityFilter === '__all__' || incident.severity === severityFilter;
    const matchesStatus: any = !statusFilter || incident.status === statusFilter;
    
    return matchesSearch && matchesType && matchesSeverity && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">
          {language === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  return (<div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {language === 'ar' ? 'الحوادث الأمنية' : 'Security Incidents'}
          </h1>
          <p className="text-muted-foreground">
            {language === 'ar' 
              ? 'إدارة ومتابعة الحوادث الأمنية والاستجابة لها'
              : 'Manage and track security incidents and responses'
            }
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {language === 'ar' ? 'إنشاء حادثة' : 'Create Incident'}
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {language === 'ar' ? 'إنشاء حادثة أمنية جديدة' : 'Create New Security Incident'}
              </DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'عنوان الحادثة' : 'Incident Title'}</Label>
                <Input
                  value={incidentForm.title}
                  onChange={(e: any) => setIncidentForm({...incidentForm, title: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل عنوان الحادثة' : 'Enter incident title'}
                />
              </div>
              <div>
                <Label>{language === 'ar' ? 'نوع الحادثة' : 'Incident Type'}</Label>
                <Select value={incidentForm.incident_type} onValueChange={(value) => setIncidentForm({...incidentForm, incident_type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {incidentTypes.map((type) => (<SelectItem key={type} value={type}>
                        {type.replace(/_/g, ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>{language === 'ar' ? 'مستوى الخطورة' : 'Severity Level'}</Label>
                <Select value={incidentForm.severity} onValueChange={(value) => setIncidentForm({...incidentForm, severity: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LOW">
                      {language === 'ar' ? 'منخفض' : 'Low'}
                    </SelectItem>
                    <SelectItem value="MEDIUM">
                      {language === 'ar' ? 'متوسط' : 'Medium'}
                    </SelectItem>
                    <SelectItem value="HIGH">
                      {language === 'ar' ? 'عالي' : 'High'}
                    </SelectItem>
                    <SelectItem value="CRITICAL">
                      {language === 'ar' ? 'حرج' : 'Critical'}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'وصف الحادثة' : 'Incident Description'}</Label>
                <Textarea
                  value={incidentForm.description}
                  onChange={(e: any) => setIncidentForm({...incidentForm, description: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل وصف تفصيلي للحادثة' : 'Enter detailed incident description'}
                  rows={4}
                />
              </div>
              <div className="md:col-span-2">
                <Label>{language === 'ar' ? 'تقييم الأثر' : 'Impact Assessment'}</Label>
                <Textarea
                  value={incidentForm.impact_assessment}
                  onChange={(e: any) => setIncidentForm({...incidentForm, impact_assessment: e.target.value})}
                  placeholder={language === 'ar' ? 'أدخل تقييم أثر الحادثة' : 'Enter impact assessment'}
                  rows={3}
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                {language === 'ar' ? 'إلغاء' : 'Cancel'}
              </Button>
              <Button onClick={handleCreateIncident}>
                {language === 'ar' ? 'إنشاء الحادثة' : 'Create Incident'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics */}
      {incidentStats && (<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'إجمالي الحوادث' : 'Total Incidents'}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{incidentStats.total_incidents}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'مفتوحة' : 'Open'}
              </CardTitle>
              <XCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{incidentStats.open_incidents}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'حرجة' : 'Critical'}
              </CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">{incidentStats.critical_incidents}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'محلولة' : 'Resolved'}
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{incidentStats.resolved_incidents}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'حديثة (30 يوم)' : 'Recent (30d)'}
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{incidentStats.recent_incidents_30d}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {language === 'ar' ? 'متوسط الحل' : 'Avg Resolution'}
              </CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{incidentStats.average_resolution_hours.toFixed(1)}h</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={language === 'ar' ? 'البحث في الحوادث...' : 'Search incidents...'}
                  value={searchTerm}
                  onChange={(e: any) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الأنواع' : 'All types'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع الأنواع' : 'All types'}
                </SelectItem>
                {incidentTypes.map((type) => (<SelectItem key={type} value={type}>
                    {type.replace(/_/g, ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع المستويات' : 'All severities'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="__all__">
                  {language === 'ar' ? 'جميع المستويات' : 'All severities'}
                </SelectItem>
                <SelectItem value="CRITICAL">
                  {language === 'ar' ? 'حرج' : 'Critical'}
                </SelectItem>
                <SelectItem value="HIGH">
                  {language === 'ar' ? 'عالي' : 'High'}
                </SelectItem>
                <SelectItem value="MEDIUM">
                  {language === 'ar' ? 'متوسط' : 'Medium'}
                </SelectItem>
                <SelectItem value="LOW">
                  {language === 'ar' ? 'منخفض' : 'Low'}
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={language === 'ar' ? 'جميع الحالات' : 'All statuses'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">
                  {language === 'ar' ? 'جميع الحالات' : 'All statuses'}
                </SelectItem>
                <SelectItem value="OPEN">
                  {language === 'ar' ? 'مفتوحة' : 'Open'}
                </SelectItem>
                <SelectItem value="INVESTIGATING">
                  {language === 'ar' ? 'قيد التحقيق' : 'Investigating'}
                </SelectItem>
                <SelectItem value="CONTAINED">
                  {language === 'ar' ? 'محتواة' : 'Contained'}
                </SelectItem>
                <SelectItem value="RESOLVED">
                  {language === 'ar' ? 'محلولة' : 'Resolved'}
                </SelectItem>
                <SelectItem value="CLOSED">
                  {language === 'ar' ? 'مغلقة' : 'Closed'}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Incidents Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            {language === 'ar' ? 'الحوادث الأمنية' : 'Security Incidents'}
            <Badge variant="secondary" className="ml-2">
              {filteredIncidents.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{language === 'ar' ? 'معرف الحادثة' : 'Incident ID'}</TableHead>
                  <TableHead>{language === 'ar' ? 'العنوان' : 'Title'}</TableHead>
                  <TableHead>{language === 'ar' ? 'النوع' : 'Type'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الخطورة' : 'Severity'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الحالة' : 'Status'}</TableHead>
                  <TableHead>{language === 'ar' ? 'المكلف' : 'Assigned To'}</TableHead>
                  <TableHead>{language === 'ar' ? 'تاريخ الاكتشاف' : 'Detected'}</TableHead>
                  <TableHead>{language === 'ar' ? 'الإجراءات' : 'Actions'}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredIncidents.map((incident) => (<TableRow key={incident.id}>
                    <TableCell>
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {incident.incident_id}
                      </code>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{incident.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {incident.affected_user_count} {language === 'ar' ? 'مستخدم متأثر' : 'users affected'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {incident.incident_type.replace(/_/g, ' ')}
                      </span>
                    </TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor(incident.severity)}>
                        {language === 'ar' 
                          ? incident.severity === 'CRITICAL' ? 'حرج' 
                            : incident.severity === 'HIGH' ? 'عالي'
                            : incident.severity === 'MEDIUM' ? 'متوسط'
                            : incident.severity === 'LOW' ? 'منخفض'
                            : incident.severity
                          : incident.severity
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(incident.status)}
                        <Badge className={getStatusColor(incident.status)}>
                          {language === 'ar' 
                            ? incident.status === 'OPEN' ? 'مفتوحة' 
                              : incident.status === 'INVESTIGATING' ? 'قيد التحقيق'
                              : incident.status === 'CONTAINED' ? 'محتواة'
                              : incident.status === 'RESOLVED' ? 'محلولة'
                              : incident.status === 'CLOSED' ? 'مغلقة'
                              : incident.status
                            : incident.status
                          }
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {incident.assigned_to_name || '-'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDate(incident.detected_at)}
                      </div>
                      {incident.duration_hours && (<div className="text-xs text-muted-foreground">
                          {incident.duration_hours.toFixed(1)}h {language === 'ar' ? 'مدة' : 'duration'}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedIncident(incident);
                            setIsDetailsDialogOpen(true);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {incident.status === 'OPEN' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleUpdateStatus(incident.id, 'INVESTIGATING')}
                          >
                            <UserCheck className="h-4 w-4" />
                          </Button>
                        )}
                        {incident.status === 'INVESTIGATING' && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleUpdateStatus(incident.id, 'CONTAINED')}
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Incident Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {language === 'ar' ? 'تفاصيل الحادثة' : 'Incident Details'}
            </DialogTitle>
          </DialogHeader>
          {selectedIncident && (<div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'معرف الحادثة' : 'Incident ID'}
                  </Label>
                  <div className="text-sm">{selectedIncident.incident_id}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </Label>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusIcon(selectedIncident.status)}
                    <Badge className={getStatusColor(selectedIncident.status)}>
                      {selectedIncident.status}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'مستوى الخطورة' : 'Severity'}
                  </Label>
                  <div className="mt-1">
                    <Badge className={getSeverityColor(selectedIncident.severity)}>
                      {selectedIncident.severity}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'نوع الحادثة' : 'Incident Type'}
                  </Label>
                  <div className="text-sm">{selectedIncident.incident_type.replace(/_/g, ' ')}</div>
                </div>
              </div>

              <div>
                <Label className="font-medium">
                  {language === 'ar' ? 'الوصف' : 'Description'}
                </Label>
                <div className="text-sm mt-1 p-3 bg-muted rounded">
                  {selectedIncident.description}
                </div>
              </div>

              {selectedIncident.impact_assessment && (<div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'تقييم الأثر' : 'Impact Assessment'}
                  </Label>
                  <div className="text-sm mt-1 p-3 bg-muted rounded">
                    {selectedIncident.impact_assessment}
                  </div>
                </div>
              )}

              {selectedIncident.affected_systems.length > 0 && (<div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'الأنظمة المتأثرة' : 'Affected Systems'}
                  </Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {selectedIncident.affected_systems.map((system, index) => (
                      <Badge key={index} variant="outline">
                        {system}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {selectedIncident.remediation_actions.length > 0 && (<div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'إجراءات المعالجة' : 'Remediation Actions'}
                  </Label>
                  <ul className="list-disc list-inside text-sm mt-1 space-y-1">
                    {selectedIncident.remediation_actions.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'تاريخ الاكتشاف' : 'Detected At'}
                  </Label>
                  <div>{formatDate(selectedIncident.detected_at)}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'تاريخ الاحتواء' : 'Contained At'}
                  </Label>
                  <div>{formatDate(selectedIncident.contained_at)}</div>
                </div>
                <div>
                  <Label className="font-medium">
                    {language === 'ar' ? 'تاريخ الحل' : 'Resolved At'}
                  </Label>
                  <div>{formatDate(selectedIncident.resolved_at)}</div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SecurityIncidents;
