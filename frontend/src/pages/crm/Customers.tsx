import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  Eye,
  Edit,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  TrendingUp,
  Star,
  Building,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { customerService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface CustomersProps {
  language: 'ar' | 'en'
}

interface Customer {
  id: number
  customer_code: string
  company_name: string
  company_name_ar?: string
  contact_person: string
  email: string
  phone: string
  address: string
  address_ar?: string
  is_active: boolean
  total_orders?: number
  total_spent?: number
  satisfaction_score?: number
  last_contact_date?: string
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    title: 'إدارة العملاء',
    subtitle: 'إدارة شاملة لقاعدة بيانات العملاء والعلاقات',
    totalCustomers: 'إجمالي العملاء',
    activeCustomers: 'العملاء النشطون',
    newCustomers: 'عملاء جدد',
    customerValue: 'قيمة العملاء',
    searchCustomers: 'البحث في العملاء...',
    addCustomer: 'إضافة عميل',
    exportData: 'تصدير البيانات',
    filterBy: 'تصفية حسب',
    customerType: 'نوع العميل',
    status: 'الحالة',
    customerList: 'قائمة العملاء',
    customerName: 'اسم العميل',
    company: 'الشركة',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    location: 'الموقع',
    joinDate: 'تاريخ الانضمام',
    totalOrders: 'إجمالي الطلبات',
    totalValue: 'إجمالي القيمة',
    lastOrder: 'آخر طلب',
    rating: 'التقييم',
    actions: 'الإجراءات',
    viewProfile: 'عرض الملف الشخصي',
    editCustomer: 'تعديل العميل',
    active: 'نشط',
    inactive: 'غير نشط',
    vip: 'عميل مميز',
    regular: 'عادي',
    corporate: 'شركة',
    individual: 'فرد',
    thisMonth: 'هذا الشهر',
    refresh: 'تحديث'
  },
  en: {
    title: 'Customer Management',
    subtitle: 'Comprehensive customer database and relationship management',
    totalCustomers: 'Total Customers',
    activeCustomers: 'Active Customers',
    newCustomers: 'New Customers',
    customerValue: 'Customer Value',
    searchCustomers: 'Search customers...',
    addCustomer: 'Add Customer',
    exportData: 'Export Data',
    filterBy: 'Filter By',
    customerType: 'Customer Type',
    status: 'Status',
    customerList: 'Customer List',
    customerName: 'Customer Name',
    company: 'Company',
    email: 'Email',
    phone: 'Phone',
    location: 'Location',
    joinDate: 'Join Date',
    totalOrders: 'Total Orders',
    totalValue: 'Total Value',
    lastOrder: 'Last Order',
    rating: 'Rating',
    actions: 'Actions',
    viewProfile: 'View Profile',
    editCustomer: 'Edit Customer',
    active: 'Active',
    inactive: 'Inactive',
    vip: 'VIP',
    regular: 'Regular',
    corporate: 'Corporate',
    individual: 'Individual',
    thisMonth: 'This Month',
    refresh: 'Refresh'
  }
}

export default function Customers({ language }: CustomersProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: customers,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Customer>({
    service: customerService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const formatCurrency = (amount?: number): string => {
    if (!amount) return 'N/A'
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const getStatusColor = (isActive: boolean): string => {
    return isActive
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-red-100 text-red-800 border-red-200'
  }

  const getSatisfactionColor = (score?: number): string => {
    if (!score) return 'text-gray-400'
    if (score >= 4.5) return 'text-green-400'
    if (score >= 3.5) return 'text-blue-400'
    if (score >= 2.5) return 'text-yellow-400'
    return 'text-red-400'
  }

  const renderStars = (rating?: number) => {
    if (!rating) return <span className="text-gray-400">N/A</span>
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'}`}
      />
    ))
  }

  // Table columns configuration
  const columns: TableColumn<Customer>[] = [
    {
      key: 'customer_code',
      label: 'Customer Code',
      sortable: true,
      render: (item: Customer) => (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-blue-400" />
          <span className="text-white font-medium">{item.customer_code}</span>
        </div>
      )
    },
    {
      key: 'company_name',
      label: t.company,
      sortable: true,
      render: (item: Customer) => (
        <div>
          <div className="flex items-center gap-2">
            <Building className="h-3 w-3 text-purple-400" />
            <span className="text-white font-medium">{item.company_name}</span>
          </div>
          <div className="text-white/60 text-sm">{item.contact_person}</div>
        </div>
      )
    },
    {
      key: 'email',
      label: t.email,
      render: (item: Customer) => (
        <div className="flex items-center gap-1">
          <Mail className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.email}</span>
        </div>
      )
    },
    {
      key: 'phone',
      label: t.phone,
      render: (item: Customer) => (
        <div className="flex items-center gap-1">
          <Phone className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.phone}</span>
        </div>
      )
    },
    {
      key: 'is_active',
      label: t.status,
      sortable: true,
      render: (item: Customer) => (
        <Badge className={getStatusColor(item.is_active)}>
          {item.is_active ? t.active : t.inactive}
        </Badge>
      )
    },
    {
      key: 'total_spent',
      label: 'Total Spent',
      sortable: true,
      render: (item: Customer) => (
        <div className="text-right">
          <div className="text-green-400 font-medium">{formatCurrency(item.total_spent)}</div>
          {item.total_orders && (
            <div className="text-white/60 text-sm">{item.total_orders} orders</div>
          )}
        </div>
      )
    },
    {
      key: 'satisfaction_score',
      label: 'Satisfaction',
      render: (item: Customer) => (
        <div className="flex items-center gap-1">
          {renderStars(item.satisfaction_score)}
          <span className={`text-sm ml-1 ${getSatisfactionColor(item.satisfaction_score)}`}>
            {item.satisfaction_score?.toFixed(1) || 'N/A'}
          </span>
        </div>
      )
    },
    {
      key: 'last_contact_date',
      label: 'Last Contact',
      sortable: true,
      render: (item: Customer) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">
            {item.last_contact_date ? new Date(item.last_contact_date).toLocaleDateString() : 'N/A'}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Customer>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Customer) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Customer) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Customer) => {
        if (confirm('Are you sure you want to delete this customer?')) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'customer_code',
      label: 'Customer Code',
      type: 'text',
      required: true
    },
    {
      name: 'company_name',
      label: t.company,
      type: 'text',
      required: true
    },
    {
      name: 'company_name_ar',
      label: t.company + ' (عربي)',
      type: 'text'
    },
    {
      name: 'contact_person',
      label: 'Contact Person',
      type: 'text',
      required: true
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel',
      required: true
    },
    {
      name: 'address',
      label: t.address,
      type: 'textarea',
      required: true
    },
    {
      name: 'address_ar',
      label: t.address + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'total_orders',
      label: 'Total Orders',
      type: 'number',
      min: 0
    },
    {
      name: 'total_spent',
      label: 'Total Spent',
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'satisfaction_score',
      label: 'Satisfaction Score',
      type: 'number',
      min: 1,
      max: 5,
      step: 0.1
    },
    {
      name: 'last_contact_date',
      label: 'Last Contact Date',
      type: 'date'
    },
    {
      name: 'is_active',
      label: 'Active',
      type: 'checkbox'
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Customer>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.customers}
        data={customers}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addCustomer}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addCustomer : modalMode === 'edit' ? t.editCustomer : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
    {
      id: 1,
      name: 'أحمد محمد الأحمد',
      nameEn: 'Ahmed Mohammed Al-Ahmed',
      company: 'شركة التقنية المتقدمة',
      companyEn: 'Advanced Technology Company',
      email: '<EMAIL>',
      phone: '+966501234567',
      location: 'الرياض، السعودية',
      locationEn: 'Riyadh, Saudi Arabia',
      joinDate: '2023-01-15',
      totalOrders: 45,
      totalValue: 125000,
      lastOrder: '2024-01-20',
      rating: 4.8,
      status: 'active',
      type: 'corporate',
      avatar: 'A'
    },
    {
      id: 2,
      name: 'فاطمة سالم العتيبي',
      nameEn: 'Fatima Salem Al-Otaibi',
      company: 'مؤسسة النور للتجارة',
      companyEn: 'Al-Noor Trading Est.',
      email: '<EMAIL>',
      phone: '+966507654321',
      location: 'جدة، السعودية',
      locationEn: 'Jeddah, Saudi Arabia',
      joinDate: '2022-08-20',
      totalOrders: 78,
      totalValue: 285000,
      lastOrder: '2024-01-25',
      rating: 4.9,
      status: 'active',
      type: 'vip',
      avatar: 'F'
    },
    {
      id: 3,
      name: 'عمر خالد الشمري',
      nameEn: 'Omar Khalid Al-Shamri',
      company: 'فرد',
      companyEn: 'Individual',
      email: '<EMAIL>',
      phone: '+966509876543',
      location: 'الدمام، السعودية',
      locationEn: 'Dammam, Saudi Arabia',
      joinDate: '2023-03-10',
      totalOrders: 12,
      totalValue: 35000,
      lastOrder: '2024-01-18',
      rating: 4.2,
      status: 'active',
      type: 'individual',
      avatar: 'O'
    }
  ])

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-400 border-green-500/30'
      case 'inactive':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'vip':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'corporate':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
      case 'individual':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
      case 'vip':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !selectedType || customer.type === selectedType
    const matchesStatus = !selectedStatus || customer.status === selectedStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  const customerMetricsCards = [
    {
      title: t.totalCustomers,
      value: customerMetrics.totalCustomers.toLocaleString(),
      change: '+12.5%',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeCustomers,
      value: customerMetrics.activeCustomers.toLocaleString(),
      change: '+8.3%',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.newCustomers,
      value: customerMetrics.newCustomers.toString(),
      change: t.thisMonth,
      trend: 'stable',
      icon: UserPlus,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.customerValue,
      value: new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        notation: 'compact'
      }).format(customerMetrics.customerValue),
      change: '+15.7%',
      trend: 'up',
      icon: DollarSign,
      color: 'from-orange-500 to-orange-600'
    }
  ]

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
          <p className="text-white/70">{t.subtitle}</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10"
          >
            <Calendar className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <UserPlus className="h-4 w-4 mr-2" />
            {t.addCustomer}
          </Button>
          <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
            <Download className="h-4 w-4 mr-2" />
            {t.exportData}
          </Button>
        </div>
      </div>

      {/* Customer Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {customerMetricsCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${
                  card.trend === 'up' ? 'text-green-400' : 
                  card.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                }`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Search and Filters */}
      <Card className="glass-card border-white/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
              <Input
                placeholder={t.searchCustomers}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-white/60"
              />
            </div>
            
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.customerType}</option>
              <option value="corporate">{t.corporate}</option>
              <option value="individual">{t.individual}</option>
              <option value="vip">{t.vip}</option>
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="bg-white/10 border-white/20 text-white rounded-lg px-3 py-2"
            >
              <option value="">{t.status}</option>
              <option value="active">{t.active}</option>
              <option value="inactive">{t.inactive}</option>
            </select>
            
            <Button variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-xl p-3 backdrop-blur-10">
              <Filter className="h-4 w-4 mr-2" />
              {t.filterBy}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Customer List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-xl">{t.customerList}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/20">
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.customerName}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.company}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.email}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.location}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.totalOrders}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.totalValue}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.rating}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.status}</th>
                  <th className="text-left py-3 px-4 text-white/80 font-medium">{t.actions}</th>
                </tr>
              </thead>
              <tbody>
                {filteredCustomers.map((customer) => (
                  <tr key={customer.id} className="border-b border-white/10 hover:bg-white/5 transition-colors">
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                          {customer.avatar}
                        </div>
                        <div>
                          <p className="text-white font-medium">
                            {language === 'ar' ? customer.name : customer.nameEn}
                          </p>
                          <p className="text-white/60 text-sm">{customer.phone}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? customer.company : customer.companyEn}
                    </td>
                    <td className="py-4 px-4 text-white">{customer.email}</td>
                    <td className="py-4 px-4 text-white">
                      {language === 'ar' ? customer.location : customer.locationEn}
                    </td>
                    <td className="py-4 px-4 text-white font-medium">{customer.totalOrders}</td>
                    <td className="py-4 px-4 text-white">
                      {new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR'
                      }).format(customer.totalValue)}
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-white text-sm">{customer.rating}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(customer.status)}`}>
                        {t[customer.status as keyof typeof t]}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" className="text-white/80 hover:text-white hover:bg-white/15 rounded-lg p-2 backdrop-blur-10">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
