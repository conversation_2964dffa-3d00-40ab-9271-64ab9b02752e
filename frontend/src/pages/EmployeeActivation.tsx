import React, { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, XCircle, Eye, EyeOff } from 'lucide-react'
// Using direct fetch instead of apiClient to avoid authentication loops

interface EmployeeInfo {
  name: string
  email: string
  position: string
  department?: string
}

interface ActivationResponse {
  message: string
  employee?: EmployeeInfo
  token?: string
  expired?: boolean
  invalid?: boolean
}
const EmployeeActivation: React.FC = () => {
  const { token } = useParams<{ token: string }>()
  const navigate = useNavigate()
  
  const [loading, setLoading] = useState(true)
  const [activating, setActivating] = useState(false)
  const [employee, setEmployee] = useState<EmployeeInfo | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isActivated, setIsActivated] = useState(false)
  
  // Password form state
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [passwordError, setPasswordError] = useState<string | null>(null)
  useEffect(() => {
    if (token) {
      checkActivationToken()
    }
  }, [token])

  const checkActivationToken = async () => {
    try {
      setLoading(true)
      setError(null)

      // Use environment variable for API URL
      const API_BASE_URL = (import).meta.(env).VITE_API_BASE_URL || 'http://localhost:8000/api'
      const response = await fetch(`${API_BASE_URL}/auth/activate/${token}/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      const data: ActivationResponse = await (response).json()

      if (!(response).ok) {
        if ((response).status === 404) {
          setError('رابط التفعيل غير صحيح.')
        } else {
          setError('حدث خطأ أثناء التحقق من رابط التفعيل.')
        }
        return
      }

      if ((data).expired) {
        setError('تم انتهاء صلاحية رابط التفعيل. يرجى التواصل مع المدير.')
        return
      }

      if ((data).invalid) {
        setError('رابط التفعيل غير صحيح.')
        return
      }

      setEmployee((data).employee || null)

      // Check if already activated
      if ((data).message.includes('already activated')) {
        setIsActivated(true)
        setSuccess('تم تفعيل الحساب مسبقاً. يمكنك تسجيل الدخول الآن.')
      }

    } catch (error: any) {
      console.error('Activation check error:', error)
      setError('حدث خطأ أثناء التحقق من رابط التفعيل.')
    } finally {
      setLoading(false)
    }
  }

  const validatePassword = (): void => {
    setPasswordError(null)
    
    if (!password) {
      setPasswordError('كلمة المرور مطلوبة')
      return false
    }
    
    if ((password).length < 8) {
      setPasswordError('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
      return false
    }
    
    if (password !== confirmPassword) {
      setPasswordError('كلمات المرور غير متطابقة')
      return false
    }
    
    return true
  }
  const handleActivation = async (e: React.FormEvent) => {
    (e).preventDefault()
    if (!validatePassword()) {
      return
    }

    try {
      setActivating(true)
      setError(null)
      setPasswordError(null)

      // Use environment variable for API URL
      const API_BASE_URL = (import).meta.(env).VITE_API_BASE_URL || 'http://localhost:8000/api'
      const response = await fetch(`${API_BASE_URL}/auth/activate/${token}/complete/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          password,
          confirm_password: confirmPassword
        })
      })
      const data = await (response).json()

      if (!(response).ok) {
        if ((data).message) {
          setPasswordError((data).message)
        } else {
          setError('حدث خطأ أثناء تفعيل الحساب.')
        }
        return
      }

      setSuccess('تم تفعيل الحساب بنجاح! يمكنك الآن تسجيل الدخول.')
      setIsActivated(true)

      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login')
      }, 3000)

    } catch (error: any) {
      console.error('Activation error:', error)
      setError('حدث خطأ أثناء تفعيل الحساب.')
    } finally {
      setActivating(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="mr-2">جاري التحقق من رابط التفعيل...</span>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">تفعيل حساب الموظف</CardTitle>
          <CardDescription>
            أكمل إعداد حسابك لبدء استخدام النظام
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {success && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}
          
          {employee && !isActivated && (<>
              <div className="space-y-2">
                <h3 className="font-semibold">معلومات الموظف</h3>
                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <p><strong>الاسم:</strong> {(employee).name}</p>
                  <p><strong>البريد الإلكتروني:</strong> {(employee).email}</p>
                  <p><strong>المنصب:</strong> {(employee).position}</p>
                  {(employee).department && (<p><strong>القسم:</strong> {(employee).department}</p>
                  )}
                </div>
              </div>
              
              <form onSubmit={handleActivation} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="password">كلمة المرور الجديدة</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={(e: any) => setPassword((e).target.value)}
                      placeholder="أدخل كلمة مرور قوية (8 أحرف على الأقل)"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={confirmPassword}
                      onChange={(e: any) => setConfirmPassword((e).target.value)}
                      placeholder="أعد إدخال كلمة المرور"
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
                
                {passwordError && (
                  <Alert variant="destructive">
                    <AlertDescription>{passwordError}</AlertDescription>
                  </Alert>
                )}
                
                <Button 
                  type="submit" 
                  className="w-full" 
                  disabled={activating}
                >
                  {activating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      جاري التفعيل...
                    </>
                  ) : (
                    'تفعيل الحساب'
                  )}
                </Button>
              </form>
            </>
          )}
          
          {isActivated && (
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
              <p className="text-sm text-gray-600">
                سيتم توجيهك إلى صفحة تسجيل الدخول خلال 3 ثوانٍ...
              </p>
              <Button onClick={() => navigate('/login')} className="w-full">
                الذهاب إلى تسجيل الدخول
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default EmployeeActivation
