#!/usr/bin/env python3

# Read the file
with open('src/components/ui/button.tsx', 'r') as f:
    lines = f.readlines()

# Fix line 53 specifically
for i, line in enumerate(lines):
    if i == 52:  # Line 53 (0-based index)
        if line.strip().endswith('}}'):
            lines[i] = line.replace('}}', '})}')
            print(f"Fixed line {i+1}: {line.strip()} -> {lines[i].strip()}")

# Write back to file
with open('src/components/ui/button.tsx', 'w') as f:
    f.writelines(lines)

print("Fixed button.tsx")
