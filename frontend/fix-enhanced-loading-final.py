#!/usr/bin/env python3

# Read the file
with open('src/components/ui/enhanced-loading.tsx', 'r') as f:
    lines = f.readlines()

# Fix line 62 - remove extra closing parenthesis
for i, line in enumerate(lines):
    if i == 61:  # Line 62 (0-based index)
        if "variant === 'default' ? 'animate-spin' : ''))} " in line:
            lines[i] = line.replace("variant === 'default' ? 'animate-spin' : '))} ", "variant === 'default' ? 'animate-spin' : '')} ")
            print(f"Fixed line {i+1}: {line.strip()} -> {lines[i].strip()}")

# Write back to file
with open('src/components/ui/enhanced-loading.tsx', 'w') as f:
    f.writelines(lines)

print("Fixed enhanced-loading.tsx")
