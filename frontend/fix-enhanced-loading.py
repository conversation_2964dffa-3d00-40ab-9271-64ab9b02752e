#!/usr/bin/env python3

# Read the file
with open('src/components/ui/enhanced-loading.tsx', 'r') as f:
    content = f.read()

# Fix the specific issues
# 1. Fix missing closing parenthesis in className
content = content.replace("variant === 'default' ? 'animate-spin' : ''} ", "variant === 'default' ? 'animate-spin' : '')} ")

# 2. Fix malformed closing tag
content = content.replace('/)}>)', '/>')

# Write back to file
with open('src/components/ui/enhanced-loading.tsx', 'w') as f:
    f.write(content)

print("Fixed enhanced-loading.tsx")
