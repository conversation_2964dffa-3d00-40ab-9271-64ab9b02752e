#!/usr/bin/env python3
"""
Fix systematic syntax errors in TypeScript/React files
"""

import os
import re
import glob
from pathlib import Path

def fix_parentheses_patterns(content):
    """Fix incorrect parentheses around property access"""

    # Pattern 1: (variable).property -> variable.property
    # But be careful not to break legitimate parentheses like (a + b).property
    content = re.sub(r'\(([a-zA-Z_][a-zA-Z0-9_]*)\)\.', r'\1.', content)

    # Pattern 2: (variable)[index] -> variable[index]
    content = re.sub(r'\(([a-zA-Z_][a-zA-Z0-9_]*)\)\[', r'\1[', content)

    # Pattern 3: Fix specific common patterns
    content = re.sub(r'\(Date\)\.now\(\)', r'Date.now()', content)
    content = re.sub(r'\(console\)\.log', r'console.log', content)
    content = re.sub(r'\(console\)\.warn', r'console.warn', content)
    content = re.sub(r'\(console\)\.error', r'console.error', content)
    content = re.sub(r'\(window\)\.', r'window.', content)
    content = re.sub(r'\(document\)\.', r'document.', content)
    content = re.sub(r'\(localStorage\)\.', r'localStorage.', content)
    content = re.sub(r'\(sessionStorage\)\.', r'sessionStorage.', content)
    content = re.sub(r'\(navigator\)\.', r'navigator.', content)
    content = re.sub(r'\(process\)\.env', r'process.env', content)
    content = re.sub(r'\(import\)\.meta', r'import.meta', content)

    # Pattern 4: Fix array access patterns
    content = re.sub(r'payload\[0\]\.\(payload\)\.', r'payload[0].payload.', content)

    # Pattern 5: Fix object property access in template literals
    content = re.sub(r'\$\{\(([a-zA-Z_][a-zA-Z0-9_]*)\)\.', r'${\1.', content)

    # Pattern 6: Fix more specific patterns that were missed
    content = re.sub(r'\(renderCount\)\.current', r'renderCount.current', content)
    content = re.sub(r'\(lastRenderTime\)\.current', r'lastRenderTime.current', content)
    content = re.sub(r'\(lastWarningTime\)\.current', r'lastWarningTime.current', content)
    content = re.sub(r'\(visibleItems\)\.map', r'visibleItems.map', content)

    # Pattern 8: Fix decimal number patterns
    content = re.sub(r'\(0\)\.(\d+)', r'0.\1', content)
    content = re.sub(r'\(1\)\.(\d+)', r'1.\1', content)

    # Pattern 9: Fix JSX closing tag issues
    content = re.sub(r'/>\}', r'/>)', content)
    content = re.sub(r'/>\)>', r'/>}>', content)
    content = re.sub(r'/>\) />\)', r'/>} />}', content)
    content = re.sub(r'/>\)', r'/>}', content)
    content = re.sub(r'<([A-Z][a-zA-Z]*) ([^>]*)/>\) />\)', r'<\1 \2/>} />}', content)

    # Pattern 10: Fix malformed try-catch blocks
    content = re.sub(r"'\)\} catch \(", r"')\n    } catch (", content)
    content = re.sub(r"'\)\} finally \{", r"')\n    } finally {", content)

    # Pattern 11: Fix missing closing parentheses in className
    content = re.sub(r'hover:border-white/40"\}', r'hover:border-white/40")}', content)

    # Pattern 12: Fix function parameter syntax
    content = re.sub(r'= \{\} =>', r'= {}) =>', content)
    content = re.sub(r': ([A-Z][a-zA-Z]*) =>', r': \1) =>', content)

    # Pattern 13: Fix TypeScript function return type syntax
    content = re.sub(r': T\) =>', r': T =>', content)
    content = re.sub(r': ([A-Z][a-zA-Z]*)\) =>', r': \1 =>', content)

    # Pattern 14: Fix useSelector syntax
    content = re.sub(r'useSelector\(\(state: RootState =>', r'useSelector((state: RootState) =>', content)
    content = re.sub(r'useSelector\(\(state: ([A-Z][a-zA-Z]*) =>', r'useSelector((state: \1) =>', content)

    # Pattern 15: Fix function parameter missing closing parenthesis
    content = re.sub(r'update: T =>', r'update: T) =>', content)
    content = re.sub(r'options: ([A-Z][a-zA-Z]*) =>', r'options: \1) =>', content)

    # Pattern 7: Fix function signature issues
    content = re.sub(r'= true: string \{', r'= true): string {', content)
    content = re.sub(r'= false: boolean \{', r'= false): boolean {', content)
    content = re.sub(r'= null: any \{', r'= null): any {', content)
    content = re.sub(r'= false: string \{', r'= false): string {', content)
    content = re.sub(r'= \{\}: string \{', r'= {}): string {', content)
    content = re.sub(r'= \{\}: Promise<Response> \{', r'= {}): Promise<Response> {', content)
    content = re.sub(r'= \{\}: ProcessedError \{', r'= {}): ProcessedError {', content)
    content = re.sub(r"= '([^']*)': string \{", r"= '\1'): string {", content)
    content = re.sub(r"= '([^']*)': boolean \{", r"= '\1'): boolean {", content)

    return content

def fix_jsx_patterns(content):
    """Fix JSX-specific syntax errors"""
    
    # Fix malformed JSX closing tags
    content = re.sub(r'// @ts-ignore\}\}', r'}}', content)
    content = re.sub(r'// @ts-ignore\}', r'}', content)
    
    # Fix decimal number patterns in JSX
    content = re.sub(r'maxLength \* \(0\)\.8', r'maxLength * 0.8', content)
    
    # Fix className concatenation issues
    content = re.sub(r'locale === \'ar\' && "flex-row-reverse"\}', r'locale === \'ar\' && "flex-row-reverse"}', content)
    
    return content

def fix_file(file_path):
    """Fix a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        content = original_content
        content = fix_parentheses_patterns(content)
        content = fix_jsx_patterns(content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Fixed {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix all TypeScript/React files"""
    src_dir = Path("src")
    
    if not src_dir.exists():
        print("❌ src directory not found")
        return
    
    # Find all TypeScript and React files
    patterns = ["**/*.ts", "**/*.tsx"]
    files = []
    
    for pattern in patterns:
        files.extend(src_dir.glob(pattern))
    
    print(f"🔍 Found {len(files)} TypeScript/React files")
    
    fixed_count = 0
    for file_path in files:
        if fix_file(file_path):
            fixed_count += 1
    
    print(f"\n🎉 Fixed {fixed_count} files out of {len(files)} total files")

if __name__ == "__main__":
    main()
