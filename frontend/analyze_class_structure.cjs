#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function analyzeClassStructure(filePath) {
  console.log(`🔍 Analyzing class structure in ${filePath}`);
  console.log('='.repeat(60));
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  let braceCount = 0;
  let parenCount = 0;
  let bracketCount = 0;
  let inClass = false;
  let classStartLine = -1;
  let issues = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // Track class boundaries
    if (line.includes('class ComprehensiveErrorHandler')) {
      inClass = true;
      classStartLine = lineNum;
      console.log(`📍 Class starts at line ${lineNum}`);
    }
    
    // Count braces, parentheses, and brackets
    const openBraces = (line.match(/{/g) || []).length;
    const closeBraces = (line.match(/}/g) || []).length;
    const openParens = (line.match(/\(/g) || []).length;
    const closeParens = (line.match(/\)/g) || []).length;
    const openBrackets = (line.match(/\[/g) || []).length;
    const closeBrackets = (line.match(/\]/g) || []).length;
    
    braceCount += openBraces - closeBraces;
    parenCount += openParens - closeParens;
    bracketCount += openBrackets - closeBrackets;
    
    // Check for potential issues
    if (inClass) {
      // Look for method declarations
      if (line.trim().match(/^\w+\s*\([^)]*\)\s*:\s*\w+\s*{/) || 
          line.trim().match(/^\w+\s*\([^)]*\)\s*{/)) {
        console.log(`🔧 Method found at line ${lineNum}: ${line.trim()}`);
        
        // Check if previous line might be causing issues
        if (i > 0) {
          const prevLine = lines[i-1].trim();
          if (prevLine && !prevLine.endsWith('}') && !prevLine.endsWith(';') && 
              !prevLine.startsWith('//') && !prevLine.startsWith('*')) {
            issues.push({
              line: lineNum - 1,
              issue: 'Previous line might be missing semicolon or closing brace',
              content: prevLine
            });
          }
        }
      }
      
      // Look for unclosed strings or template literals
      const singleQuotes = (line.match(/'/g) || []).length;
      const doubleQuotes = (line.match(/"/g) || []).length;
      const backticks = (line.match(/`/g) || []).length;
      
      if (singleQuotes % 2 !== 0 || doubleQuotes % 2 !== 0 || backticks % 2 !== 0) {
        issues.push({
          line: lineNum,
          issue: 'Potential unclosed string or template literal',
          content: line.trim()
        });
      }
      
      // Check for malformed function signatures
      if (line.includes(': ') && line.includes('{') && !line.includes('=>')) {
        const beforeColon = line.substring(0, line.indexOf(': '));
        const afterColon = line.substring(line.indexOf(': ') + 2);
        
        if (beforeColon.includes('=') && afterColon.includes('{')) {
          issues.push({
            line: lineNum,
            issue: 'Malformed function signature - colon should be parenthesis',
            content: line.trim()
          });
        }
      }
      
      // Report significant brace imbalances
      if (Math.abs(braceCount) > 5) {
        console.log(`⚠️  Line ${lineNum}: Large brace imbalance (${braceCount})`);
      }
      
      // Check if class ends
      if (braceCount === 0 && classStartLine > 0 && lineNum > classStartLine + 10) {
        console.log(`📍 Class likely ends at line ${lineNum}`);
        inClass = false;
      }
    }
  }
  
  console.log('\n📊 Final Counts:');
  console.log(`Braces: ${braceCount} (should be 0)`);
  console.log(`Parentheses: ${parenCount} (should be 0)`);
  console.log(`Brackets: ${bracketCount} (should be 0)`);
  
  if (issues.length > 0) {
    console.log('\n🚨 Potential Issues Found:');
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. Line ${issue.line}: ${issue.issue}`);
      console.log(`   Content: ${issue.content}`);
    });
  }
  
  return { braceCount, parenCount, bracketCount, issues };
}

// Run the analysis
const filePath = 'src/utils/comprehensiveErrorHandler.tsx';
if (fs.existsSync(filePath)) {
  analyzeClassStructure(filePath);
} else {
  console.error(`File not found: ${filePath}`);
}
