# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2023-05-07 03:57+0000\n"
"Last-Translator: Lattefang <<EMAIL>>\n"
"Language-Team: Chinese (Simplified) <https://hosted.weblate.org/projects/"
"django-filter/django-filter/zh_Hans/>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.18-dev\n"

#: conf.py:16
msgid "date"
msgstr "日期"

#: conf.py:17
msgid "year"
msgstr "年"

#: conf.py:18
msgid "month"
msgstr "月"

#: conf.py:19
msgid "day"
msgstr "日"

#: conf.py:20
msgid "week day"
msgstr "工作日"

#: conf.py:21
msgid "hour"
msgstr "小时"

#: conf.py:22
msgid "minute"
msgstr "分钟"

#: conf.py:23
msgid "second"
msgstr "秒"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "包含"

#: conf.py:29
msgid "is in"
msgstr "在"

#: conf.py:30
msgid "is greater than"
msgstr "大于"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "大于等于"

#: conf.py:32
msgid "is less than"
msgstr "小于"

#: conf.py:33
msgid "is less than or equal to"
msgstr "小于等于"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "以……开始"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "以……结尾"

#: conf.py:38
msgid "is in range"
msgstr "在范围内"

#: conf.py:39
msgid "is null"
msgstr "为空"

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "匹配正则表达式"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "搜索"

#: conf.py:44
msgid "is contained by"
msgstr "包含在"

#: conf.py:45
msgid "overlaps"
msgstr "重叠"

#: conf.py:46
msgid "has key"
msgstr "单值"

#: conf.py:47
msgid "has keys"
msgstr "多值"

#: conf.py:48
msgid "has any keys"
msgstr "任何值"

#: fields.py:94
msgid "Select a lookup."
msgstr "选择查找。"

#: fields.py:198
msgid "Range query expects two values."
msgstr "范围查询需要两个值。"

#: filters.py:437
msgid "Today"
msgstr "今日"

#: filters.py:438
msgid "Yesterday"
msgstr "昨日"

#: filters.py:439
msgid "Past 7 days"
msgstr "过去 7 日"

#: filters.py:440
msgid "This month"
msgstr "本月"

#: filters.py:441
msgid "This year"
msgstr "今年"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "多个值可以用逗号分隔。"

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s（降序）"

#: filters.py:737
msgid "Ordering"
msgstr "排序"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr "提交"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr "字段过滤器"

#: utils.py:308
msgid "exclude"
msgstr "排除"

#: widgets.py:58
msgid "All"
msgstr "全部"

#: widgets.py:162
msgid "Unknown"
msgstr "未知"

#: widgets.py:162
msgid "Yes"
msgstr "是"

#: widgets.py:162
msgid "No"
msgstr "否"

#~ msgid "This is an exclusion filter"
#~ msgstr "未启用该过滤器"
