Metadata-Version: 2.1
Name: django-ratelimit
Version: 4.1.0
Summary: Cache-based rate-limiting for Django.
Author-email: <PERSON> <<EMAIL>>
License: Copyright (c) 2022, <PERSON>
        
        Licensed under the Apache License, Version 2.0 (the "License");
        you may not use this file except in compliance with the License.
        You may obtain a copy of the License at
        
            http://www.apache.org/licenses/LICENSE-2.0
        
        Unless required by applicable law or agreed to in writing, software
        distributed under the License is distributed on an "AS IS" BASIS,
        WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
        See the License for the specific language governing permissions and
        limitations under the License.
        
Project-URL: Homepage, https://github.com/jsocol/django-ratelimit
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Framework :: Django
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: LICENSE

================
Django Ratelimit
================

Django Ratelimit provides a decorator to rate-limit views. Limiting can
be based on IP address or a field in the request--either a GET or POST
variable.

.. image:: https://github.com/jsocol/django-ratelimit/workflows/test/badge.svg?branch=main
   :target: https://github.com/jsocol/django-ratelimit/actions

:Code:          https://github.com/jsocol/django-ratelimit
:License:       Apache Software License 2.0; see LICENSE file
:Issues:        https://github.com/jsocol/django-ratelimit/issues
:Documentation: http://django-ratelimit.readthedocs.io/
