# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-28 15:09-0300\n"
"Last-Translator: rene <<EMAIL>>\n"
"Language: de_CH\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Der Authorizationheader muss zwei leerzeichen-getrennte Werte enthalten"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Der Token ist für keinen Tokentyp gültig"

#: authentication.py:128 authentication.py:166
msgid "Token contained no recognizable user identification"
msgstr "Token enthält keine erkennbare Benutzeridentifikation"

#: authentication.py:135
msgid "User not found"
msgstr "Benutzer nicht gefunden"

#: authentication.py:139
msgid "User is inactive"
msgstr "Inaktiver Benutzer"

#: authentication.py:146
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Unerkannter Algorithmustyp '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:125 backends.py:177 tokens.py:69
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Ungültiger oder abgelaufener Token"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr ""

#: backends.py:175 tokens.py:67
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Ungültiger oder abgelaufener Token"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Ungültiger oder abgelaufener Token"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Kein aktiver Account mit diesen Zugangsdaten gefunden"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Kein aktiver Account mit diesen Zugangsdaten gefunden"

#: serializers.py:178 tokens.py:280
msgid "Token is blacklisted"
msgstr "Token steht auf der Blacklist"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Die Einstellung '{}' wurde gelöscht. Bitte beachte '{}' für verfügbare "
"Einstellungen."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "Benutzer"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "erstellt am"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "läuft ab am"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Blacklist"

#: token_blacklist/models.py:19
msgid "Outstanding Token"
msgstr ""

#: token_blacklist/models.py:20
msgid "Outstanding Tokens"
msgstr ""

#: token_blacklist/models.py:32
#, python-format
msgid "Token for %(user)s (%(jti)s)"
msgstr ""

#: token_blacklist/models.py:45
msgid "Blacklisted Token"
msgstr ""

#: token_blacklist/models.py:46
msgid "Blacklisted Tokens"
msgstr ""

#: token_blacklist/models.py:57
#, python-format
msgid "Blacklisted token for %(user)s"
msgstr ""

#: tokens.py:53
msgid "Cannot create token with no type or lifetime"
msgstr "Ein Token ohne Typ oder Lebensdauer kann nicht erstellt werden"

#: tokens.py:127
msgid "Token has no id"
msgstr "Token hat keine Id"

#: tokens.py:139
msgid "Token has no type"
msgstr "Token hat keinen Typ"

#: tokens.py:142
msgid "Token has wrong type"
msgstr "Token hat den falschen Typ"

#: tokens.py:201
msgid "Token has no '{}' claim"
msgstr "Token hat kein '{}' Recht"

#: tokens.py:206
msgid "Token '{}' claim has expired"
msgstr "Das Tokenrecht '{}' ist abgelaufen"
