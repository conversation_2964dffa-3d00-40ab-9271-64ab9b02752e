# This file is distributed under the same license as the PACKAGE package.
# FIRST AUTHOR <PERSON> <danie<PERSON><PERSON><EMAIL>>, 2022.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-28 15:09-0300\n"
"Last-Translator: <PERSON> <daniel<PERSON><EMAIL>>\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Header-ul(antetul) de autorizare trebuie să conțină două valori separate "
"prin spațiu"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Tokenul dat nu este valid pentru niciun tip de token"

#: authentication.py:128 authentication.py:166
msgid "Token contained no recognizable user identification"
msgstr "Tokenul nu conține date de identificare a utilizatorului"

#: authentication.py:135
msgid "User not found"
msgstr "Utilizatorul nu a fost găsit"

#: authentication.py:139
msgid "User is inactive"
msgstr "Utilizatorul este inactiv"

#: authentication.py:146
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "Tipul de algoritm '{}' nu este recunoscut"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "Trebuie să aveți instalată criptografia pentru a utiliza {}."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""
"Tipul '{}' nu este recunoscut, 'leeway' trebuie să fie de tip int, float sau "
"timedelta."

#: backends.py:125 backends.py:177 tokens.py:69
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "Token nu este valid sau a expirat"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "Algoritm nevalid specificat"

#: backends.py:175 tokens.py:67
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "Token nu este valid sau a expirat"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "Token nu este valid sau a expirat"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Nu a fost găsit cont activ cu aceste date de autentificare"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "Nu a fost găsit cont activ cu aceste date de autentificare"

#: serializers.py:178 tokens.py:280
msgid "Token is blacklisted"
msgstr "Tokenul este în listă de tokenuri blocate"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Setarea '{}' a fost ștearsă. Referați la '{}' pentru setări disponibile."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "utilizator"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "creat la"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "expiră la"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Listă de token-uri blocate"

#: token_blacklist/models.py:19
msgid "Outstanding Token"
msgstr ""

#: token_blacklist/models.py:20
msgid "Outstanding Tokens"
msgstr ""

#: token_blacklist/models.py:32
#, python-format
msgid "Token for %(user)s (%(jti)s)"
msgstr ""

#: token_blacklist/models.py:45
msgid "Blacklisted Token"
msgstr ""

#: token_blacklist/models.py:46
msgid "Blacklisted Tokens"
msgstr ""

#: token_blacklist/models.py:57
#, python-format
msgid "Blacklisted token for %(user)s"
msgstr ""

#: tokens.py:53
msgid "Cannot create token with no type or lifetime"
msgstr "Nu se poate crea token fără tip sau durată de viață"

#: tokens.py:127
msgid "Token has no id"
msgstr "Tokenul nu are id"

#: tokens.py:139
msgid "Token has no type"
msgstr "Tokenul nu are tip"

#: tokens.py:142
msgid "Token has wrong type"
msgstr "Tokenul are tipul greșit"

#: tokens.py:201
msgid "Token has no '{}' claim"
msgstr "Tokenul nu are reclamația '{}'"

#: tokens.py:206
msgid "Token '{}' claim has expired"
msgstr "Reclamația tokenului '{}' a expirat"
