# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2019.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-28 15:09-0300\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr "يجب أن يحتوي رأس التفويض على قيمتين مفصولتين بمسافات"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "تأشيرة المرور غير صالحة لأي نوع من أنواع التأشيرات"

#: authentication.py:128 authentication.py:166
msgid "Token contained no recognizable user identification"
msgstr "لا تحتوي تأشيرة المرور على هوية مستخدم يمكن التعرف عليها"

#: authentication.py:135
msgid "User not found"
msgstr "لم يتم العثور على المستخدم"

#: authentication.py:139
msgid "User is inactive"
msgstr "الحساب غير مفعل"

#: authentication.py:146
msgid "The user's password has been changed."
msgstr ""

#: backends.py:90
msgid "Unrecognized algorithm type '{}'"
msgstr "نوع الخوارزمية غير معروف '{}'"

#: backends.py:96
msgid "You must have cryptography installed to use {}."
msgstr "يجب أن يكون لديك تشفير مثبت لاستخدام {}."

#: backends.py:111
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""
"نوع غير معروف '{}'. يجب أن تكون 'leeway' عددًا صحيحًا أو عددًا نسبيًا أو فرق وقت."

#: backends.py:125 backends.py:177 tokens.py:69
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is invalid"
msgstr "تأشيرة المرور غير صالحة أو منتهية الصلاحية"

#: backends.py:173
msgid "Invalid algorithm specified"
msgstr "تم تحديد خوارزمية غير صالحة"

#: backends.py:175 tokens.py:67
#, fuzzy
#| msgid "Token is invalid or expired"
msgid "Token is expired"
msgstr "تأشيرة المرور غير صالحة أو منتهية الصلاحية"

#: exceptions.py:55
msgid "Token is invalid or expired"
msgstr "تأشيرة المرور غير صالحة أو منتهية الصلاحية"

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "لم يتم العثور على حساب نشط للبيانات المقدمة"

#: serializers.py:108
#, fuzzy
#| msgid "No active account found with the given credentials"
msgid "No active account found for the given token."
msgstr "لم يتم العثور على حساب نشط للبيانات المقدمة"

#: serializers.py:178 tokens.py:280
msgid "Token is blacklisted"
msgstr "التأشيرة مدرجة في القائمة السوداء"

#: settings.py:74
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"تمت إزالة الإعداد '{}'. يرجى الرجوع إلى '{}' للتعرف على الإعدادات المتاحة."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "المستخدم"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "أنشئت في"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "تنتهي في"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "قائمة تأشيرات المرور السوداء"

#: token_blacklist/models.py:19
msgid "Outstanding Token"
msgstr ""

#: token_blacklist/models.py:20
msgid "Outstanding Tokens"
msgstr ""

#: token_blacklist/models.py:32
#, python-format
msgid "Token for %(user)s (%(jti)s)"
msgstr ""

#: token_blacklist/models.py:45
msgid "Blacklisted Token"
msgstr ""

#: token_blacklist/models.py:46
msgid "Blacklisted Tokens"
msgstr ""

#: token_blacklist/models.py:57
#, python-format
msgid "Blacklisted token for %(user)s"
msgstr ""

#: tokens.py:53
msgid "Cannot create token with no type or lifetime"
msgstr "لا يمكن إنشاء تأشيرة مرور بدون نوع أو عمر"

#: tokens.py:127
msgid "Token has no id"
msgstr "التأشيرة ليس لها معرف"

#: tokens.py:139
msgid "Token has no type"
msgstr "التأشيرة ليس لها نوع"

#: tokens.py:142
msgid "Token has wrong type"
msgstr "التأشيرة لها نوع خاطئ"

#: tokens.py:201
msgid "Token has no '{}' claim"
msgstr "التأشيرة ليس لديها مطالبة '{}'"

#: tokens.py:206
msgid "Token '{}' claim has expired"
msgstr "انتهى عمر المطالبة بالتأشيرة '{}'"
